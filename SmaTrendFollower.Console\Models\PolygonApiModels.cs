using System.Text.Json.Serialization;

namespace SmaTrendFollower.Models;

/// <summary>
/// Response model for Polygon /v3/reference/tickers endpoint
/// </summary>
public class PolygonTickersResponse
{
    [JsonPropertyName("results")]
    public List<PolygonTickerResult> Results { get; set; } = new();

    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty;

    [JsonPropertyName("request_id")]
    public string RequestId { get; set; } = string.Empty;

    [JsonPropertyName("count")]
    public int Count { get; set; }

    [JsonPropertyName("next_url")]
    public string? NextUrl { get; set; }
}

/// <summary>
/// Individual ticker result from Polygon API
/// </summary>
public class PolygonTickerResult
{
    [JsonPropertyName("ticker")]
    public string Ticker { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("market")]
    public string Market { get; set; } = string.Empty;

    [JsonPropertyName("locale")]
    public string Locale { get; set; } = string.Empty;

    [JsonPropertyName("primary_exchange")]
    public string PrimaryExchange { get; set; } = string.Empty;

    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    [JsonPropertyName("active")]
    public bool Active { get; set; }

    [JsonPropertyName("currency_name")]
    public string CurrencyName { get; set; } = string.Empty;

    [JsonPropertyName("cik")]
    public string? Cik { get; set; }

    [JsonPropertyName("composite_figi")]
    public string? CompositeFigi { get; set; }

    [JsonPropertyName("share_class_figi")]
    public string? ShareClassFigi { get; set; }

    [JsonPropertyName("last_updated_utc")]
    public DateTime? LastUpdatedUtc { get; set; }
}

/// <summary>
/// Response model for Polygon /v2/snapshot/locale/us/markets/stocks/tickers endpoint
/// </summary>
public class PolygonSnapshotResponse
{
    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty;

    [JsonPropertyName("request_id")]
    public string RequestId { get; set; } = string.Empty;

    [JsonPropertyName("count")]
    public int Count { get; set; }

    [JsonPropertyName("results")]
    public List<PolygonSnapshotTicker> Results { get; set; } = new();
}

/// <summary>
/// Individual ticker snapshot from Polygon API
/// </summary>
public class PolygonSnapshotTicker
{
    [JsonPropertyName("ticker")]
    public string Ticker { get; set; } = string.Empty;

    [JsonPropertyName("todaysChangePerc")]
    public decimal? TodaysChangePerc { get; set; }

    [JsonPropertyName("todaysChange")]
    public decimal? TodaysChange { get; set; }

    [JsonPropertyName("updated")]
    public long? Updated { get; set; }

    [JsonPropertyName("timeframe")]
    public string? Timeframe { get; set; }

    [JsonPropertyName("quote")]
    public PolygonQuote? Quote { get; set; }

    [JsonPropertyName("lastQuote")]
    public PolygonQuote? LastQuote { get; set; }

    [JsonPropertyName("lastTrade")]
    public PolygonTrade? LastTrade { get; set; }

    [JsonPropertyName("min")]
    public PolygonBar? Min { get; set; }

    [JsonPropertyName("prevDay")]
    public PolygonBar? PrevDay { get; set; }
}

/// <summary>
/// Quote data from Polygon API
/// </summary>
public class PolygonQuote
{
    [JsonPropertyName("ask")]
    public decimal? Ask { get; set; }

    [JsonPropertyName("askSize")]
    public long? AskSize { get; set; }

    [JsonPropertyName("bid")]
    public decimal? Bid { get; set; }

    [JsonPropertyName("bidSize")]
    public long? BidSize { get; set; }

    [JsonPropertyName("last_updated")]
    public long? LastUpdated { get; set; }

    [JsonPropertyName("timeframe")]
    public string? Timeframe { get; set; }
}

/// <summary>
/// Trade data from Polygon API
/// </summary>
public class PolygonTrade
{
    [JsonPropertyName("conditions")]
    public List<int>? Conditions { get; set; }

    [JsonPropertyName("exchange")]
    public int? Exchange { get; set; }

    [JsonPropertyName("price")]
    public decimal? Price { get; set; }

    [JsonPropertyName("sip_timestamp")]
    public long? SipTimestamp { get; set; }

    [JsonPropertyName("size")]
    public long? Size { get; set; }

    [JsonPropertyName("timeframe")]
    public string? Timeframe { get; set; }
}

/// <summary>
/// Bar data from Polygon API
/// </summary>
public class PolygonBar
{
    [JsonPropertyName("c")]
    public decimal? Close { get; set; }

    [JsonPropertyName("h")]
    public decimal? High { get; set; }

    [JsonPropertyName("l")]
    public decimal? Low { get; set; }

    [JsonPropertyName("o")]
    public decimal? Open { get; set; }

    [JsonPropertyName("t")]
    public long? Timestamp { get; set; }

    [JsonPropertyName("v")]
    public long? Volume { get; set; }

    [JsonPropertyName("vw")]
    public decimal? VolumeWeightedAveragePrice { get; set; }

    [JsonPropertyName("n")]
    public int? NumberOfTransactions { get; set; }
}

/// <summary>
/// Response model for Polygon aggregates endpoint
/// </summary>
public class PolygonAggregatesResponse
{
    [JsonPropertyName("ticker")]
    public string Ticker { get; set; } = string.Empty;

    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty;

    [JsonPropertyName("request_id")]
    public string RequestId { get; set; } = string.Empty;

    [JsonPropertyName("count")]
    public int Count { get; set; }

    [JsonPropertyName("results")]
    public List<PolygonBar> Results { get; set; } = new();

    [JsonPropertyName("adjusted")]
    public bool Adjusted { get; set; }

    [JsonPropertyName("next_url")]
    public string? NextUrl { get; set; }

    [JsonPropertyName("resultsCount")]
    public int ResultsCount { get; set; }
}

/// <summary>
/// Configuration for Polygon symbol universe fetching
/// </summary>
public class PolygonUniverseConfig
{
    /// <summary>
    /// Maximum number of symbols to fetch per API call
    /// </summary>
    public int PageSize { get; set; } = 1000;

    /// <summary>
    /// Maximum total symbols to fetch (0 = no limit)
    /// </summary>
    public int MaxSymbols { get; set; } = 0;

    /// <summary>
    /// Markets to include (e.g., "stocks", "crypto", "fx")
    /// </summary>
    public List<string> IncludedMarkets { get; set; } = new() { "stocks" };

    /// <summary>
    /// Symbol types to include (e.g., "CS" for common stock)
    /// </summary>
    public List<string> IncludedTypes { get; set; } = new() { "CS", "ETF" };

    /// <summary>
    /// Whether to include only active symbols
    /// </summary>
    public bool ActiveOnly { get; set; } = true;

    /// <summary>
    /// Delay between API calls (milliseconds)
    /// </summary>
    public int DelayBetweenCalls { get; set; } = 200;

    /// <summary>
    /// Cache TTL for symbol list (hours)
    /// </summary>
    public int CacheTtlHours { get; set; } = 168; // 1 week

    /// <summary>
    /// Default configuration for production use
    /// </summary>
    public static PolygonUniverseConfig Default => new()
    {
        PageSize = 1000,
        MaxSymbols = 0, // No limit
        IncludedMarkets = new() { "stocks" },
        IncludedTypes = new() { "CS", "ETF" },
        ActiveOnly = true,
        DelayBetweenCalls = 200,
        CacheTtlHours = 168
    };
}
