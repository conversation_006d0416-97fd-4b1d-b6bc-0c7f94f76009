using SmaTrendFollower.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

public class MarketSessionGuardTests
{
    private readonly Mock<ITimeProvider> _mockTimeProvider;
    private readonly Mock<ILogger<MarketSessionGuard>> _mockLogger;
    private readonly MarketSessionGuard _guard;

    public MarketSessionGuardTests()
    {
        _mockTimeProvider = new Mock<ITimeProvider>();
        _mockLogger = new Mock<ILogger<MarketSessionGuard>>();
        _guard = new MarketSessionGuard(_mockTimeProvider.Object, _mockLogger.Object);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task CanTradeNowAsync_OnWeekday_ReturnsTrue()
    {
        // Arrange - Tuesday at 2 PM ET
        var tuesday = new DateTime(2024, 1, 2, 19, 0, 0, DateTimeKind.Utc); // 2 PM ET = 7 PM UTC
        _mockTimeProvider.Setup(x => x.UtcNow).Returns(tuesday);

        // Act
        var result = await _guard.CanTradeNowAsync();

        // Assert
        result.Should().BeTrue();
        _guard.Reason.Should().BeEmpty();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task CanTradeNowAsync_OnSaturday_ReturnsFalse()
    {
        // Arrange - Saturday
        var saturday = new DateTime(2024, 1, 6, 19, 0, 0, DateTimeKind.Utc);
        _mockTimeProvider.Setup(x => x.UtcNow).Returns(saturday);

        // Act
        var result = await _guard.CanTradeNowAsync();

        // Assert
        result.Should().BeFalse();
        _guard.Reason.Should().Be("Weekend - markets closed");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task CanTradeNowAsync_OnSunday_ReturnsFalse()
    {
        // Arrange - Sunday
        var sunday = new DateTime(2024, 1, 7, 19, 0, 0, DateTimeKind.Utc);
        _mockTimeProvider.Setup(x => x.UtcNow).Returns(sunday);

        // Act
        var result = await _guard.CanTradeNowAsync();

        // Assert
        result.Should().BeFalse();
        _guard.Reason.Should().Be("Weekend - markets closed");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Theory]
    [InlineData(DayOfWeek.Monday)]
    [InlineData(DayOfWeek.Tuesday)]
    [InlineData(DayOfWeek.Wednesday)]
    [InlineData(DayOfWeek.Thursday)]
    [InlineData(DayOfWeek.Friday)]
    public async Task CanTradeNowAsync_OnWeekdays_ReturnsTrue(DayOfWeek dayOfWeek)
    {
        // Arrange
        var baseDate = new DateTime(2024, 1, 1); // Monday
        var targetDate = baseDate.AddDays((int)dayOfWeek - (int)baseDate.DayOfWeek);
        var utcTime = new DateTime(targetDate.Year, targetDate.Month, targetDate.Day, 19, 0, 0, DateTimeKind.Utc);
        
        _mockTimeProvider.Setup(x => x.UtcNow).Returns(utcTime);

        // Act
        var result = await _guard.CanTradeNowAsync();

        // Assert
        result.Should().BeTrue();
        _guard.Reason.Should().BeEmpty();
    }
}
