{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "SmaTrendFollower.Services.IndexRegimeService": "Debug",
      "SmaTrendFollower.Services.VIXResolverService": "Debug",
      "SmaTrendFollower.Services.BreadthMonitorService": "Debug",
      "SmaTrendFollower.Services.RealTimeExecutionService": "Debug"
    }
  },

  // Phase 6: Real-Time Intelligence & Signal Architecture Configuration
  
  // Index Regime Service Configuration
  "IndexRegime": {
    // SPX momentum analysis period in minutes (default: 24 hours)
    "SpxMomentumPeriodMinutes": 1440,
    
    // VIX thresholds for regime classification
    "VixVolatileThreshold": 25.0,
    "VixPanicThreshold": 35.0,
    "VixEuphoricThreshold": 12.0,
    
    // SPX momentum thresholds (percentage)
    "SpxMomentumBullThreshold": 0.5,
    "SpxMomentumBearThreshold": -0.5,
    
    // Divergence detection threshold
    "DivergenceThreshold": 2.0,
    
    // Regime confirmation period in minutes
    "RegimeConfirmationMinutes": 15,
    
    // Cache settings
    "CacheExpiryMinutes": 5,
    
    // Real-time updates
    "EnableRealTimeUpdates": true
  },

  // VIX Resolver Service Configuration
  "VixResolver": {
    // Data freshness requirement (15 minutes for trading decisions)
    "FreshnessThreshold": "00:15:00",
    
    // Stale cache threshold (1 hour maximum for emergency fallback)
    "CacheStaleThreshold": "01:00:00",
    
    // Retry configuration
    "MaxRetryAttempts": 3,
    "RetryDelay": "00:00:05",
    
    // Feature toggles
    "EnableSyntheticCalculation": true,
    "EnableWebScraping": true,
    "AllowStaleCache": true,
    "HaltTradingOnFailure": true
  },

  // Breadth Monitor Service Configuration
  "BreadthMonitor": {
    // Update frequency in seconds
    "UpdateIntervalSeconds": 30,
    
    // Minimum universe size for reliable breadth analysis
    "MinUniverseSize": 100,
    
    // Extreme breadth threshold (0.9 = 90% advancing/declining)
    "ExtremeBreadthThreshold": 0.9,
    
    // Divergence detection threshold
    "DivergenceThreshold": 0.3,
    
    // Momentum lookback period in days
    "MomentumLookbackDays": 20,
    
    // Real-time features
    "EnableRealTimeUpdates": true,
    
    // Cache settings
    "CacheExpiryMinutes": 5
  },

  // Real-Time Execution Service Configuration
  "RealTimeExecution": {
    // Monitoring frequency in milliseconds
    "MonitoringIntervalMs": 1000,
    
    // Spread spike detection threshold (2.0 = 200% of normal spread)
    "SpreadSpikeThreshold": 2.0,
    
    // Minimum liquidity threshold for execution
    "LiquidityThreshold": 5000,
    
    // Maximum concurrent executions
    "MaxConcurrentExecutions": 10,
    
    // Default throttle duration when conditions are poor
    "DefaultThrottleDuration": "00:05:00",
    
    // Feature toggles
    "EnableAdaptiveExecution": true,
    "EnableSpreadMonitoring": true,
    "EnableVwapGuidance": true
  },

  // Redis Configuration for Phase 6 Services
  "Redis": {
    "ConnectionString": "192.168.1.168:6379",
    "Database": 0,
    "KeyPrefix": "sma:",
    
    // Phase 6 specific cache keys and TTL
    "CacheSettings": {
      "IndexRegime": {
        "KeyPattern": "index:regime:*",
        "DefaultTTL": "00:05:00"
      },
      "VixData": {
        "KeyPattern": "vix:*",
        "DefaultTTL": "00:15:00"
      },
      "BreadthAnalysis": {
        "KeyPattern": "breadth:*",
        "DefaultTTL": "00:05:00"
      },
      "ExecutionMetrics": {
        "KeyPattern": "execution:*",
        "DefaultTTL": "01:00:00"
      }
    }
  },

  // Market Data Configuration
  "MarketData": {
    // Data freshness requirements
    "MaxDataAge": "00:15:00",
    "StaleDataWarningThreshold": "00:10:00",
    
    // Index symbols for Phase 6 analysis
    "IndexSymbols": {
      "SPX": "SPX",
      "VIX": "VIX", 
      "NDX": "NDX"
    },
    
    // Rate limiting for Phase 6 services
    "RateLimits": {
      "IndexDataRequestsPerMinute": 60,
      "VixFallbackRequestsPerMinute": 30,
      "BreadthAnalysisRequestsPerMinute": 20
    }
  },

  // Trading Integration Configuration
  "Trading": {
    // Phase 6 trading filters
    "Filters": {
      "EnableIndexRegimeFilter": true,
      "EnableVixDataFreshnessFilter": true,
      "EnableBreadthFilter": true,
      "EnableExecutionConditionFilter": true
    },
    
    // Position sizing adjustments
    "PositionSizing": {
      "EnableBreadthAdjustment": true,
      "MaxBreadthAdjustment": 1.5,
      "MinBreadthAdjustment": 0.5
    },
    
    // Execution preferences
    "Execution": {
      "PreferLimitOrders": true,
      "MaxSpreadPercent": 0.5,
      "RequireVwapValidation": true
    }
  },

  // Monitoring and Alerting
  "Monitoring": {
    "Phase6Services": {
      "EnableHealthChecks": true,
      "HealthCheckInterval": "00:01:00",
      "AlertOnServiceFailure": true,
      "AlertOnDataStaleness": true,
      "AlertOnTradingHalt": true
    },
    
    // Performance thresholds
    "PerformanceThresholds": {
      "IndexRegimeAnalysisMaxTime": "00:00:00.500",
      "VixResolutionMaxTime": "00:00:02.000",
      "BreadthAnalysisMaxTime": "00:00:01.000",
      "ExecutionStrategyMaxTime": "00:00:00.200"
    }
  },

  // Development and Testing Configuration
  "Development": {
    "EnableMockData": false,
    "EnableDetailedLogging": true,
    "EnablePerformanceMetrics": true,
    "EnableEventTracing": true,
    
    // Test data configuration
    "TestData": {
      "MockVixValue": 18.5,
      "MockSpxPrice": 4500.0,
      "MockNdxPrice": 15000.0,
      "MockUniverseSize": 500
    }
  }
}
