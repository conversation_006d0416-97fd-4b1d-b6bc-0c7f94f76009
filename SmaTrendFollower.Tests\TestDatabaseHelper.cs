using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Data;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Tests;

/// <summary>
/// Helper class for managing test databases and ensuring proper initialization
/// </summary>
public static class TestDatabaseHelper
{
    /// <summary>
    /// Creates an in-memory IndexCacheDbContext for testing
    /// </summary>
    public static IndexCacheDbContext CreateInMemoryIndexCacheContext(string? databaseName = null)
    {
        var options = new DbContextOptionsBuilder<IndexCacheDbContext>()
            .UseInMemoryDatabase(databaseName ?? Guid.NewGuid().ToString())
            .EnableSensitiveDataLogging()
            .Options;

        var context = new IndexCacheDbContext(options);
        
        // Ensure database is created
        context.Database.EnsureCreated();
        
        return context;
    }

    /// <summary>
    /// Creates an in-memory StockBarCacheDbContext for testing
    /// </summary>
    public static StockBarCacheDbContext CreateInMemoryStockCacheContext(string? databaseName = null)
    {
        var options = new DbContextOptionsBuilder<StockBarCacheDbContext>()
            .UseInMemoryDatabase(databaseName ?? Guid.NewGuid().ToString())
            .EnableSensitiveDataLogging()
            .Options;

        var context = new StockBarCacheDbContext(options);
        
        // Ensure database is created
        context.Database.EnsureCreated();
        
        return context;
    }

    /// <summary>
    /// Creates a SQLite IndexCacheDbContext for integration testing
    /// </summary>
    public static IndexCacheDbContext CreateSqliteIndexCacheContext(string? databasePath = null)
    {
        var dbPath = databasePath ?? Path.Combine(Path.GetTempPath(), $"test_index_cache_{Guid.NewGuid()}.db");
        
        var options = new DbContextOptionsBuilder<IndexCacheDbContext>()
            .UseSqlite($"Data Source={dbPath}")
            .EnableSensitiveDataLogging()
            .Options;

        var context = new IndexCacheDbContext(options);
        
        // Ensure database is created and schema is applied
        context.Database.EnsureCreated();
        
        return context;
    }

    /// <summary>
    /// Creates a SQLite StockBarCacheDbContext for integration testing
    /// </summary>
    public static StockBarCacheDbContext CreateSqliteStockCacheContext(string? databasePath = null)
    {
        var dbPath = databasePath ?? Path.Combine(Path.GetTempPath(), $"test_stock_cache_{Guid.NewGuid()}.db");
        
        var options = new DbContextOptionsBuilder<StockBarCacheDbContext>()
            .UseSqlite($"Data Source={dbPath}")
            .EnableSensitiveDataLogging()
            .Options;

        var context = new StockBarCacheDbContext(options);
        
        // Ensure database is created and schema is applied
        context.Database.EnsureCreated();
        
        return context;
    }

    /// <summary>
    /// Configures database services for testing with in-memory databases
    /// </summary>
    public static void ConfigureTestDatabases(IServiceCollection services, string? testName = null)
    {
        var testId = testName ?? Guid.NewGuid().ToString();
        
        // Configure IndexCacheDbContext with in-memory database
        services.AddDbContext<IndexCacheDbContext>(options =>
            options.UseInMemoryDatabase($"TestIndexCache_{testId}")
                   .EnableSensitiveDataLogging());

        // Configure StockBarCacheDbContext with in-memory database
        services.AddDbContext<StockBarCacheDbContext>(options =>
            options.UseInMemoryDatabase($"TestStockCache_{testId}")
                   .EnableSensitiveDataLogging());
    }

    /// <summary>
    /// Configures database services for integration testing with SQLite databases
    /// </summary>
    public static void ConfigureIntegrationTestDatabases(IServiceCollection services, string? testName = null)
    {
        var testId = testName ?? Guid.NewGuid().ToString();
        var tempDir = Path.GetTempPath();
        
        // Configure IndexCacheDbContext with SQLite database
        var indexDbPath = Path.Combine(tempDir, $"integration_test_index_{testId}.db");
        services.AddDbContext<IndexCacheDbContext>(options =>
            options.UseSqlite($"Data Source={indexDbPath}")
                   .EnableSensitiveDataLogging());

        // Configure StockBarCacheDbContext with SQLite database
        var stockDbPath = Path.Combine(tempDir, $"integration_test_stock_{testId}.db");
        services.AddDbContext<StockBarCacheDbContext>(options =>
            options.UseSqlite($"Data Source={stockDbPath}")
                   .EnableSensitiveDataLogging());
    }

    /// <summary>
    /// Ensures all database contexts are properly initialized
    /// </summary>
    public static async Task EnsureDatabasesCreatedAsync(IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();

        // Try to use the database initialization service first
        var dbInitService = scope.ServiceProvider.GetService<IDatabaseInitializationService>();
        if (dbInitService != null)
        {
            await dbInitService.InitializeAllDatabasesAsync();
            return;
        }

        // Fallback to manual initialization
        var indexContext = scope.ServiceProvider.GetService<IndexCacheDbContext>();
        if (indexContext != null)
        {
            await indexContext.Database.EnsureCreatedAsync();
        }

        var stockContext = scope.ServiceProvider.GetService<StockBarCacheDbContext>();
        if (stockContext != null)
        {
            await stockContext.Database.EnsureCreatedAsync();
        }
    }

    /// <summary>
    /// Cleans up test databases
    /// </summary>
    public static async Task CleanupTestDatabasesAsync(IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        
        // Cleanup IndexCacheDbContext
        var indexContext = scope.ServiceProvider.GetService<IndexCacheDbContext>();
        if (indexContext != null)
        {
            await indexContext.Database.EnsureDeletedAsync();
        }

        // Cleanup StockBarCacheDbContext
        var stockContext = scope.ServiceProvider.GetService<StockBarCacheDbContext>();
        if (stockContext != null)
        {
            await stockContext.Database.EnsureDeletedAsync();
        }
    }
}
