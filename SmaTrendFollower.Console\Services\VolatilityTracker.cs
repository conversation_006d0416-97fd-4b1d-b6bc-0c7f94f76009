using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time volatility tracker for individual symbols
/// Maintains rolling window of price changes and calculates volatility metrics
/// </summary>
internal sealed class VolatilityTracker
{
    private readonly string _symbol;
    private readonly ILogger _logger;
    private VolatilityGuardConfig _config;
    
    private readonly ConcurrentQueue<VolatilityPricePoint> _pricePoints = new();
    private decimal _lastPrice;
    private DateTime _lastUpdate = DateTime.UtcNow;
    private readonly object _calculationLock = new();
    
    // Volatility calculation state
    private decimal _sumReturns;
    private decimal _sumSquaredReturns;
    private int _tickCount;
    
    public VolatilityTracker(string symbol, VolatilityGuardConfig config, ILogger logger)
    {
        _symbol = symbol ?? throw new ArgumentNullException(nameof(symbol));
        _config = config ?? throw new ArgumentNullException(nameof(config));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }
    
    public VolatilityMetrics? UpdateWithTrade(decimal price, DateTime timestamp)
    {
        lock (_calculationLock)
        {
            if (_lastPrice == 0)
            {
                _lastPrice = price;
                _lastUpdate = timestamp;
                return null;
            }
            
            // Calculate price return
            var priceReturn = Math.Log((double)(price / _lastPrice));
            
            // Add price point
            var pricePoint = new VolatilityPricePoint(price, (decimal)priceReturn, timestamp);
            _pricePoints.Enqueue(pricePoint);
            
            // Update running statistics
            _sumReturns += (decimal)priceReturn;
            _sumSquaredReturns += (decimal)(priceReturn * priceReturn);
            _tickCount++;
            
            _lastPrice = price;
            _lastUpdate = timestamp;
            
            // Remove old price points outside window
            CleanOldPricePoints(timestamp);
            
            return CalculateVolatilityMetrics(timestamp);
        }
    }
    
    public VolatilityMetrics? UpdateWithQuote(decimal midPrice, DateTime timestamp)
    {
        // Use mid-price for quote-based volatility
        return UpdateWithTrade(midPrice, timestamp);
    }
    
    public VolatilityMetrics? GetCurrentMetrics()
    {
        lock (_calculationLock)
        {
            return CalculateVolatilityMetrics(DateTime.UtcNow);
        }
    }
    
    public void UpdateConfig(VolatilityGuardConfig config)
    {
        _config = config;
    }
    
    public void InitializeFromCache(VolatilityMetrics cachedMetrics)
    {
        lock (_calculationLock)
        {
            _lastPrice = cachedMetrics.CurrentVolatility > 0 ? cachedMetrics.CurrentVolatility : _lastPrice;
            _tickCount = cachedMetrics.TickCount;
            _lastUpdate = cachedMetrics.LastUpdate;
        }
    }
    
    private VolatilityMetrics? CalculateVolatilityMetrics(DateTime timestamp)
    {
        if (_tickCount < _config.MinTicksRequired)
            return null;
            
        // Calculate realized volatility (annualized)
        var meanReturn = _sumReturns / _tickCount;
        var variance = (_sumSquaredReturns / _tickCount) - (meanReturn * meanReturn);
        var volatility = (decimal)Math.Sqrt((double)variance) * (decimal)Math.Sqrt(252 * 390); // Annualized (252 trading days, 390 minutes per day)
        
        // Calculate rolling average volatility for comparison
        var averageVolatility = CalculateAverageVolatility();
        
        // Calculate standard deviation of volatility
        var volatilityStdDev = CalculateVolatilityStandardDeviation(volatility, averageVolatility);
        
        // Calculate Z-score
        var zScore = volatilityStdDev > 0 ? (volatility - averageVolatility) / volatilityStdDev : 0;
        
        // Determine dynamic threshold
        var dynamicThreshold = _config.BaseStdDevThreshold;
        
        return new VolatilityMetrics(
            Symbol: _symbol,
            CurrentVolatility: volatility * 100, // Convert to percentage
            AverageVolatility: averageVolatility * 100,
            StandardDeviation: volatilityStdDev * 100,
            ZScore: zScore,
            DynamicThreshold: dynamicThreshold,
            TickCount: _tickCount,
            LastUpdate: timestamp,
            IsBlocked: false,
            BlockReason: null
        );
    }
    
    private void CleanOldPricePoints(DateTime currentTime)
    {
        var cutoffTime = currentTime.AddMinutes(-_config.VolatilityWindowMinutes);
        
        while (_pricePoints.TryPeek(out var oldestPoint) && oldestPoint.Timestamp < cutoffTime)
        {
            if (_pricePoints.TryDequeue(out var removedPoint))
            {
                _sumReturns -= removedPoint.Return;
                _sumSquaredReturns -= removedPoint.Return * removedPoint.Return;
                _tickCount--;
            }
        }
        
        // Ensure we don't go negative
        if (_tickCount < 0) _tickCount = 0;
        if (_sumReturns < 0 && _tickCount == 0) _sumReturns = 0;
        if (_sumSquaredReturns < 0 && _tickCount == 0) _sumSquaredReturns = 0;
    }
    
    private decimal CalculateAverageVolatility()
    {
        // Simple moving average of recent volatility
        var recentPoints = _pricePoints.TakeLast(Math.Min(_tickCount, 100)).ToList();
        if (recentPoints.Count < 10)
            return 0;
            
        var returns = recentPoints.Select(p => p.Return).ToList();
        var meanReturn = returns.Average();
        var variance = returns.Select(r => (r - meanReturn) * (r - meanReturn)).Average();
        
        return (decimal)Math.Sqrt((double)variance) * (decimal)Math.Sqrt(252 * 390);
    }
    
    private decimal CalculateVolatilityStandardDeviation(decimal currentVolatility, decimal averageVolatility)
    {
        // Calculate standard deviation of volatility over the window
        var recentPoints = _pricePoints.TakeLast(Math.Min(_tickCount, 100)).ToList();
        if (recentPoints.Count < 10)
            return 0.01m; // Default small value
            
        var volatilities = new List<decimal>();
        
        // Calculate volatility for sub-windows
        for (int i = 10; i <= recentPoints.Count; i += 5)
        {
            var subWindow = recentPoints.Take(i).ToList();
            var returns = subWindow.Select(p => p.Return).ToList();
            var meanReturn = returns.Average();
            var variance = returns.Select(r => (r - meanReturn) * (r - meanReturn)).Average();
            var subVolatility = (decimal)Math.Sqrt((double)variance) * (decimal)Math.Sqrt(252 * 390);
            volatilities.Add(subVolatility);
        }
        
        if (volatilities.Count < 2)
            return 0.01m;
            
        var meanVolatility = volatilities.Average();
        var volatilityVariance = volatilities.Select(v => (v - meanVolatility) * (v - meanVolatility)).Average();
        
        return (decimal)Math.Sqrt((double)volatilityVariance);
    }
}

/// <summary>
/// Internal price point structure for volatility calculation
/// </summary>
internal readonly record struct VolatilityPricePoint(
    decimal Price,
    decimal Return,
    DateTime Timestamp
);

/// <summary>
/// Trading block information
/// </summary>
internal readonly record struct TradingBlock(
    string Symbol,
    string Reason,
    DateTime BlockedAt,
    DateTime ExpiresAt
);
