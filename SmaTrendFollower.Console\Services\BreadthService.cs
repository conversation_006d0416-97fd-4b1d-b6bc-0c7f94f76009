using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using StackExchange.Redis;
using System.Collections.Concurrent;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Market breadth analysis service implementation
/// Provides real-time market breadth metrics for macro market awareness
/// </summary>
public sealed class BreadthService : IBreadthService, IDisposable
{
    private readonly IDynamicUniverseProvider _universeProvider;
    private readonly IMarketDataService _marketDataService;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly IMarketRegimeService _regimeService;
    private readonly ILogger<BreadthService> _logger;
    private readonly BreadthServiceConfig _config;
    
    private readonly SemaphoreSlim _analysisLock = new(1, 1);
    private MarketBreadthAnalysis? _cachedAnalysis;
    private MarketRiskSentiment _lastSentiment = MarketRiskSentiment.Neutral;
    private bool _disposed;

    // Redis key patterns
    private const string BreadthCacheKey = "breadth:analysis";
    private const string AdvanceDeclineCacheKey = "breadth:advance_decline";
    private const string MovingAverageCacheKey = "breadth:moving_averages";
    private const string NewHighsLowsCacheKey = "breadth:new_highs_lows";

    public BreadthService(
        IDynamicUniverseProvider universeProvider,
        IMarketDataService marketDataService,
        IOptimizedRedisConnectionService redisService,
        IMarketRegimeService regimeService,
        IConfiguration configuration,
        ILogger<BreadthService> logger)
    {
        _universeProvider = universeProvider ?? throw new ArgumentNullException(nameof(universeProvider));
        _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
        _redisService = redisService ?? throw new ArgumentNullException(nameof(redisService));
        _regimeService = regimeService ?? throw new ArgumentNullException(nameof(regimeService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _config = new BreadthServiceConfig();
        configuration.GetSection("BreadthService").Bind(_config);
    }

    // === Events ===
    
    public event EventHandler<BreadthAnalysisEventArgs>? BreadthAnalysisCompleted;
    public event EventHandler<MarketSentimentChangeEventArgs>? SentimentChanged;

    // === Public Methods ===

    public async Task<MarketBreadthAnalysis> CalculateMarketBreadthAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(BreadthService));

        await _analysisLock.WaitAsync(cancellationToken);
        try
        {
            // Check cache first
            var cached = await GetCachedBreadthAsync();
            if (cached.HasValue && IsAnalysisValid(cached.Value))
            {
                _logger.LogDebug("Using cached market breadth analysis");
                return cached.Value;
            }

            _logger.LogInformation("Calculating market breadth analysis...");

            // Get universe symbols
            var universe = await _universeProvider.GetCachedUniverseAsync(cancellationToken);
            var symbols = universe.Take(_config.MaxSymbolsToAnalyze).ToList();

            _logger.LogDebug("Analyzing breadth for {Count} symbols", symbols.Count);

            // Calculate all breadth metrics in parallel
            var advanceDeclineTask = CalculateAdvanceDeclineAsync(cancellationToken);
            var movingAverageTask = CalculateMovingAverageBreadthAsync(cancellationToken);
            var newHighsLowsTask = CalculateNewHighsLowsAsync(cancellationToken);

            await Task.WhenAll(advanceDeclineTask, movingAverageTask, newHighsLowsTask);

            var advanceDecline = await advanceDeclineTask;
            var movingAverages = await movingAverageTask;
            var newHighsLows = await newHighsLowsTask;

            // Calculate overall breadth score
            var breadthScore = CalculateBreadthScore(advanceDecline, movingAverages, newHighsLows);
            
            // Determine sentiment
            var sentiment = DetermineSentiment(breadthScore);
            
            // Generate analysis text
            var analysis = GenerateAnalysisText(advanceDecline, movingAverages, newHighsLows, breadthScore, sentiment);

            var breadthAnalysis = new MarketBreadthAnalysis(
                DateTime.UtcNow,
                sentiment,
                advanceDecline,
                movingAverages,
                newHighsLows,
                breadthScore,
                analysis
            );

            // Cache the result
            await CacheBreadthAnalysisAsync(breadthAnalysis);
            
            // Check for sentiment changes
            if (sentiment != _lastSentiment)
            {
                var changeReason = GetSentimentChangeReason(sentiment, breadthScore);
                SentimentChanged?.Invoke(this, new MarketSentimentChangeEventArgs
                {
                    PreviousSentiment = _lastSentiment,
                    CurrentSentiment = sentiment,
                    ChangeTime = DateTime.UtcNow,
                    Reason = changeReason
                });
                
                _lastSentiment = sentiment;
                _logger.LogInformation("Market sentiment changed to {Sentiment}: {Reason}", sentiment, changeReason);
            }

            // Fire analysis completed event
            BreadthAnalysisCompleted?.Invoke(this, new BreadthAnalysisEventArgs { Analysis = breadthAnalysis });

            _logger.LogInformation("Market breadth analysis completed: Sentiment={Sentiment}, Score={Score:F1}", 
                sentiment, breadthScore);

            return breadthAnalysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating market breadth analysis");
            
            // Return safe default
            return new MarketBreadthAnalysis(
                DateTime.UtcNow,
                MarketRiskSentiment.Neutral,
                new AdvanceDeclineMetrics(0, 0, 0, 1.0m, 0m),
                new MovingAverageBreadth(0m, 0m, 0m, 0, 0, 0, 0),
                new NewHighsLowsMetrics(0, 0, 0, 0, 1.0m, 0m),
                50m,
                $"Analysis error: {ex.Message}"
            );
        }
        finally
        {
            _analysisLock.Release();
        }
    }

    public async Task<MarketBreadthAnalysis?> GetCachedBreadthAsync()
    {
        try
        {
            // Check in-memory cache first
            if (_cachedAnalysis.HasValue && IsAnalysisValid(_cachedAnalysis.Value))
            {
                return _cachedAnalysis.Value;
            }

            // Check Redis cache
            var database = await _redisService.GetDatabaseAsync();
            var value = await database.StringGetAsync(BreadthCacheKey);
            
            if (value.HasValue)
            {
                var analysis = JsonSerializer.Deserialize<MarketBreadthAnalysis>(value!);
                if (IsAnalysisValid(analysis))
                {
                    _cachedAnalysis = analysis;
                    return analysis;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error retrieving cached breadth analysis");
        }

        return null;
    }

    public async Task<MarketRiskSentiment> GetMarketSentimentAsync(CancellationToken cancellationToken = default)
    {
        var analysis = await CalculateMarketBreadthAsync(cancellationToken);
        return analysis.Sentiment;
    }

    public async Task<AdvanceDeclineMetrics> CalculateAdvanceDeclineAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Get universe symbols
            var universe = await _universeProvider.GetCachedUniverseAsync(cancellationToken);
            var symbols = universe.Take(_config.MaxSymbolsToAnalyze).ToList();

            var advancers = 0;
            var decliners = 0;
            var unchanged = 0;

            // Process symbols in batches for better performance
            var batchSize = 50;
            var batches = symbols.Chunk(batchSize);

            foreach (var batch in batches)
            {
                var tasks = batch.Select(async symbol =>
                {
                    try
                    {
                        var endDate = DateTime.Today;
                        var startDate = endDate.AddDays(-5); // Get a few days to ensure we have 2 bars
                        var barsPage = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                        var bars = barsPage.Items.ToList();
                        if (bars.Count >= 2)
                        {
                            var currentClose = bars[^1].Close;
                            var previousClose = bars[^2].Close;
                            var changePercent = ((currentClose - previousClose) / previousClose) * 100m;

                            if (Math.Abs(changePercent) >= _config.MinimumChangePercent)
                            {
                                return changePercent > 0 ? 1 : -1; // 1 for advance, -1 for decline
                            }
                        }
                        return 0; // unchanged
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error calculating change for {Symbol}", symbol);
                        return 0;
                    }
                });

                var results = await Task.WhenAll(tasks);
                
                advancers += results.Count(r => r > 0);
                decliners += results.Count(r => r < 0);
                unchanged += results.Count(r => r == 0);
            }

            var total = advancers + decliners;
            var ratio = decliners > 0 ? (decimal)advancers / decliners : advancers > 0 ? 10m : 1m;
            var advancePercent = total > 0 ? ((decimal)advancers / total) * 100m : 50m;

            var metrics = new AdvanceDeclineMetrics(
                advancers,
                decliners,
                unchanged,
                ratio,
                advancePercent
            );

            // Cache the result
            await CacheAdvanceDeclineAsync(metrics);

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating advance/decline metrics");
            return new AdvanceDeclineMetrics(0, 0, 0, 1.0m, 50m);
        }
    }

    public async Task<MovingAverageBreadth> CalculateMovingAverageBreadthAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Get universe symbols
            var universe = await _universeProvider.GetCachedUniverseAsync(cancellationToken);
            var symbols = universe.Take(_config.MaxSymbolsToAnalyze).ToList();

            var above20Sma = 0;
            var above50Sma = 0;
            var above200Sma = 0;
            var totalAnalyzed = 0;

            // Process symbols in batches
            var batchSize = 50;
            var batches = symbols.Chunk(batchSize);

            foreach (var batch in batches)
            {
                var tasks = batch.Select(async symbol =>
                {
                    try
                    {
                        // Get enough bars for 200-day SMA calculation
                        var endDate = DateTime.Today;
                        var startDate = endDate.AddDays(-300); // Get more days to ensure we have 250 bars
                        var barsPage = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                        var bars = barsPage.Items.ToList();
                        if (bars.Count < 200)
                            return (false, false, false, false); // Not enough data

                        var currentPrice = bars[^1].Close;

                        // Calculate SMAs
                        var sma20 = bars.TakeLast(20).Average(b => b.Close);
                        var sma50 = bars.TakeLast(50).Average(b => b.Close);
                        var sma200 = bars.TakeLast(200).Average(b => b.Close);

                        return (
                            true, // hasData
                            currentPrice > sma20,
                            currentPrice > sma50,
                            currentPrice > sma200
                        );
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error calculating SMAs for {Symbol}", symbol);
                        return (false, false, false, false);
                    }
                });

                var results = await Task.WhenAll(tasks);

                foreach (var result in results)
                {
                    var (hasData, isAbove20, isAbove50, isAbove200) = result;
                {
                    if (hasData)
                    {
                        totalAnalyzed++;
                        if (isAbove20) above20Sma++;
                        if (isAbove50) above50Sma++;
                        if (isAbove200) above200Sma++;
                    }
                }
            }
            }

            var percent20 = totalAnalyzed > 0 ? ((decimal)above20Sma / totalAnalyzed) * 100m : 0m;
            var percent50 = totalAnalyzed > 0 ? ((decimal)above50Sma / totalAnalyzed) * 100m : 0m;
            var percent200 = totalAnalyzed > 0 ? ((decimal)above200Sma / totalAnalyzed) * 100m : 0m;

            var breadth = new MovingAverageBreadth(
                percent20,
                percent50,
                percent200,
                totalAnalyzed,
                above20Sma,
                above50Sma,
                above200Sma
            );

            // Cache the result
            await CacheMovingAverageBreadthAsync(breadth);

            return breadth;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating moving average breadth");
            return new MovingAverageBreadth(0m, 0m, 0m, 0, 0, 0, 0);
        }
    }

    public async Task<NewHighsLowsMetrics> CalculateNewHighsLowsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Get universe symbols
            var universe = await _universeProvider.GetCachedUniverseAsync(cancellationToken);
            var symbols = universe.Take(_config.MaxSymbolsToAnalyze).ToList();

            var newHighs52Week = 0;
            var newLows52Week = 0;
            var newHighs20Day = 0;
            var newLows20Day = 0;

            // Process symbols in batches
            var batchSize = 50;
            var batches = symbols.Chunk(batchSize);

            foreach (var batch in batches)
            {
                var tasks = batch.Select(async symbol =>
                {
                    try
                    {
                        // Get enough bars for 52-week analysis
                        var endDate = DateTime.Today;
                        var startDate = endDate.AddDays(-(_config.NewHighsLowsLookbackDays + 10)); // Add buffer days
                        var barsPage = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                        var bars = barsPage.Items.ToList();
                        if (bars.Count < 21) // Need at least 21 days for 20-day analysis
                            return (false, false, false, false);

                        var currentPrice = bars[^1].Close;

                        // 20-day highs/lows
                        var last20Days = bars.TakeLast(21).ToList(); // Include current day
                        var high20Day = last20Days.Take(20).Max(b => b.High); // Exclude current day
                        var low20Day = last20Days.Take(20).Min(b => b.Low);

                        var isNewHigh20Day = currentPrice > high20Day;
                        var isNewLow20Day = currentPrice < low20Day;

                        // 52-week highs/lows (if we have enough data)
                        var isNewHigh52Week = false;
                        var isNewLow52Week = false;

                        if (bars.Count >= _config.NewHighsLowsLookbackDays)
                        {
                            var last52Weeks = bars.Take(bars.Count - 1).ToList(); // Exclude current day
                            var high52Week = last52Weeks.Max(b => b.High);
                            var low52Week = last52Weeks.Min(b => b.Low);

                            isNewHigh52Week = currentPrice > high52Week;
                            isNewLow52Week = currentPrice < low52Week;
                        }

                        return (isNewHigh52Week, isNewLow52Week, isNewHigh20Day, isNewLow20Day);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error calculating new highs/lows for {Symbol}", symbol);
                        return (false, false, false, false);
                    }
                });

                var results = await Task.WhenAll(tasks);

                foreach (var result in results)
                {
                    var (isHigh52, isLow52, isHigh20, isLow20) = result;
                    if (isHigh52) newHighs52Week++;
                    if (isLow52) newLows52Week++;
                    if (isHigh20) newHighs20Day++;
                    if (isLow20) newLows20Day++;
                }
            }

            var totalHighsLows52 = newHighs52Week + newLows52Week;
            var totalHighsLows20 = newHighs20Day + newLows20Day;

            var ratio = newLows52Week > 0 ? (decimal)newHighs52Week / newLows52Week : newHighs52Week > 0 ? 10m : 1m;
            var netNewHighs = newHighs52Week - newLows52Week;

            var metrics = new NewHighsLowsMetrics(
                newHighs52Week,
                newLows52Week,
                newHighs20Day,
                newLows20Day,
                ratio,
                netNewHighs
            );

            // Cache the result
            await CacheNewHighsLowsAsync(metrics);

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating new highs/lows metrics");
            return new NewHighsLowsMetrics(0, 0, 0, 0, 1.0m, 0m);
        }
    }

    public async Task ClearCacheAsync()
    {
        try
        {
            _cachedAnalysis = null;

            var database = await _redisService.GetDatabaseAsync();
            await Task.WhenAll(
                database.KeyDeleteAsync(BreadthCacheKey),
                database.KeyDeleteAsync(AdvanceDeclineCacheKey),
                database.KeyDeleteAsync(MovingAverageCacheKey),
                database.KeyDeleteAsync(NewHighsLowsCacheKey)
            );

            _logger.LogInformation("Market breadth cache cleared");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing market breadth cache");
        }
    }

    // === Private Methods ===

    private decimal CalculateBreadthScore(AdvanceDeclineMetrics advanceDecline, MovingAverageBreadth movingAverages, NewHighsLowsMetrics newHighsLows)
    {
        var score = 0m;

        // Advance/Decline component (30% weight)
        score += advanceDecline.AdvanceDeclinePercent * 0.3m;

        // Moving Average component (40% weight)
        var maScore = (movingAverages.PercentAbove20Sma * 0.2m +
                      movingAverages.PercentAbove50Sma * 0.3m +
                      movingAverages.PercentAbove200Sma * 0.5m);
        score += maScore * 0.4m;

        // New Highs/Lows component (30% weight)
        var totalNewHighsLows = newHighsLows.NewHighs52Week + newHighsLows.NewLows52Week;
        var newHighsPercent = totalNewHighsLows > 0 ? ((decimal)newHighsLows.NewHighs52Week / totalNewHighsLows) * 100m : 50m;
        score += newHighsPercent * 0.3m;

        return Math.Max(0m, Math.Min(100m, score)); // Clamp between 0-100
    }

    private MarketRiskSentiment DetermineSentiment(decimal breadthScore)
    {
        if (breadthScore >= _config.RiskOnThreshold)
            return MarketRiskSentiment.RiskOn;

        if (breadthScore <= _config.RiskOffThreshold)
            return MarketRiskSentiment.RiskOff;

        return MarketRiskSentiment.Neutral;
    }

    private string GenerateAnalysisText(AdvanceDeclineMetrics advanceDecline, MovingAverageBreadth movingAverages,
        NewHighsLowsMetrics newHighsLows, decimal breadthScore, MarketRiskSentiment sentiment)
    {
        var analysis = $"Market Breadth Score: {breadthScore:F1}/100 ({sentiment}). ";

        analysis += $"A/D: {advanceDecline.Advancers}/{advanceDecline.Decliners} ({advanceDecline.AdvanceDeclinePercent:F1}% advancing). ";

        analysis += $"Above SMAs: 20D={movingAverages.PercentAbove20Sma:F1}%, 50D={movingAverages.PercentAbove50Sma:F1}%, 200D={movingAverages.PercentAbove200Sma:F1}%. ";

        analysis += $"New H/L: {newHighsLows.NewHighs52Week}/{newHighsLows.NewLows52Week} (52W), {newHighsLows.NewHighs20Day}/{newHighsLows.NewLows20Day} (20D).";

        return analysis;
    }

    private string GetSentimentChangeReason(MarketRiskSentiment sentiment, decimal breadthScore)
    {
        return sentiment switch
        {
            MarketRiskSentiment.RiskOn => $"Breadth score improved to {breadthScore:F1}, indicating broad market strength",
            MarketRiskSentiment.RiskOff => $"Breadth score declined to {breadthScore:F1}, indicating market weakness",
            _ => $"Breadth score normalized to {breadthScore:F1}, indicating neutral conditions"
        };
    }

    private bool IsAnalysisValid(MarketBreadthAnalysis analysis)
    {
        var age = DateTime.UtcNow - analysis.AnalysisTime;
        return age <= _config.CacheExpiry;
    }

    private async Task CacheBreadthAnalysisAsync(MarketBreadthAnalysis analysis)
    {
        try
        {
            _cachedAnalysis = analysis;

            var database = await _redisService.GetDatabaseAsync();
            var json = JsonSerializer.Serialize(analysis);
            await database.StringSetAsync(BreadthCacheKey, json, _config.CacheExpiry);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error caching breadth analysis");
        }
    }

    private async Task CacheAdvanceDeclineAsync(AdvanceDeclineMetrics metrics)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var json = JsonSerializer.Serialize(metrics);
            await database.StringSetAsync(AdvanceDeclineCacheKey, json, _config.CacheExpiry);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error caching advance/decline metrics");
        }
    }

    private async Task CacheMovingAverageBreadthAsync(MovingAverageBreadth breadth)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var json = JsonSerializer.Serialize(breadth);
            await database.StringSetAsync(MovingAverageCacheKey, json, _config.CacheExpiry);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error caching moving average breadth");
        }
    }

    private async Task CacheNewHighsLowsAsync(NewHighsLowsMetrics metrics)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var json = JsonSerializer.Serialize(metrics);
            await database.StringSetAsync(NewHighsLowsCacheKey, json, _config.CacheExpiry);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error caching new highs/lows metrics");
        }
    }

    // === IDisposable ===

    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            _analysisLock?.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing BreadthService");
        }
        finally
        {
            _disposed = true;
        }
    }
}
