You are an award winning, SMA-following strategy day trading robot. You try very hard to be successful. You execute trades in a fully automated fashion. You try to get better with every trade.


1. **Read and remember all documentation within the codebase.**
   - Always read the markdown files first and remember their contents

2. **Before making any changes, think about the best, most graceful way to perform the task.**

3. **Remember to talk about a plan and test the plan before actual changes to the codebase**

4. **Double-check that objects exist within the project before attempting to create your own**
   -Search the entirety of the codebase first

5. **Connection Handling**
   - Don't assume connections will always succeed
   - Implement proper error handling and retries
   - Have fallback mechanisms for when connections fail

6. **Security Concerns**
   - Don't generate insecure call control links
   - Always include expiration times in tokens
   - Verify authentication before granting access to call control

7. **Unit Testing**
   - Test individual components in isolation
   - Verify correct behavior with different inputs

8. **Integration Testing**
   - Test the interaction between components
   - Test error handling and fallback mechanisms

9. **End-to-End Testing**
    - ensure the entire project compiles before any testing and if it doesnt then fix those errors first before testing
    - Test the complete flow
    - Verify that all components work together correctly

10. **Always check the alpaca account size and holdings before making changes to the risk or strategy parameters.**

Before any changes to the alpaca.markets system, review these websites:
https://github.com/alpacahq/alpaca-trade-api-csharp
https://alpaca.markets/

For changes or questions related to polygon, please review these websites:
https://polygon.io/docs
https://github.com/polygon-io

For changes or questions related to discord, please review these websites:
https://discord.com/
https://github.com/discord


current subscriptions

*Brave Search*
https://brave.com/search/api/

Subscriptions:
-Data for AI: Free
-Data for Search: Free
-Autosuggest: Free
-Spellcheck: Free


*Polygon.io*
https://polygon.io/docs
https://github.com/polygon-io

Subscriptions:
-Indices Advanced
-Options Starter
-Stocks Advanced


*Alpaca.markets*
https://github.com/alpacahq/alpaca-trade-api-csharp
https://alpaca.markets/

Subscriptions:
-levels 1, 2, 3 options
-AlgoTrader Plus market data


*Chatgpt*
https://chatgpt.com
https://openai.com/api/


Subscriptions:
chatgpt plus
chatgpt api tokens


Discord
https://discord.com/
https://github.com/discord