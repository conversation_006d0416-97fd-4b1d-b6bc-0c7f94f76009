using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Unit tests for ExecutionQAService
/// Tests execution quality analysis, slippage calculation, and logging functionality
/// </summary>
public class ExecutionQAServiceTests : IDisposable
{
    private readonly Mock<IPolygonClientFactory> _mockPolygonFactory;
    private readonly Mock<IOptimizedRedisConnectionService> _mockRedisService;
    private readonly Mock<IDiscordNotificationService> _mockDiscordService;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<ILogger<ExecutionQAService>> _mockLogger;
    private readonly Mock<IDatabase> _mockDatabase;
    private readonly ExecutionQAService _executionQAService;

    public ExecutionQAServiceTests()
    {
        _mockPolygonFactory = new Mock<IPolygonClientFactory>();
        _mockRedisService = new Mock<IOptimizedRedisConnectionService>();
        _mockDiscordService = new Mock<IDiscordNotificationService>();
        _mockConfiguration = new Mock<IConfiguration>();
        _mockLogger = new Mock<ILogger<ExecutionQAService>>();
        _mockDatabase = new Mock<IDatabase>();

        // Setup configuration
        var configSection = new Mock<IConfigurationSection>();
        _mockConfiguration.Setup(x => x.GetSection("ExecutionQA")).Returns(configSection.Object);

        // Setup Redis
        _mockRedisService.Setup(x => x.GetDatabaseAsync(It.IsAny<int>()))
            .ReturnsAsync(_mockDatabase.Object);

        _executionQAService = new ExecutionQAService(
            _mockPolygonFactory.Object,
            _mockRedisService.Object,
            _mockDiscordService.Object,
            _mockConfiguration.Object,
            _mockLogger.Object);
    }

    [Fact]
    public void Constructor_WithValidParameters_ShouldInitializeCorrectly()
    {
        // Act & Assert - Constructor should not throw
        var service = new ExecutionQAService(
            _mockPolygonFactory.Object,
            _mockRedisService.Object,
            _mockDiscordService.Object,
            _mockConfiguration.Object,
            _mockLogger.Object);

        service.Should().NotBeNull();
    }

    [Fact]
    public void Constructor_WithNullPolygonFactory_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new ExecutionQAService(
            null!,
            _mockRedisService.Object,
            _mockDiscordService.Object,
            _mockConfiguration.Object,
            _mockLogger.Object);

        act.Should().Throw<ArgumentNullException>().WithParameterName("polygonFactory");
    }

    [Fact]
    public void Constructor_WithNullRedisService_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new ExecutionQAService(
            _mockPolygonFactory.Object,
            null!,
            _mockDiscordService.Object,
            _mockConfiguration.Object,
            _mockLogger.Object);

        act.Should().Throw<ArgumentNullException>().WithParameterName("redisService");
    }

    [Fact]
    public void Constructor_WithNullDiscordService_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new ExecutionQAService(
            _mockPolygonFactory.Object,
            _mockRedisService.Object,
            null!,
            _mockConfiguration.Object,
            _mockLogger.Object);

        act.Should().Throw<ArgumentNullException>().WithParameterName("discordService");
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new ExecutionQAService(
            _mockPolygonFactory.Object,
            _mockRedisService.Object,
            _mockDiscordService.Object,
            _mockConfiguration.Object,
            null!);

        act.Should().Throw<ArgumentNullException>().WithParameterName("logger");
    }

    [Fact]
    public void CalculateSlippage_WithBuyOrderAndHigherExecutionPrice_ShouldReturnUnfavorableSlippage()
    {
        // Arrange
        var execution = new TradeExecution(
            "AAPL",
            "ORDER123",
            100.50m,
            100,
            DateTime.UtcNow,
            "buy",
            "limit",
            100.00m,
            "NASDAQ",
            "EXEC123"
        );
        var expectedPrice = 100.00m;

        // Act
        var result = _executionQAService.CalculateSlippage(execution, expectedPrice);

        // Assert
        result.ExpectedPrice.Should().Be(expectedPrice);
        result.ExecutedPrice.Should().Be(100.50m);
        result.SlippageAmount.Should().Be(0.50m);
        result.SlippagePercent.Should().Be(0.5m);
        result.Direction.Should().Be(SlippageDirection.Unfavorable);
        result.Severity.Should().Be(SlippageSeverity.High);
    }

    [Fact]
    public void CalculateSlippage_WithSellOrderAndHigherExecutionPrice_ShouldReturnFavorableSlippage()
    {
        // Arrange
        var execution = new TradeExecution(
            "AAPL",
            "ORDER123",
            100.50m,
            100,
            DateTime.UtcNow,
            "sell",
            "limit",
            100.00m,
            "NASDAQ",
            "EXEC123"
        );
        var expectedPrice = 100.00m;

        // Act
        var result = _executionQAService.CalculateSlippage(execution, expectedPrice);

        // Assert
        result.Direction.Should().Be(SlippageDirection.Favorable);
        result.SlippageAmount.Should().Be(0.50m);
        result.SlippagePercent.Should().Be(0.5m);
    }

    [Fact]
    public void CalculateSlippage_WithMinimalSlippage_ShouldReturnMinimalSeverity()
    {
        // Arrange
        var execution = new TradeExecution(
            "AAPL",
            "ORDER123",
            100.05m,
            100,
            DateTime.UtcNow,
            "buy",
            "limit",
            100.00m,
            "NASDAQ",
            "EXEC123"
        );
        var expectedPrice = 100.00m;

        // Act
        var result = _executionQAService.CalculateSlippage(execution, expectedPrice);

        // Assert
        result.Severity.Should().Be(SlippageSeverity.Minimal);
        result.SlippagePercent.Should().Be(0.05m);
    }

    [Fact]
    public async Task LogExecutionAsync_WithValidExecution_ShouldLogToRedis()
    {
        // Arrange
        var execution = new TradeExecution(
            "AAPL",
            "ORDER123",
            100.00m,
            100,
            DateTime.UtcNow,
            "buy",
            "market",
            null,
            "NASDAQ",
            "EXEC123"
        );

        // Act
        await _executionQAService.LogExecutionAsync(execution);

        // Assert
        _mockDatabase.Verify(x => x.ListRightPushAsync(
            It.IsAny<RedisKey>(),
            It.IsAny<RedisValue>(),
            It.IsAny<When>(),
            It.IsAny<CommandFlags>()), Times.Once);

        _mockDatabase.Verify(x => x.KeyExpireAsync(
            It.IsAny<RedisKey>(),
            It.IsAny<TimeSpan?>(),
            It.IsAny<ExpireWhen>(),
            It.IsAny<CommandFlags>()), Times.Once);
    }

    [Fact]
    public async Task GetExecutionMetricsAsync_WithNoExecutions_ShouldReturnEmptyMetrics()
    {
        // Arrange
        const string symbol = "AAPL";
        var startDate = DateTime.Today.AddDays(-7);
        var endDate = DateTime.Today;

        _mockDatabase.Setup(x => x.ListRangeAsync(It.IsAny<RedisKey>(), It.IsAny<long>(), It.IsAny<long>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(Array.Empty<RedisValue>());

        // Act
        var result = await _executionQAService.GetExecutionMetricsAsync(symbol, startDate, endDate);

        // Assert
        result.Symbol.Should().Be(symbol);
        result.TotalExecutions.Should().Be(0);
        result.AverageSlippage.Should().Be(0m);
        result.SuccessRate.Should().Be(0m);
    }

    [Fact]
    public async Task CleanupOldLogsAsync_WhenCalled_ShouldNotThrow()
    {
        // Arrange
        var mockServer = new Mock<IServer>();
        var mockMultiplexer = new Mock<IConnectionMultiplexer>();
        var endpoints = new System.Net.EndPoint[] { new System.Net.IPEndPoint(System.Net.IPAddress.Loopback, 6379) };
        
        _mockDatabase.Setup(x => x.Multiplexer).Returns(mockMultiplexer.Object);
        mockMultiplexer.Setup(x => x.GetEndPoints(It.IsAny<bool>())).Returns(endpoints);
        mockMultiplexer.Setup(x => x.GetServer(It.IsAny<System.Net.EndPoint>(), It.IsAny<object>()))
            .Returns(mockServer.Object);

        // Act & Assert
        var act = async () => await _executionQAService.CleanupOldLogsAsync();
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public void Dispose_WhenCalled_ShouldNotThrow()
    {
        // Act & Assert
        var act = () => _executionQAService.Dispose();
        act.Should().NotThrow();
    }

    [Fact]
    public async Task AnalyzeExecutionAsync_AfterDispose_ShouldThrowObjectDisposedException()
    {
        // Arrange
        var execution = new TradeExecution(
            "AAPL",
            "ORDER123",
            100.00m,
            100,
            DateTime.UtcNow,
            "buy",
            "market",
            null,
            "NASDAQ",
            "EXEC123"
        );

        _executionQAService.Dispose();

        // Act & Assert
        var act = async () => await _executionQAService.AnalyzeExecutionAsync(execution);
        await act.Should().ThrowAsync<ObjectDisposedException>();
    }

    public void Dispose()
    {
        _executionQAService?.Dispose();
    }
}
