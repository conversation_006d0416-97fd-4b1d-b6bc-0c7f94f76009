using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using SmaTrendFollower.Services.ErrorHandling;
using Xunit;
using FluentAssertions;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services.ErrorHandling;

public sealed class EnhancedRetryServiceTests
{
    private readonly Mock<ILogger<EnhancedRetryService>> _mockLogger;
    private readonly Mock<ICircuitBreakerService> _mockCircuitBreaker;
    private readonly Mock<IErrorHandler> _mockErrorHandler;
    private readonly EnhancedRetryService _retryService;

    public EnhancedRetryServiceTests()
    {
        _mockLogger = new Mock<ILogger<EnhancedRetryService>>();
        _mockCircuitBreaker = new Mock<ICircuitBreakerService>();
        _mockErrorHandler = new Mock<IErrorHandler>();

        var defaultPolicy = RetryPolicy.ForApi();
        _retryService = new EnhancedRetryService(
            _mockLogger.Object,
            _mockCircuitBreaker.Object,
            _mockErrorHandler.Object,
            Options.Create(defaultPolicy));
    }

    [Fact]
    public async Task ExecuteAsync_WithSuccessfulOperation_ShouldReturnResult()
    {
        // Arrange
        const string expectedResult = "Success";
        var operation = () => Task.FromResult(expectedResult);

        // Act
        var result = await _retryService.ExecuteAsync(operation, "TestOperation");

        // Assert
        result.Should().Be(expectedResult);
    }

    [Fact]
    public async Task ExecuteAsync_WithRetriableFailure_ShouldRetryAndSucceed()
    {
        // Arrange
        var attemptCount = 0;
        var operation = () =>
        {
            attemptCount++;
            if (attemptCount < 3)
                throw new HttpRequestException("Temporary failure");
            return Task.FromResult("Success");
        };

        _mockErrorHandler.Setup(x => x.ShouldRetry(It.IsAny<Exception>(), It.IsAny<int>()))
            .Returns(true);

        // Act
        var result = await _retryService.ExecuteAsync(operation, "TestOperation");

        // Assert
        result.Should().Be("Success");
        attemptCount.Should().Be(3);
    }

    [Fact]
    public async Task ExecuteAsync_WithNonRetriableFailure_ShouldFailImmediately()
    {
        // Arrange
        var attemptCount = 0;
        var operation = () =>
        {
            attemptCount++;
            throw new ArgumentException("Non-retriable failure");
            return Task.FromResult("Success");
        };

        _mockErrorHandler.Setup(x => x.ShouldRetry(It.IsAny<Exception>(), It.IsAny<int>()))
            .Returns(false);

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() =>
            _retryService.ExecuteAsync(operation, "TestOperation"));

        attemptCount.Should().Be(1);
    }

    [Fact]
    public async Task ExecuteAsync_WithMaxRetriesExceeded_ShouldThrowLastException()
    {
        // Arrange
        var attemptCount = 0;
        var operation = () =>
        {
            attemptCount++;
            throw new HttpRequestException($"Failure {attemptCount}");
            return Task.FromResult("Success");
        };

        _mockErrorHandler.Setup(x => x.ShouldRetry(It.IsAny<Exception>(), It.IsAny<int>()))
            .Returns(true);

        var policy = new RetryPolicy { MaxAttempts = 2, BaseDelay = TimeSpan.FromMilliseconds(1) };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<HttpRequestException>(() =>
            _retryService.ExecuteWithPolicyAsync(operation, policy, "TestOperation"));

        exception.Message.Should().Be("Failure 2");
        attemptCount.Should().Be(2);
    }

    [Fact]
    public async Task ExecuteAsync_WithCircuitBreakerEnabled_ShouldUseCircuitBreaker()
    {
        // Arrange
        const string serviceName = "TestService";
        const string expectedResult = "Success";
        var operation = () => Task.FromResult(expectedResult);

        _mockCircuitBreaker.Setup(x => x.ExecuteAsync(
                serviceName,
                It.IsAny<Func<Task<string>>>(),
                It.IsAny<Func<Task<string>>?>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _retryService.ExecuteAsync(operation, "TestOperation", serviceName);

        // Assert
        result.Should().Be(expectedResult);
        _mockCircuitBreaker.Verify(x => x.ExecuteAsync(
            serviceName,
            It.IsAny<Func<Task<string>>>(),
            It.IsAny<Func<Task<string>>?>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ExecuteHttpAsync_ShouldUseHttpRetryPolicy()
    {
        // Arrange
        var response = new HttpResponseMessage(System.Net.HttpStatusCode.OK);
        var operation = () => Task.FromResult(response);

        // Act
        var result = await _retryService.ExecuteHttpAsync(operation, "HttpOperation");

        // Assert
        result.Should().Be(response);
    }

    [Fact]
    public void GetStatistics_ShouldReturnCorrectStatistics()
    {
        // Act
        var stats = _retryService.GetStatistics();

        // Assert
        stats.Should().NotBeNull();
        stats.TotalOperations.Should().Be(0);
        stats.SuccessfulOperations.Should().Be(0);
        stats.FailedOperations.Should().Be(0);
        stats.TotalRetries.Should().Be(0);
    }

    [Fact]
    public void ResetStatistics_ShouldClearAllStatistics()
    {
        // Act
        _retryService.ResetStatistics();
        var stats = _retryService.GetStatistics();

        // Assert
        stats.TotalOperations.Should().Be(0);
        stats.LastResetTime.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public async Task ExecuteAsync_WithCustomRetryPolicy_ShouldUseCustomSettings()
    {
        // Arrange
        var attemptCount = 0;
        var operation = () =>
        {
            attemptCount++;
            if (attemptCount < 2)
                throw new InvalidOperationException("Custom failure");
            return Task.FromResult("Success");
        };

        var customPolicy = new RetryPolicy
        {
            MaxAttempts = 3,
            BaseDelay = TimeSpan.FromMilliseconds(1),
            BackoffStrategy = BackoffStrategy.Fixed,
            ShouldRetryPredicate = ex => ex is InvalidOperationException
        };

        // Act
        var result = await _retryService.ExecuteWithPolicyAsync(operation, customPolicy, "CustomOperation");

        // Assert
        result.Should().Be("Success");
        attemptCount.Should().Be(2);
    }

    [Fact]
    public async Task ExecuteAsync_WithOperationTimeout_ShouldRespectTimeout()
    {
        // Arrange
        var operation = async () =>
        {
            await Task.Delay(TimeSpan.FromSeconds(2));
            return "Should not complete";
        };

        var policy = new RetryPolicy
        {
            MaxAttempts = 1,
            OperationTimeout = TimeSpan.FromMilliseconds(100)
        };

        // Act & Assert
        await Assert.ThrowsAsync<TaskCanceledException>(() =>
            _retryService.ExecuteWithPolicyAsync(operation, policy, "TimeoutOperation"));
    }

    [Theory]
    [InlineData(BackoffStrategy.Fixed, 1, 100)]
    [InlineData(BackoffStrategy.Linear, 2, 200)]
    [InlineData(BackoffStrategy.Exponential, 2, 200)]
    public void CalculateDelay_WithDifferentStrategies_ShouldCalculateCorrectly(
        BackoffStrategy strategy, int attemptCount, int expectedMs)
    {
        // Arrange
        var policy = new RetryPolicy
        {
            BackoffStrategy = strategy,
            BaseDelay = TimeSpan.FromMilliseconds(100),
            UseJitter = false
        };

        // Act
        var delay = CalculateDelayUsingReflection(policy, attemptCount);

        // Assert
        delay.Should().Be(TimeSpan.FromMilliseconds(expectedMs));
    }

    [Fact]
    public async Task ExecuteAsync_ShouldFireRetryEvents()
    {
        // Arrange
        var retryEventFired = false;
        var attemptCount = 0;
        var operation = () =>
        {
            attemptCount++;
            if (attemptCount < 3)
                throw new HttpRequestException("Retry test");
            return Task.FromResult("Success");
        };

        _mockErrorHandler.Setup(x => x.ShouldRetry(It.IsAny<Exception>(), It.IsAny<int>()))
            .Returns(true);

        _retryService.RetryAttempted += (sender, args) =>
        {
            retryEventFired = true;
            args.AttemptInfo.Should().NotBeNull();
            args.AttemptInfo.OperationName.Should().Be("EventTest");
        };

        // Act
        await _retryService.ExecuteAsync(operation, "EventTest");

        // Assert
        retryEventFired.Should().BeTrue();
    }

    private static TimeSpan CalculateDelayUsingReflection(RetryPolicy policy, int attemptCount)
    {
        // This is a simplified version of the delay calculation logic
        // In a real test, you might use reflection to access private methods
        return policy.BackoffStrategy switch
        {
            BackoffStrategy.Fixed => policy.BaseDelay,
            BackoffStrategy.Linear => TimeSpan.FromMilliseconds(policy.BaseDelay.TotalMilliseconds * attemptCount),
            BackoffStrategy.Exponential => TimeSpan.FromMilliseconds(policy.BaseDelay.TotalMilliseconds * Math.Pow(2, attemptCount - 1)),
            _ => policy.BaseDelay
        };
    }
}

public sealed class HealthMonitoringServiceTests
{
    private readonly Mock<ILogger<HealthMonitoringService>> _mockLogger;
    private readonly HealthMonitoringService _healthService;

    public HealthMonitoringServiceTests()
    {
        _mockLogger = new Mock<ILogger<HealthMonitoringService>>();
        _healthService = new HealthMonitoringService(_mockLogger.Object);
    }

    [Fact]
    public async Task CheckServiceHealthAsync_WithUnregisteredService_ShouldReturnUnhealthy()
    {
        // Act
        var result = await _healthService.CheckServiceHealthAsync("UnregisteredService");

        // Assert
        result.Should().NotBeNull();
        result.Status.Should().Be(HealthStatus.Unhealthy);
        result.ServiceName.Should().Be("UnregisteredService");
        result.Description.Should().Contain("not registered");
    }

    [Fact]
    public async Task CheckServiceHealthAsync_WithHealthyService_ShouldReturnHealthy()
    {
        // Arrange
        const string serviceName = "TestService";
        _healthService.RegisterHealthCheck(serviceName, _ =>
            Task.FromResult(HealthCheckResult.Healthy(serviceName, "All good")));

        // Act
        var result = await _healthService.CheckServiceHealthAsync(serviceName);

        // Assert
        result.Should().NotBeNull();
        result.Status.Should().Be(HealthStatus.Healthy);
        result.ServiceName.Should().Be(serviceName);
        result.Description.Should().Be("All good");
    }

    [Fact]
    public async Task CheckServiceHealthAsync_WithUnhealthyService_ShouldReturnUnhealthy()
    {
        // Arrange
        const string serviceName = "TestService";
        var testException = new InvalidOperationException("Service down");
        
        _healthService.RegisterHealthCheck(serviceName, _ =>
            Task.FromResult(HealthCheckResult.Unhealthy(serviceName, "Service is down", testException)));

        // Act
        var result = await _healthService.CheckServiceHealthAsync(serviceName);

        // Assert
        result.Should().NotBeNull();
        result.Status.Should().Be(HealthStatus.Unhealthy);
        result.ServiceName.Should().Be(serviceName);
        result.Exception.Should().Be(testException);
    }

    [Fact]
    public async Task CheckOverallHealthAsync_WithMixedServices_ShouldReturnCorrectOverallStatus()
    {
        // Arrange
        _healthService.RegisterHealthCheck("HealthyService", _ =>
            Task.FromResult(HealthCheckResult.Healthy("HealthyService")));
        
        _healthService.RegisterHealthCheck("DegradedService", _ =>
            Task.FromResult(HealthCheckResult.Degraded("DegradedService")));
        
        _healthService.RegisterHealthCheck("UnhealthyService", _ =>
            Task.FromResult(HealthCheckResult.Unhealthy("UnhealthyService")));

        // Act
        var result = await _healthService.CheckOverallHealthAsync();

        // Assert
        result.Should().NotBeNull();
        result.Status.Should().Be(HealthStatus.Unhealthy); // Worst case determines overall status
        result.TotalServices.Should().Be(3);
        result.HealthyCount.Should().Be(1);
        result.DegradedCount.Should().Be(1);
        result.UnhealthyCount.Should().Be(1);
    }

    [Fact]
    public void RegisterHealthCheck_ShouldRegisterSuccessfully()
    {
        // Arrange
        const string serviceName = "TestService";
        var healthCheck = new Func<CancellationToken, Task<HealthCheckResult>>(_ =>
            Task.FromResult(HealthCheckResult.Healthy(serviceName)));

        // Act
        _healthService.RegisterHealthCheck(serviceName, healthCheck);

        // Assert
        var currentStatus = _healthService.GetCurrentHealthStatus();
        currentStatus.Should().ContainKey(serviceName);
    }

    [Fact]
    public void GetHealthHistory_WithNoHistory_ShouldReturnEmpty()
    {
        // Arrange
        const string serviceName = "TestService";

        // Act
        var history = _healthService.GetHealthHistory(serviceName, TimeSpan.FromHours(1));

        // Assert
        history.Should().BeEmpty();
    }

    [Fact]
    public async Task StartMonitoringAsync_ShouldStartMonitoring()
    {
        // Act
        await _healthService.StartMonitoringAsync();

        // Assert - No exception should be thrown
        // In a real implementation, you might check internal state
    }

    [Fact]
    public async Task StopMonitoringAsync_ShouldStopMonitoring()
    {
        // Arrange
        await _healthService.StartMonitoringAsync();

        // Act
        await _healthService.StopMonitoringAsync();

        // Assert - No exception should be thrown
    }

    [Fact]
    public void GetCurrentHealthStatus_WithNoServices_ShouldReturnEmpty()
    {
        // Act
        var status = _healthService.GetCurrentHealthStatus();

        // Assert
        status.Should().BeEmpty();
    }
}
