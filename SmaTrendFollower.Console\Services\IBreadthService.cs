using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Market breadth analysis service for macro market awareness
/// Provides real-time market breadth metrics to assess risk-on vs risk-off conditions
/// </summary>
public interface IBreadthService
{
    /// <summary>
    /// Calculates current market breadth metrics
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current market breadth analysis</returns>
    Task<MarketBreadthAnalysis> CalculateMarketBreadthAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets cached market breadth analysis if available
    /// </summary>
    /// <returns>Cached breadth analysis or null if not available</returns>
    Task<MarketBreadthAnalysis?> GetCachedBreadthAsync();

    /// <summary>
    /// Determines if current market conditions are risk-on or risk-off
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Market risk sentiment</returns>
    Task<MarketRiskSentiment> GetMarketSentimentAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculates advance/decline ratio for the current universe
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Advance/decline metrics</returns>
    Task<AdvanceDeclineMetrics> CalculateAdvanceDeclineAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculates percentage of universe above key moving averages
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Moving average breadth metrics</returns>
    Task<MovingAverageBreadth> CalculateMovingAverageBreadthAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculates new highs vs new lows for the universe
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>New highs/lows metrics</returns>
    Task<NewHighsLowsMetrics> CalculateNewHighsLowsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Clears cached breadth data
    /// </summary>
    Task ClearCacheAsync();
}

/// <summary>
/// Comprehensive market breadth analysis
/// </summary>
public readonly record struct MarketBreadthAnalysis(
    DateTime AnalysisTime,
    MarketRiskSentiment Sentiment,
    AdvanceDeclineMetrics AdvanceDecline,
    MovingAverageBreadth MovingAverages,
    NewHighsLowsMetrics NewHighsLows,
    decimal BreadthScore,
    string Analysis
);

/// <summary>
/// Market risk sentiment levels
/// </summary>
public enum MarketRiskSentiment
{
    RiskOff,
    Neutral,
    RiskOn
}

/// <summary>
/// Advance/decline metrics
/// </summary>
public readonly record struct AdvanceDeclineMetrics(
    int Advancers,
    int Decliners,
    int Unchanged,
    decimal AdvanceDeclineRatio,
    decimal AdvanceDeclinePercent
);

/// <summary>
/// Moving average breadth metrics
/// </summary>
public readonly record struct MovingAverageBreadth(
    decimal PercentAbove20Sma,
    decimal PercentAbove50Sma,
    decimal PercentAbove200Sma,
    int TotalSymbols,
    int SymbolsAbove20Sma,
    int SymbolsAbove50Sma,
    int SymbolsAbove200Sma
);

/// <summary>
/// New highs and lows metrics
/// </summary>
public readonly record struct NewHighsLowsMetrics(
    int NewHighs52Week,
    int NewLows52Week,
    int NewHighs20Day,
    int NewLows20Day,
    decimal HighLowRatio,
    decimal NetNewHighs
);

/// <summary>
/// Breadth service configuration
/// </summary>
public class BreadthServiceConfig
{
    /// <summary>
    /// Cache expiry time for breadth analysis (default: 15 minutes)
    /// </summary>
    public TimeSpan CacheExpiry { get; set; } = TimeSpan.FromMinutes(15);

    /// <summary>
    /// Minimum percentage change to consider a stock advancing/declining (default: 0.1%)
    /// </summary>
    public decimal MinimumChangePercent { get; set; } = 0.1m;

    /// <summary>
    /// Number of days to look back for new highs/lows (default: 252 trading days = 1 year)
    /// </summary>
    public int NewHighsLowsLookbackDays { get; set; } = 252;

    /// <summary>
    /// Breadth score threshold for risk-on sentiment (default: 60)
    /// </summary>
    public decimal RiskOnThreshold { get; set; } = 60m;

    /// <summary>
    /// Breadth score threshold for risk-off sentiment (default: 40)
    /// </summary>
    public decimal RiskOffThreshold { get; set; } = 40m;

    /// <summary>
    /// Maximum number of symbols to analyze for performance (default: 500)
    /// </summary>
    public int MaxSymbolsToAnalyze { get; set; } = 500;
}

/// <summary>
/// Event args for breadth analysis updates
/// </summary>
public class BreadthAnalysisEventArgs : EventArgs
{
    public required MarketBreadthAnalysis Analysis { get; init; }
}

/// <summary>
/// Event args for sentiment changes
/// </summary>
public class MarketSentimentChangeEventArgs : EventArgs
{
    public required MarketRiskSentiment PreviousSentiment { get; init; }
    public required MarketRiskSentiment CurrentSentiment { get; init; }
    public required DateTime ChangeTime { get; init; }
    public required string Reason { get; init; }
}
