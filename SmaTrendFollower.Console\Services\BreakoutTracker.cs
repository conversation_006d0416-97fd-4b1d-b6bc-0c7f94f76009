using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Collections.Concurrent;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time breakout tracker for individual symbols
/// Maintains price history and detects breakout/breakdown conditions
/// </summary>
internal sealed class BreakoutTracker
{
    private readonly string _symbol;
    private readonly ILogger _logger;
    private BreakoutSignalConfig _config;
    
    private readonly ConcurrentQueue<PriceDataPoint> _priceHistory = new();
    private decimal _priorHigh;
    private decimal _priorLow;
    private decimal _currentPrice;
    private decimal _bidPrice;
    private decimal _askPrice;
    private long _currentVolume;
    private long _averageVolume;
    private DateTime _lastUpdate = DateTime.UtcNow;
    private readonly object _calculationLock = new();
    
    // Breakout state tracking
    private BreakoutCondition _currentCondition = BreakoutCondition.None;
    private DateTime _conditionStartTime = DateTime.UtcNow;
    
    public BreakoutTracker(string symbol, BreakoutSignalConfig config, ILogger logger)
    {
        _symbol = symbol ?? throw new ArgumentNullException(nameof(symbol));
        _config = config ?? throw new ArgumentNullException(nameof(config));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }
    
    public BreakoutStatus? UpdateWithTrade(decimal price, long volume, DateTime timestamp)
    {
        lock (_calculationLock)
        {
            _currentPrice = price;
            _currentVolume = volume;
            _lastUpdate = timestamp;
            
            // Add to price history
            var dataPoint = new PriceDataPoint(price, volume, timestamp, PriceDataType.Trade);
            _priceHistory.Enqueue(dataPoint);
            
            // Clean old data
            CleanOldPriceData(timestamp);
            
            // Update prior high/low
            UpdatePriorHighLow();
            
            return AnalyzeBreakoutConditions(timestamp);
        }
    }
    
    public BreakoutStatus? UpdateWithQuote(decimal bidPrice, decimal askPrice, DateTime timestamp)
    {
        lock (_calculationLock)
        {
            _bidPrice = bidPrice;
            _askPrice = askPrice;
            _lastUpdate = timestamp;
            
            // Use mid-price for analysis if no recent trade
            var midPrice = (bidPrice + askPrice) / 2;
            if (_currentPrice == 0 || timestamp - _lastUpdate > TimeSpan.FromSeconds(30))
            {
                _currentPrice = midPrice;
            }
            
            // Add quote to history
            var dataPoint = new PriceDataPoint(midPrice, 0, timestamp, PriceDataType.Quote);
            _priceHistory.Enqueue(dataPoint);
            
            // Clean old data
            CleanOldPriceData(timestamp);
            
            return AnalyzeBreakoutConditions(timestamp);
        }
    }
    
    public BreakoutStatus? GetCurrentStatus()
    {
        lock (_calculationLock)
        {
            return CreateBreakoutStatus(DateTime.UtcNow);
        }
    }
    
    public void UpdateConfig(BreakoutSignalConfig config)
    {
        _config = config;
    }
    
    public void InitializePriceHistory(IEnumerable<IBar> bars)
    {
        lock (_calculationLock)
        {
            var barsList = bars.ToList();
            if (!barsList.Any())
                return;
                
            // Calculate prior high and low from historical bars
            _priorHigh = barsList.Max(b => b.High);
            _priorLow = barsList.Min(b => b.Low);
            
            // Calculate average volume
            _averageVolume = (long)barsList.Average(b => b.Volume);
            
            // Initialize with latest bar data
            var latestBar = barsList.OrderBy(b => b.TimeUtc).Last();
            _currentPrice = latestBar.Close;
            
            _logger.LogDebug("Initialized {Symbol} with prior high: {High:F2}, low: {Low:F2}, avg volume: {Volume}",
                _symbol, _priorHigh, _priorLow, _averageVolume);
        }
    }
    
    private BreakoutStatus? AnalyzeBreakoutConditions(DateTime timestamp)
    {
        if (_priorHigh == 0 || _priorLow == 0 || _currentPrice < _config.MinPriceThreshold)
            return null;
            
        var breakoutLevel = _priorHigh * (1 + _config.MinBreakoutPercent / 100);
        var breakdownLevel = _priorLow * (1 - _config.MinBreakoutPercent / 100);
        
        var newCondition = DetermineBreakoutCondition(breakoutLevel, breakdownLevel);
        
        // Update condition state
        if (newCondition != _currentCondition)
        {
            _currentCondition = newCondition;
            _conditionStartTime = timestamp;
        }
        
        var signalStrength = CalculateSignalStrength(breakoutLevel, breakdownLevel);
        
        return CreateBreakoutStatus(timestamp, breakoutLevel, breakdownLevel, signalStrength);
    }
    
    private BreakoutCondition DetermineBreakoutCondition(decimal breakoutLevel, decimal breakdownLevel)
    {
        // Check for confirmed breakout (trade above prior high with bid support)
        if (_currentPrice > _priorHigh)
        {
            var bidSupportRatio = _bidPrice / _currentPrice;
            var hasVolumeConfirmation = !_config.RequireVolumeConfirmation || 
                                      (_currentVolume >= _averageVolume * _config.MinVolumeMultiplier);
            var hasBidSupport = !_config.RequireBidSupport || 
                               (bidSupportRatio >= _config.BidSupportThreshold);
            
            if (hasVolumeConfirmation && hasBidSupport)
                return BreakoutCondition.Confirmed;
            else
                return BreakoutCondition.Triggered;
        }
        
        // Check for confirmed breakdown
        if (_currentPrice < _priorLow)
        {
            var askSupportRatio = _askPrice / _currentPrice;
            var hasVolumeConfirmation = !_config.RequireVolumeConfirmation || 
                                      (_currentVolume >= _averageVolume * _config.MinVolumeMultiplier);
            
            if (hasVolumeConfirmation)
                return BreakoutCondition.Confirmed;
            else
                return BreakoutCondition.Triggered;
        }
        
        // Check for approaching breakout
        var distanceToBreakout = Math.Abs(_currentPrice - breakoutLevel) / breakoutLevel;
        var distanceToBreakdown = Math.Abs(_currentPrice - breakdownLevel) / breakdownLevel;
        
        if (distanceToBreakout <= 0.002m || distanceToBreakdown <= 0.002m) // Within 0.2%
            return BreakoutCondition.Approaching;
        
        // Check if previous condition has failed
        if (_currentCondition == BreakoutCondition.Triggered || _currentCondition == BreakoutCondition.Confirmed)
        {
            var timeSinceCondition = DateTime.UtcNow - _conditionStartTime;
            if (timeSinceCondition > _config.SignalValidityPeriod)
                return BreakoutCondition.Failed;
        }
        
        return BreakoutCondition.None;
    }
    
    private decimal CalculateSignalStrength(decimal breakoutLevel, decimal breakdownLevel)
    {
        var strength = 0m;
        
        // Price momentum component (40%)
        if (_currentPrice > _priorHigh)
        {
            var priceStrength = Math.Min((_currentPrice - _priorHigh) / _priorHigh * 100, 10m);
            strength += priceStrength * 4; // Scale to 40%
        }
        else if (_currentPrice < _priorLow)
        {
            var priceStrength = Math.Min((_priorLow - _currentPrice) / _priorLow * 100, 10m);
            strength += priceStrength * 4; // Scale to 40%
        }
        
        // Volume confirmation component (30%)
        if (_averageVolume > 0 && _currentVolume > 0)
        {
            var volumeRatio = (decimal)_currentVolume / _averageVolume;
            var volumeStrength = Math.Min(volumeRatio * 10, 30m);
            strength += volumeStrength;
        }
        
        // Bid/Ask support component (20%)
        if (_bidPrice > 0 && _currentPrice > 0)
        {
            var bidSupportRatio = _bidPrice / _currentPrice;
            var bidStrength = Math.Min(bidSupportRatio * 20, 20m);
            strength += bidStrength;
        }
        
        // Time persistence component (10%)
        var timeSinceCondition = DateTime.UtcNow - _conditionStartTime;
        if (timeSinceCondition <= _config.SignalValidityPeriod)
        {
            var timeStrength = (1 - (decimal)timeSinceCondition.TotalMinutes / (decimal)_config.SignalValidityPeriod.TotalMinutes) * 10;
            strength += Math.Max(timeStrength, 0);
        }
        
        return Math.Min(strength, 100m);
    }
    
    private BreakoutStatus CreateBreakoutStatus(DateTime timestamp, decimal? breakoutLevel = null, decimal? breakdownLevel = null, decimal? signalStrength = null)
    {
        var actualBreakoutLevel = breakoutLevel ?? _priorHigh * (1 + _config.MinBreakoutPercent / 100);
        var actualBreakdownLevel = breakdownLevel ?? _priorLow * (1 - _config.MinBreakoutPercent / 100);
        var actualSignalStrength = signalStrength ?? CalculateSignalStrength(actualBreakoutLevel, actualBreakdownLevel);
        
        return new BreakoutStatus(
            Symbol: _symbol,
            CurrentPrice: _currentPrice,
            PriorHigh: _priorHigh,
            PriorLow: _priorLow,
            BidPrice: _bidPrice,
            AskPrice: _askPrice,
            BreakoutLevel: actualBreakoutLevel,
            BreakdownLevel: actualBreakdownLevel,
            Condition: _currentCondition,
            SignalStrength: actualSignalStrength,
            CurrentVolume: _currentVolume,
            AverageVolume: _averageVolume,
            LastUpdate: timestamp,
            IsValid: _currentPrice >= _config.MinPriceThreshold
        );
    }
    
    private void UpdatePriorHighLow()
    {
        var cutoffTime = DateTime.UtcNow.AddMinutes(-_config.LookbackPeriodMinutes);
        var recentPrices = _priceHistory
            .Where(p => p.Timestamp >= cutoffTime)
            .ToList();
            
        if (recentPrices.Any())
        {
            _priorHigh = recentPrices.Max(p => p.Price);
            _priorLow = recentPrices.Min(p => p.Price);
            
            // Update average volume from trades only
            var recentTrades = recentPrices.Where(p => p.Type == PriceDataType.Trade && p.Volume > 0).ToList();
            if (recentTrades.Any())
            {
                _averageVolume = (long)recentTrades.Average(p => p.Volume);
            }
        }
    }
    
    private void CleanOldPriceData(DateTime currentTime)
    {
        var cutoffTime = currentTime.AddMinutes(-_config.LookbackPeriodMinutes * 2); // Keep extra data for calculations
        
        while (_priceHistory.TryPeek(out var oldestPoint) && oldestPoint.Timestamp < cutoffTime)
        {
            _priceHistory.TryDequeue(out _);
        }
    }
}

/// <summary>
/// Internal price data point for breakout analysis
/// </summary>
internal readonly record struct PriceDataPoint(
    decimal Price,
    long Volume,
    DateTime Timestamp,
    PriceDataType Type
);

/// <summary>
/// Type of price data
/// </summary>
internal enum PriceDataType
{
    Trade,
    Quote
}
