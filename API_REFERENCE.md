# SmaTrendFollower API Reference

## Core Trading Interfaces

### ITradingService
Main orchestrator for trading operations and cycle management.

```csharp
public interface ITradingService
{
    Task ExecuteCycleAsync(CancellationToken cancellationToken = default);
}
```

**Methods:**
- `ExecuteCycleAsync(cancellationToken)`: Executes a complete trading cycle including safety checks, signal generation, risk management, and trade execution.

**Implementation Notes:**
- Coordinates all trading services in proper sequence
- Handles safety validation and market regime checks
- Manages error handling and logging throughout the cycle

### ISignalGenerator
Generates trading signals using SMA momentum strategy with universe screening.

```csharp
public interface ISignalGenerator
{
    Task<IEnumerable<TradingSignal>> RunAsync(int topN = 10);
}
```

**Methods:**
- `RunAsync(topN)`: Generates up to `topN` trading signals based on SMA momentum strategy.

**Strategy Implementation:**
- **Universe**: SPY + top-500 tickers from universe provider
- **Technical Filters**: Close > SMA50 && Close > SMA200 && ATR14/Close < 3%
- **Ranking**: 6-month total return descending
- **Output**: Top N symbols with complete TradingSignal objects including price, ATR, and momentum data

**Enhanced Versions:**
- `ParallelSignalGenerator`: High-performance parallel processing implementation
- `EnhancedSignalGenerator`: Advanced filtering with momentum and volatility filters

### IRiskManager
Manages position sizing and risk calculations with dynamic risk tolerance.

```csharp
public interface IRiskManager
{
    Task<decimal> CalculateQuantityAsync(TradingSignal signal);
}
```

**Methods:**
- `CalculateQuantityAsync(signal)`: Calculates position size based on risk management rules and current account equity.

**Risk Management Algorithm:**
- **Risk Capital**: min(account equity × 1%, $1000) - 10bps per $100k cap
- **Position Sizing**: quantity = riskDollars / (ATR14 × price)
- **Safety Constraint**: quantity ≤ riskDollars / price
- **Dynamic Adjustment**: Risk tolerance automatically adjusts based on account size

### ITradeExecutor
Executes trades with comprehensive order management and stop-loss placement.

```csharp
public interface ITradeExecutor
{
    Task ExecuteTradeAsync(TradingSignal signal, decimal quantity);
}
```

**Methods:**
- `ExecuteTradeAsync(signal, quantity)`: Executes trade with Limit-on-Open entry and automatic stop-loss placement.

**Execution Pattern:**
1. **Order Cleanup**: Cancel existing orders for symbol
2. **Entry Order**: Submit Limit-on-Open Buy at lastClose × 1.002
3. **Stop-Loss**: Place GTC stop-loss Sell at entry - 2×ATR14
4. **Integration**: Coordinates with StopManager for trailing stop setup

**Enhanced Versions:**
- `SafeTradeExecutor`: Additional safety checks and validation

### IPortfolioGate
Market condition gating using SPY SMA200 trend filter.

```csharp
public interface IPortfolioGate
{
    Task<bool> ShouldTradeAsync();
}
```

**Methods:**
- `ShouldTradeAsync()`: Returns true if market conditions allow trading based on SPY SMA200 analysis.

**Implementation Logic:**
- Fetches 250+ days of SPY historical data
- Calculates 200-day Simple Moving Average
- Returns `true` if SPY close > SPY SMA200 (bullish market regime)
- Provides comprehensive logging of market condition analysis

### IStopManager
Manages trailing stop-loss orders for capital preservation and risk control.

```csharp
public interface IStopManager
{
    Task UpdateTrailingStopsAsync(CancellationToken cancellationToken = default);
    Task SetInitialStopAsync(string symbol, decimal entryPrice, decimal atr, decimal quantity, CancellationToken cancellationToken = default);
    Task RemoveStopsAsync(string symbol, CancellationToken cancellationToken = default);
}
```

**Methods:**
- `UpdateTrailingStopsAsync(cancellationToken)`: Updates trailing stops for all open positions based on current market data
- `SetInitialStopAsync(symbol, entryPrice, atr, quantity, cancellationToken)`: Sets initial stop-loss order for a new position
- `RemoveStopsAsync(symbol, cancellationToken)`: Removes stop-loss orders when position is closed

**Stop-Loss Strategy:**
- **Initial Stop**: Entry price - 2×ATR14
- **Trailing Logic**: Adjusts stop higher as price moves favorably
- **Risk Control**: Maintains 2×ATR distance for volatility-adjusted stops

**Enhanced Versions:**
- `RealTimeTrailingStopManager`: Live price monitoring with automatic adjustments

## Market Data Interfaces

### IMarketDataService
Unified market data interface combining Alpaca and Polygon sources with comprehensive data access.

```csharp
public interface IMarketDataService
{
    // === Historical Data ===
    Task<IPage<IBar>> GetStockBarsAsync(string symbol, DateTime startDate, DateTime endDate);
    Task<IPage<IBar>> GetStockMinuteBarsAsync(string symbol, DateTime startDate, DateTime endDate);
    Task<IDictionary<string, IPage<IBar>>> GetStockBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate);
    Task<IDictionary<string, IPage<IBar>>> GetStockMinuteBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate);

    // === Account & Positions ===
    Task<IAccount> GetAccountAsync();
    Task<IReadOnlyList<IPosition>> GetPositionsAsync();
    Task<IReadOnlyList<IOrder>> GetRecentFillsAsync(int limitCount = 100);

    // === Index Data (Polygon) ===
    Task<decimal?> GetIndexValueAsync(string indexSymbol);
    Task<IEnumerable<IndexBar>> GetIndexBarsAsync(string indexSymbol, DateTime startDate, DateTime endDate);

    // === Options Data (Polygon) ===
    Task<IEnumerable<OptionData>> GetOptionsDataAsync(string underlyingSymbol, DateTime? expirationDate = null);
    Task<IEnumerable<VixTermData>> GetVixTermStructureAsync();
    Task<IEnumerable<OptionQuote>> GetOptionsQuotesAsync(IEnumerable<string> optionSymbols);
    Task<IEnumerable<OptionData>> GetProtectivePutOptionsAsync(string underlyingSymbol, int daysToExpiration = 30);
    Task<IEnumerable<OptionData>> GetCoveredCallOptionsAsync(string underlyingSymbol, decimal currentPrice, int daysToExpiration = 7);
}
```

**Data Sources & Capabilities:**
- **Alpaca Markets**: Account data, positions, stock/ETF bars, live fills, real-time streaming
- **Polygon.io**: Index data (SPX, VIX, DJI, NDX), options data with Greeks, fallback bars

**Key Features:**
- **Automatic Fallback**: Polygon minute bars when Alpaca hits rate limits
- **Timestamp Normalization**: All timestamps converted to UTC for consistency
- **Batch Processing**: Multiple symbols processed efficiently
- **Caching Integration**: SQLite cache for historical data optimization
- **Error Handling**: Comprehensive retry logic with exponential backoff
- **Rate Limiting**: Built-in rate limiting for both Alpaca (200/min) and Polygon (5/sec)

**Supported Index Symbols:**
- `I:SPX` - S&P 500 Index
- `I:VIX` - CBOE Volatility Index
- `I:DJI` - Dow Jones Industrial Average
- `I:NDX` - NASDAQ 100 Index

### IStreamingDataService
Real-time market data streaming with comprehensive WebSocket connectivity.

```csharp
public interface IStreamingDataService
{
    // === Connection Management ===
    Task ConnectAlpacaStreamAsync();
    Task ConnectPolygonStreamAsync();
    Task DisconnectAsync();

    // === Subscriptions ===
    Task SubscribeToQuotesAsync(IEnumerable<string> symbols);
    Task SubscribeToTradeUpdatesAsync();
    Task SubscribeToIndexUpdatesAsync(IEnumerable<string> indexSymbols);
    Task SubscribeToOptionsQuotesAsync(IEnumerable<string> optionSymbols);
    Task UnsubscribeAllAsync();

    // === Status ===
    StreamingConnectionStatus ConnectionStatus { get; }

    // === Core Events ===
    event EventHandler<QuoteEventArgs> QuoteReceived;
    event EventHandler<TradeEventArgs> TradeUpdated;
    event EventHandler<IndexEventArgs> IndexUpdated;

    // === Enhanced Events ===
    event EventHandler<VixSpikeEventArgs> VixSpikeDetected;
    event EventHandler<OptionsQuoteEventArgs> OptionsQuoteReceived;
}
```

**Streaming Capabilities:**
- **Alpaca WebSocket**: Live quotes, bars, trade execution updates
- **Polygon WebSocket**: Index data streams, options quotes, volatility triggers
- **Connection Health**: Automatic reconnection and health monitoring
- **Event-Driven**: Real-time event processing for immediate market response

**Connection Status Values:**
- `Disconnected` - Not connected to any streams
- `Connecting` - Establishing connections
- `Connected` - Successfully connected and receiving data
- `Reconnecting` - Attempting to restore lost connections
- `Error` - Connection error state
```

**Capabilities:**
- Live quotes and bars via Alpaca WebSocket
- Trade execution updates and fills
- Index/volatility triggers via Polygon WebSocket
- Automatic reconnection and health monitoring

## Cache and Data Management

### IStockBarCacheService
SQLite-based historical bar caching.

```csharp
public interface IStockBarCacheService
{
    Task<List<CachedStockBar>> GetCachedBarsAsync(string symbol, string timeFrame, DateTime startDate, DateTime endDate);
    Task CacheBarsAsync(string symbol, string timeFrame, IEnumerable<IBar> bars);
    Task<(DateTime? earliestDate, DateTime? latestDate)> GetCachedDateRangeAsync(string symbol, string timeFrame);
    Task CleanupOldDataAsync(TimeSpan retentionPeriod);
}
```

**Features:**
- 1-year historical bar retention
- Bulk insert optimization
- Automatic cleanup of old data
- Performance metrics and monitoring

### IRedisWarmingService
Pre-market cache warming for fast execution.

```csharp
public interface IRedisWarmingService
{
    Task WarmCacheAsync(CancellationToken cancellationToken = default);
    Task PersistRedisStateAsync(CancellationToken cancellationToken = default);
    Task ClearCacheAsync(CancellationToken cancellationToken = default);
}
```

**Cache Keys:**
- `stop:{symbol}`: Trailing stop levels
- `signal:{symbol}:{yyyymmdd}`: Daily signal flags
- `block:{symbol}:{yyyymmdd}`: Daily throttle flags

## Enhanced Services

### IMarketRegimeService
Market condition analysis and regime detection.

```csharp
public interface IMarketRegimeService
{
    Task<MarketRegimeAnalysis> DetectRegimeAsync(string symbol = "SPY", CancellationToken cancellationToken = default);
    Task<MarketRegimeAnalysis> GetCachedRegimeAsync(string symbol = "SPY", CancellationToken cancellationToken = default);
}
```

**Regimes:**
- `TrendingUp`: Favorable for long positions
- `TrendingDown`: Unfavorable market conditions
- `Sideways`: Range-bound market
- `Volatile`: High volatility regime

### IDynamicUniverseProvider
Dynamic symbol universe generation with filtering.

```csharp
public interface IDynamicUniverseProvider
{
    Task<UniverseResult> GetCachedUniverseAsync(CancellationToken cancellationToken = default);
    Task<UniverseResult> BuildUniverseAsync(UniverseFilterCriteria criteria, CancellationToken cancellationToken = default);
    Task<UniverseResult> RefreshUniverseAsync(CancellationToken cancellationToken = default);
}
```

**Filter Criteria:**
- Minimum price: >$10
- Minimum volume: >1M shares daily
- Volatility: >2% daily standard deviation
- Maximum symbols: Configurable limit

## Core Models

### TradingSignal
Represents a trading signal with technical analysis data.

```csharp
public readonly record struct TradingSignal(
    string Symbol,
    decimal Price,
    decimal Atr,
    decimal SixMonthReturn
);
```

### MarketRegimeAnalysis
Market regime analysis results.

```csharp
public class MarketRegimeAnalysis
{
    public MarketRegime Regime { get; set; }
    public DateTime DetectedAt { get; set; }
    public decimal SmaSlope { get; set; }
    public decimal AverageAtr { get; set; }
    public decimal ReturnToDrawdownRatio { get; set; }
    public decimal Confidence { get; set; }
    public string Metadata { get; set; }
}
```

### UniverseResult
Dynamic universe generation results.

```csharp
public class UniverseResult
{
    public List<string> Symbols { get; set; }
    public DateTime GeneratedAt { get; set; }
    public int CandidateCount { get; set; }
    public int QualifiedCount { get; set; }
    public UniverseFilterCriteria FilterCriteria { get; set; }
    public UniverseMetrics Metrics { get; set; }
}
```

## Client Factories

### IAlpacaClientFactory
Creates and manages Alpaca API clients.

```csharp
public interface IAlpacaClientFactory
{
    IAlpacaTradingClient CreateTradingClient();
    IAlpacaDataClient CreateDataClient();
    IAlpacaStreamingClient CreateStreamingClient();
}
```

### IPolygonClientFactory
Creates and manages Polygon HTTP clients.

```csharp
public interface IPolygonClientFactory
{
    HttpClient CreateClient();
    Task<T> ExecuteRequestAsync<T>(string endpoint, CancellationToken cancellationToken = default);
}
```

## Safety and Monitoring

### ITradingSafetyGuard
Trading safety validation and checks.

```csharp
public interface ITradingSafetyGuard
{
    Task<SafetyCheckResult> ValidateTradingCycleAsync(CancellationToken cancellationToken = default);
    Task<SafetyCheckResult> ValidateTradeAsync(TradingSignal signal, decimal quantity, CancellationToken cancellationToken = default);
}
```

### IApiHealthMonitor
API connectivity and health monitoring.

```csharp
public interface IApiHealthMonitor
{
    Task<ApiHealthStatus> GetAlpacaHealthAsync();
    Task<ApiHealthStatus> GetPolygonHealthAsync();
    Task<OverallHealthStatus> GetOverallHealthAsync();
}
```

## Usage Examples

### Basic Trading Cycle
```csharp
// Inject ITradingService
var tradingService = serviceProvider.GetRequiredService<ITradingService>();

// Execute trading cycle
await tradingService.ExecuteCycleAsync();
```

### Market Data Retrieval
```csharp
// Get historical bars
var marketData = serviceProvider.GetRequiredService<IMarketDataService>();
var bars = await marketData.GetStockBarsAsync("AAPL", DateTime.Today.AddDays(-30), DateTime.Today);

// Get account information
var account = await marketData.GetAccountAsync();
Console.WriteLine($"Equity: {account.Equity:C}");
```

### Real-time Streaming
```csharp
var streaming = serviceProvider.GetRequiredService<IStreamingDataService>();

// Subscribe to events
streaming.QuoteReceived += (sender, e) => 
    Console.WriteLine($"{e.Symbol}: {e.BidPrice:C} x {e.AskPrice:C}");

// Connect and subscribe
await streaming.ConnectAlpacaStreamAsync();
await streaming.SubscribeToQuotesAsync(new[] { "SPY", "QQQ", "AAPL" });
```

## Error Handling

### Common Exception Types
- `AlpacaApiException`: Alpaca API-specific errors
- `PolygonApiException`: Polygon API-specific errors
- `TradingValidationException`: Trading rule violations
- `MarketDataException`: Data retrieval failures
- `CacheException`: Cache operation failures

### Retry Policies
All API calls implement exponential backoff retry policies:
- **Alpaca**: 3 retries with 200ms base delay
- **Polygon**: 3 retries with 200ms base delay
- **Database**: 2 retries with 100ms base delay

### Rate Limiting
- **Alpaca Markets**: 200 requests/minute
- **Polygon.io**: 5 requests/second
- **Automatic throttling**: Built-in rate limiting with queue management

## Phase 7 Services

### SlippageEstimator

```csharp
public interface ISlippageEstimator : IDisposable
{
    // Core Methods
    Task StartTrackingAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    Task StopTrackingAsync(CancellationToken cancellationToken = default);
    Task RecordOrderSubmissionAsync(OrderSubmissionRecord submission);
    Task RecordOrderFillAsync(OrderFillRecord fill);
    Task<SlippageEstimate> EstimateSlippageAsync(string symbol, decimal orderSize, OrderSide side, decimal currentSpread);
    Task<SlippageStatistics?> GetSlippageStatisticsAsync(string symbol);
    Task<SlippageModel?> GetSlippageModelAsync(string symbol);
    Task RecalibrateModelAsync(string symbol);

    // Configuration
    Task UpdateConfigurationAsync(SlippageEstimatorConfig config);
    SlippageTrackingStatus GetStatus();
    IEnumerable<string> GetTrackedSymbols();
}
```

### TickBarBuilder

```csharp
public interface ITickBarBuilder : IDisposable
{
    // Core Methods
    Task StartBuildingAsync(IEnumerable<string> symbols, IEnumerable<BarBuildingConfig> configs, CancellationToken cancellationToken = default);
    Task StopBuildingAsync(CancellationToken cancellationToken = default);
    Task ProcessTradeTickAsync(TradeTick tick);
    Task ProcessQuoteTickAsync(QuoteTick tick);
    Task<CustomBar?> GetCurrentBarAsync(string symbol, CustomBarType barType);
    Task<IEnumerable<CustomBar>> GetCompletedBarsAsync(string symbol, CustomBarType barType, int count = 100);
    Task ForceBarCompletionAsync(string symbol, CustomBarType barType, string reason = "Manual");

    // Configuration
    Task UpdateConfigurationAsync(string symbol, BarBuildingConfig config);
    BarBuildingStatus GetStatus();
    IEnumerable<string> GetProcessedSymbols();
    IEnumerable<CustomBarType> GetSupportedBarTypes();
}
```

### SmartTradeThrottler

```csharp
public interface ISmartTradeThrottler : IDisposable
{
    // Core Methods
    Task StartThrottlingAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    Task StopThrottlingAsync(CancellationToken cancellationToken = default);
    Task<TradeThrottleDecision> ShouldAllowTradeAsync(string symbol, TradeRequest tradeRequest);
    Task RecordTradeExecutionAsync(string symbol, TradeExecution execution);
    Task<ThrottlingStatus?> GetThrottlingStatusAsync(string symbol);
    Task<TradeFrequencyStats?> GetTradeFrequencyStatsAsync(string symbol);
    Task OverrideThrottlingAsync(string symbol, TimeSpan duration, string reason);

    // Configuration
    Task UpdateConfigurationAsync(TradeThrottlerConfig config);
    Task UpdateSymbolRulesAsync(string symbol, SymbolThrottlingRules rules);
    ThrottlingServiceStatus GetStatus();
    IEnumerable<string> GetThrottledSymbols();
    IEnumerable<string> GetMonitoredSymbols();
}
```

## Polygon Universe Management Services

### IPolygonSymbolUniverseService

Manages the complete symbol universe from Polygon API with weekly caching.

```csharp
public interface IPolygonSymbolUniverseService
{
    // Fetch full symbol list from Polygon API with pagination
    Task<IEnumerable<PolygonSymbolInfo>> FetchFullSymbolListAsync(CancellationToken cancellationToken = default);

    // Get cached symbol list or fetch if not available/stale
    Task<IEnumerable<PolygonSymbolInfo>> GetSymbolListAsync(CancellationToken cancellationToken = default);

    // Force refresh of symbol list from Polygon API
    Task<IEnumerable<PolygonSymbolInfo>> RefreshSymbolListAsync(CancellationToken cancellationToken = default);

    // Check if cached symbol list is valid (not stale)
    Task<bool> IsCacheValidAsync(CancellationToken cancellationToken = default);

    // Get cache statistics and metadata
    Task<RedisPolygonSymbolList?> GetCacheDetailsAsync(CancellationToken cancellationToken = default);
}
```

### IPolygonSymbolSnapshotService

Provides real-time symbol filtering and ranking using Polygon snapshot data.

```csharp
public interface IPolygonSymbolSnapshotService
{
    // Get real-time snapshots for all symbols
    Task<IEnumerable<PolygonSnapshotTicker>> GetAllSnapshotsAsync(CancellationToken cancellationToken = default);

    // Get snapshots for specific symbols
    Task<IEnumerable<PolygonSnapshotTicker>> GetSnapshotsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);

    // Filter and rank symbols based on real-time criteria
    Task<IEnumerable<UniverseCandidate>> FilterAndRankSymbolsAsync(
        IEnumerable<PolygonSymbolInfo> symbols,
        CandidateFilterCriteria criteria,
        CancellationToken cancellationToken = default);

    // Get historical volatility for symbols
    Task<Dictionary<string, decimal>> GetHistoricalVolatilityAsync(
        IEnumerable<string> symbols,
        int periodDays = 20,
        CancellationToken cancellationToken = default);

    // Get average volume for symbols
    Task<Dictionary<string, long>> GetAverageVolumeAsync(
        IEnumerable<string> symbols,
        int periodDays = 20,
        CancellationToken cancellationToken = default);
}
```

### IDailyUniverseRefreshService

Manages daily universe refresh and candidate selection with scheduled execution.

```csharp
public interface IDailyUniverseRefreshService
{
    // Manually trigger universe refresh
    Task<RedisUniverseCandidates> RefreshUniverseAsync(CancellationToken cancellationToken = default);

    // Get current cached candidates
    Task<RedisUniverseCandidates?> GetCurrentCandidatesAsync(CancellationToken cancellationToken = default);

    // Check if candidates cache is valid
    Task<bool> IsCacheValidAsync(CancellationToken cancellationToken = default);

    // Get service status and next refresh time
    Task<UniverseRefreshStatus> GetStatusAsync(CancellationToken cancellationToken = default);
}
```

### IWebSocketSymbolSubscriptionManager

Manages WebSocket subscriptions for filtered universe candidates.

```csharp
public interface IWebSocketSymbolSubscriptionManager
{
    // Subscribe to symbols via WebSocket
    Task SubscribeToSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);

    // Unsubscribe from symbols
    Task UnsubscribeFromSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);

    // Get currently subscribed symbols
    IEnumerable<string> GetSubscribedSymbols();

    // Get subscription status
    Task<SubscriptionStatus> GetStatusAsync(CancellationToken cancellationToken = default);

    // Refresh subscriptions based on current universe candidates
    Task RefreshSubscriptionsAsync(CancellationToken cancellationToken = default);
}
```

### IMarketScheduleCoordinatorService

Coordinates market schedule events for universe management.

```csharp
public interface IMarketScheduleCoordinatorService
{
    // Get current market schedule status
    Task<MarketScheduleStatus> GetStatusAsync(CancellationToken cancellationToken = default);

    // Check if market is currently open
    bool IsMarketOpen();

    // Get next market event time
    DateTime GetNextMarketEventTime();

    // Manually trigger universe refresh
    Task TriggerUniverseRefreshAsync(CancellationToken cancellationToken = default);

    // Manually trigger subscription setup
    Task TriggerSubscriptionSetupAsync(CancellationToken cancellationToken = default);

    // Manually trigger trading start
    Task TriggerTradingStartAsync(CancellationToken cancellationToken = default);
}
```

## Configuration

### Environment Variables
```bash
# Required - Alpaca Trading API
APCA_API_KEY_ID=your_alpaca_key_id
APCA_API_SECRET_KEY=your_alpaca_secret_key
APCA_API_ENV=paper  # or live

# Required - Polygon Market Data (Enhanced)
POLY_API_KEY=your_polygon_api_key

# Optional - Redis Cache (Recommended for Polygon Universe)
REDIS_URL=localhost:6379
REDIS_DATABASE=0
REDIS_PASSWORD=your_redis_password

# Optional - Discord Notifications
DISCORD_BOT_TOKEN=your_discord_bot_token
DISCORD_CHANNEL_ID=your_discord_channel_id

# Polygon Universe Integration
UsePolygonUniverse=true  # Enable Polygon-based universe management
```

### Service Registration
```csharp
// Register core services
services.AddSingleton<IAlpacaClientFactory, AlpacaClientFactory>();
services.AddSingleton<IMarketDataService, MarketDataService>();
services.AddScoped<ITradingService, TradingService>();
services.AddScoped<ISignalGenerator, SignalGenerator>();
services.AddScoped<IRiskManager, RiskManager>();
services.AddScoped<ITradeExecutor, TradeExecutor>();
```

## Performance Considerations

### Caching Strategy
- **SQLite Cache**: Historical bars cached locally for 1 year
- **Redis Cache**: Live trading state with 24-hour TTL
- **Memory Cache**: Frequently accessed configuration data

### Optimization Tips
1. **Batch Operations**: Use multi-symbol API calls when possible
2. **Cache Warming**: Pre-load cache before market open
3. **Connection Pooling**: Reuse HTTP connections
4. **Async Operations**: All I/O operations are asynchronous

### Memory Management
- Implement `IDisposable` for all services with resources
- Use `using` statements for short-lived objects
- Monitor memory usage in production environments

## Testing

### Unit Test Examples
```csharp
[Fact]
public async Task SignalGenerator_ShouldReturnValidSignals()
{
    // Arrange
    var mockMarketData = new Mock<IMarketDataService>();
    var signalGenerator = new SignalGenerator(mockMarketData.Object, logger);

    // Act
    var signals = await signalGenerator.RunAsync(5);

    // Assert
    signals.Should().NotBeEmpty();
    signals.Should().HaveCountLessOrEqualTo(5);
}
```

### Integration Test Setup
```csharp
public class TradingServiceIntegrationTests : IClassFixture<TestFixture>
{
    private readonly IServiceProvider _serviceProvider;

    public TradingServiceIntegrationTests(TestFixture fixture)
    {
        _serviceProvider = fixture.ServiceProvider;
    }

    [Fact]
    public async Task ExecuteCycleAsync_ShouldCompleteSuccessfully()
    {
        var tradingService = _serviceProvider.GetRequiredService<ITradingService>();
        await tradingService.ExecuteCycleAsync();
    }
}
```

## Security Best Practices

### API Key Security
- Store API keys in environment variables only
- Use separate keys for paper and live trading
- Rotate keys regularly
- Monitor API key usage

### Network Security
- Use HTTPS for all API communications
- Implement certificate validation
- Consider VPN for production deployments

### Data Protection
- Encrypt sensitive data at rest
- Use secure connection strings
- Implement proper access controls

This comprehensive API reference provides all necessary information for developing with and extending the SmaTrendFollower system.
