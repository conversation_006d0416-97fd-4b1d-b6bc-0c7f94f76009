using System;

namespace SmaTrendFollower.Exceptions;

/// <summary>
/// Exception thrown when all VIX data sources fail and trading should be halted
/// </summary>
public class VixDataUnavailableException : Exception
{
    public VixDataUnavailableException() : base("VIX data is unavailable from all sources")
    {
    }

    public VixDataUnavailableException(string message) : base(message)
    {
    }

    public VixDataUnavailableException(string message, Exception innerException) : base(message, innerException)
    {
    }

    /// <summary>
    /// Recommended wait time before retrying VIX data acquisition
    /// </summary>
    public TimeSpan RecommendedWaitTime { get; init; } = TimeSpan.FromMinutes(15);

    /// <summary>
    /// Timestamp when the exception was thrown
    /// </summary>
    public DateTime FailureTime { get; init; } = DateTime.UtcNow;
}
