using System;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Standalone test to demonstrate the top 200 stock universe selection
/// This simulates the DynamicUniverseProvider logic without dependencies
/// </summary>
public class UniverseTest
{
    public static void Main()
    {
        Console.WriteLine("=== SmaTrendFollower Phase 6 Universe Selection Test ===");
        Console.WriteLine();
        
        var universeProvider = new MockDynamicUniverseProvider();
        var top200Stocks = universeProvider.GetTop200Stocks();
        
        Console.WriteLine($"Selected {top200Stocks.Count} stocks for trading universe:");
        Console.WriteLine();
        
        // Display in groups of 10 for readability
        for (int i = 0; i < top200Stocks.Count; i += 10)
        {
            var group = top200Stocks.Skip(i).Take(10);
            Console.WriteLine($"{i + 1,3}-{Math.Min(i + 10, top200Stocks.Count),3}: {string.Join(", ", group)}");
        }
        
        Console.WriteLine();
        Console.WriteLine("=== Selection Criteria ===");
        Console.WriteLine("• Price > $10.00");
        Console.WriteLine("• Average Daily Volume > 1,000,000 shares");
        Console.WriteLine("• Market Cap > $1 billion");
        Console.WriteLine("• 20-day volatility > 2%");
        Console.WriteLine("• Excludes penny stocks, ETFs, and illiquid securities");
        Console.WriteLine("• Ranked by 6-month momentum and liquidity");
        Console.WriteLine();
        Console.WriteLine("=== Phase 6 Enhancements ===");
        Console.WriteLine("• Real-time index regime filtering (SPX, VIX, NDX)");
        Console.WriteLine("• Market breadth validation");
        Console.WriteLine("• VIX data freshness enforcement (15-minute threshold)");
        Console.WriteLine("• Dynamic execution strategy selection");
        Console.WriteLine("• Breadth-adjusted position sizing");
    }
}

/// <summary>
/// Mock implementation of DynamicUniverseProvider for demonstration
/// </summary>
public class MockDynamicUniverseProvider
{
    private readonly List<StockCandidate> _stockCandidates;
    
    public MockDynamicUniverseProvider()
    {
        _stockCandidates = GenerateMockStockCandidates();
    }
    
    public List<string> GetTop200Stocks()
    {
        return _stockCandidates
            .Where(s => s.Price > 10.00m)
            .Where(s => s.AverageDailyVolume > 1_000_000)
            .Where(s => s.MarketCap > 1_000_000_000m)
            .Where(s => s.Volatility > 0.02m)
            .OrderByDescending(s => s.MomentumScore)
            .ThenByDescending(s => s.LiquidityScore)
            .Take(200)
            .Select(s => s.Symbol)
            .ToList();
    }
    
    private List<StockCandidate> GenerateMockStockCandidates()
    {
        // This represents the actual stock universe that would be fetched from Polygon
        // In the real system, this data comes from /v3/reference/tickers API
        return new List<StockCandidate>
        {
            // Large Cap Tech
            new("AAPL", 175.50m, 45_000_000, 2_800_000_000_000m, 0.025m, 0.85m, 0.95m),
            new("MSFT", 378.25m, 25_000_000, 2_800_000_000_000m, 0.022m, 0.82m, 0.92m),
            new("GOOGL", 138.75m, 28_000_000, 1_750_000_000_000m, 0.028m, 0.78m, 0.88m),
            new("AMZN", 145.80m, 35_000_000, 1_500_000_000_000m, 0.032m, 0.75m, 0.85m),
            new("NVDA", 485.20m, 42_000_000, 1_200_000_000_000m, 0.045m, 0.92m, 0.98m),
            new("META", 325.60m, 18_000_000, 850_000_000_000m, 0.035m, 0.72m, 0.82m),
            new("TSLA", 248.75m, 85_000_000, 790_000_000_000m, 0.055m, 0.68m, 0.75m),
            new("NFLX", 445.30m, 8_500_000, 195_000_000_000m, 0.038m, 0.65m, 0.72m),
            
            // Large Cap Finance
            new("JPM", 158.90m, 12_000_000, 465_000_000_000m, 0.028m, 0.71m, 0.78m),
            new("BAC", 32.45m, 45_000_000, 260_000_000_000m, 0.032m, 0.69m, 0.76m),
            new("WFC", 42.80m, 28_000_000, 155_000_000_000m, 0.035m, 0.67m, 0.74m),
            new("GS", 385.60m, 2_800_000, 132_000_000_000m, 0.042m, 0.64m, 0.71m),
            new("MS", 88.75m, 8_500_000, 145_000_000_000m, 0.038m, 0.62m, 0.69m),
            new("C", 52.30m, 18_000_000, 98_000_000_000m, 0.045m, 0.58m, 0.65m),
            
            // Large Cap Healthcare
            new("JNJ", 162.85m, 8_200_000, 425_000_000_000m, 0.018m, 0.55m, 0.68m),
            new("PFE", 28.95m, 32_000_000, 165_000_000_000m, 0.025m, 0.52m, 0.65m),
            new("UNH", 528.40m, 2_800_000, 495_000_000_000m, 0.022m, 0.73m, 0.85m),
            new("ABBV", 148.75m, 12_500_000, 262_000_000_000m, 0.024m, 0.59m, 0.72m),
            new("MRK", 108.60m, 11_000_000, 275_000_000_000m, 0.021m, 0.56m, 0.69m),
            new("LLY", 598.25m, 3_200_000, 565_000_000_000m, 0.028m, 0.81m, 0.88m),
            
            // Large Cap Consumer
            new("WMT", 158.20m, 8_500_000, 425_000_000_000m, 0.019m, 0.61m, 0.74m),
            new("PG", 152.80m, 6_800_000, 365_000_000_000m, 0.016m, 0.58m, 0.71m),
            new("KO", 59.45m, 15_000_000, 258_000_000_000m, 0.015m, 0.54m, 0.67m),
            new("PEP", 168.90m, 4_500_000, 235_000_000_000m, 0.017m, 0.57m, 0.70m),
            new("MCD", 285.75m, 2_800_000, 208_000_000_000m, 0.020m, 0.60m, 0.73m),
            new("NKE", 102.35m, 8_200_000, 158_000_000_000m, 0.032m, 0.53m, 0.66m),
            
            // Mid Cap Tech
            new("CRM", 248.60m, 6_500_000, 245_000_000_000m, 0.038m, 0.66m, 0.73m),
            new("ADBE", 568.75m, 2_200_000, 258_000_000_000m, 0.035m, 0.63m, 0.70m),
            new("ORCL", 112.85m, 12_000_000, 315_000_000_000m, 0.025m, 0.60m, 0.67m),
            new("INTC", 43.25m, 28_000_000, 185_000_000_000m, 0.042m, 0.45m, 0.52m),
            new("AMD", 138.90m, 45_000_000, 225_000_000_000m, 0.055m, 0.71m, 0.78m),
            new("QCOM", 148.75m, 8_800_000, 165_000_000_000m, 0.038m, 0.57m, 0.64m),
            new("AVGO", 925.40m, 1_800_000, 385_000_000_000m, 0.032m, 0.74m, 0.81m),
            new("TXN", 168.50m, 4_200_000, 152_000_000_000m, 0.028m, 0.56m, 0.63m),
            
            // Communication Services
            new("VZ", 38.75m, 18_000_000, 162_000_000_000m, 0.022m, 0.48m, 0.55m),
            new("T", 15.85m, 42_000_000, 115_000_000_000m, 0.025m, 0.42m, 0.49m),
            new("CMCSA", 42.60m, 15_000_000, 185_000_000_000m, 0.028m, 0.51m, 0.58m),
            new("DIS", 95.25m, 12_000_000, 175_000_000_000m, 0.045m, 0.49m, 0.56m),
            
            // Energy
            new("XOM", 108.75m, 18_000_000, 445_000_000_000m, 0.035m, 0.62m, 0.69m),
            new("CVX", 148.90m, 8_500_000, 285_000_000_000m, 0.032m, 0.59m, 0.66m),
            new("COP", 118.45m, 6_800_000, 145_000_000_000m, 0.042m, 0.56m, 0.63m),
            new("EOG", 128.60m, 4_200_000, 75_000_000_000m, 0.045m, 0.54m, 0.61m),
            
            // Industrials
            new("BA", 198.75m, 8_500_000, 118_000_000_000m, 0.055m, 0.47m, 0.54m),
            new("CAT", 285.40m, 3_200_000, 148_000_000_000m, 0.038m, 0.58m, 0.65m),
            new("GE", 108.25m, 45_000_000, 118_000_000_000m, 0.042m, 0.55m, 0.62m),
            new("MMM", 98.60m, 4_800_000, 55_000_000_000m, 0.028m, 0.46m, 0.53m),
            
            // Additional high-quality stocks to reach 200
            new("V", 258.75m, 6_500_000, 545_000_000_000m, 0.025m, 0.76m, 0.83m),
            new("MA", 418.90m, 3_200_000, 385_000_000_000m, 0.028m, 0.74m, 0.81m),
            new("HD", 328.45m, 3_800_000, 335_000_000_000m, 0.024m, 0.65m, 0.72m),
            new("COST", 685.20m, 2_200_000, 305_000_000_000m, 0.022m, 0.68m, 0.75m),
            new("AMGN", 268.75m, 2_800_000, 145_000_000_000m, 0.032m, 0.52m, 0.59m),
            new("GILD", 78.90m, 8_500_000, 98_000_000_000m, 0.035m, 0.48m, 0.55m),
            new("BIIB", 258.40m, 1_200_000, 37_000_000_000m, 0.055m, 0.44m, 0.51m),
            new("REGN", 885.60m, 850_000, 95_000_000_000m, 0.042m, 0.61m, 0.68m),
            new("ISRG", 385.75m, 1_800_000, 138_000_000_000m, 0.038m, 0.64m, 0.71m),
            new("MDLZ", 68.25m, 8_200_000, 92_000_000_000m, 0.018m, 0.49m, 0.56m),
            
            // Continue with more stocks to demonstrate the full 200...
            // (In the real system, this would be dynamically fetched from Polygon API)
            
            // Growth stocks
            new("SHOP", 68.75m, 12_000_000, 85_000_000_000m, 0.065m, 0.58m, 0.65m),
            new("SQ", 78.90m, 15_000_000, 45_000_000_000m, 0.072m, 0.55m, 0.62m),
            new("ROKU", 68.25m, 8_500_000, 7_500_000_000m, 0.085m, 0.42m, 0.49m),
            new("SNAP", 12.45m, 35_000_000, 19_500_000_000m, 0.078m, 0.38m, 0.45m),
            new("TWTR", 54.20m, 25_000_000, 41_000_000_000m, 0.068m, 0.41m, 0.48m),
            new("UBER", 58.75m, 22_000_000, 115_000_000_000m, 0.055m, 0.52m, 0.59m),
            new("LYFT", 15.80m, 18_000_000, 5_800_000_000m, 0.082m, 0.35m, 0.42m),
            new("ABNB", 138.90m, 6_500_000, 88_000_000_000m, 0.062m, 0.48m, 0.55m),
            new("DASH", 118.45m, 4_200_000, 42_000_000_000m, 0.075m, 0.44m, 0.51m),
            new("ZM", 68.75m, 12_000_000, 20_500_000_000m, 0.088m, 0.32m, 0.39m),
            
            // REITs and Utilities (some high-quality ones)
            new("AMT", 198.75m, 2_800_000, 92_000_000_000m, 0.025m, 0.56m, 0.63m),
            new("PLD", 128.60m, 4_200_000, 118_000_000_000m, 0.028m, 0.58m, 0.65m),
            new("CCI", 108.25m, 3_500_000, 48_000_000_000m, 0.032m, 0.54m, 0.61m),
            new("EQIX", 785.40m, 450_000, 72_000_000_000m, 0.035m, 0.61m, 0.68m),
            new("NEE", 68.90m, 8_500_000, 138_000_000_000m, 0.022m, 0.52m, 0.59m),
            new("SO", 68.75m, 6_200_000, 72_000_000_000m, 0.018m, 0.48m, 0.55m),
            new("D", 48.25m, 8_800_000, 42_000_000_000m, 0.020m, 0.45m, 0.52m),
            new("DUK", 98.60m, 4_500_000, 75_000_000_000m, 0.019m, 0.47m, 0.54m),
            
            // Additional stocks to reach 200 total
            // (This would continue with more real stock data...)
        };
    }
}

/// <summary>
/// Represents a stock candidate for universe selection
/// </summary>
public record StockCandidate(
    string Symbol,
    decimal Price,
    long AverageDailyVolume,
    decimal MarketCap,
    decimal Volatility,
    decimal MomentumScore,
    decimal LiquidityScore
);
