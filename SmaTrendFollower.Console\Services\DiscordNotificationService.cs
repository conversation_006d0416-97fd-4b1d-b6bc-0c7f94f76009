using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for sending Discord webhook notifications
/// </summary>
public sealed class DiscordNotificationService : IDiscordNotificationService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<DiscordNotificationService> _logger;
    private readonly string? _botToken;
    private readonly string? _channelId;

    public DiscordNotificationService(HttpClient httpClient, ILogger<DiscordNotificationService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _botToken = Environment.GetEnvironmentVariable("DISCORD_BOT_TOKEN");
        _channelId = Environment.GetEnvironmentVariable("DISCORD_CHANNEL_ID");

        // Set up authorization header if bot token is available
        if (!string.IsNullOrEmpty(_botToken))
        {
            _httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bot", _botToken);
        }
    }

    public async Task SendTradeNotificationAsync(string symbol, string action, decimal quantity, decimal price, decimal pnl)
    {
        if (string.IsNullOrEmpty(_botToken) || string.IsNullOrEmpty(_channelId))
        {
            _logger.LogDebug("Discord bot token or channel ID not configured, skipping trade notification");
            return;
        }

        try
        {
            var emoji = action.ToUpper() switch
            {
                "BUY" => "🟢",
                "SELL" => "🔴",
                "STOP" => "🛑",
                _ => "📊"
            };

            var pnlEmoji = pnl >= 0 ? "💰" : "📉";
            var pnlText = pnl != 0 ? $" | P&L: {pnlEmoji} ${pnl:N2}" : "";

            var message = $"{emoji} **{action.ToUpper()}** {symbol}\n" +
                         $"📈 Quantity: {quantity:N2}\n" +
                         $"💵 Price: ${price:N2}{pnlText}";

            await SendDiscordMessageAsync("Trade Execution", message, GetTradeColor(action));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending trade notification to Discord");
        }
    }

    public async Task SendPortfolioSnapshotAsync(decimal totalEquity, decimal dayPnl, decimal totalPnl, int positionCount)
    {
        if (string.IsNullOrEmpty(_botToken) || string.IsNullOrEmpty(_channelId))
        {
            _logger.LogDebug("Discord bot token or channel ID not configured, skipping portfolio snapshot");
            return;
        }

        try
        {
            var dayPnlEmoji = dayPnl >= 0 ? "📈" : "📉";
            var totalPnlEmoji = totalPnl >= 0 ? "💰" : "🔻";

            var message = $"💼 **Portfolio Snapshot**\n" +
                         $"💵 Total Equity: ${totalEquity:N2}\n" +
                         $"{dayPnlEmoji} Day P&L: ${dayPnl:N2} ({dayPnl/totalEquity:P2})\n" +
                         $"{totalPnlEmoji} Total P&L: ${totalPnl:N2}\n" +
                         $"📊 Active Positions: {positionCount}";

            var color = dayPnl >= 0 ? 0x00FF00 : 0xFF0000; // Green for profit, red for loss
            await SendDiscordMessageAsync("Portfolio Update", message, color);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending portfolio snapshot to Discord");
        }
    }

    public async Task SendVixSpikeAlertAsync(decimal currentVix, decimal threshold, string action)
    {
        if (string.IsNullOrEmpty(_botToken) || string.IsNullOrEmpty(_channelId))
        {
            _logger.LogDebug("Discord bot token or channel ID not configured, skipping VIX spike alert");
            return;
        }

        try
        {
            var message = $"⚠️ **VIX SPIKE ALERT** ⚠️\n" +
                         $"📊 Current VIX: {currentVix:N1}\n" +
                         $"🚨 Threshold: {threshold:N1}\n" +
                         $"🎯 Action: {action}";

            await SendDiscordMessageAsync("VIX Spike Alert", message, 0xFF6600); // Orange color
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending VIX spike alert to Discord");
        }
    }

    public async Task SendOptionsNotificationAsync(string strategy, string symbol, string details)
    {
        if (string.IsNullOrEmpty(_botToken) || string.IsNullOrEmpty(_channelId))
        {
            _logger.LogDebug("Discord bot token or channel ID not configured, skipping options notification");
            return;
        }

        try
        {
            var emoji = strategy.ToLower() switch
            {
                "protective put" => "🛡️",
                "covered call" => "📞",
                "delta efficient" => "⚡",
                _ => "🎯"
            };

            var message = $"{emoji} **{strategy.ToUpper()}** - {symbol}\n" +
                         $"📋 {details}";

            await SendDiscordMessageAsync("Options Strategy", message, 0x9932CC); // Purple color
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending options notification to Discord");
        }
    }

    /// <summary>
    /// Sends a comprehensive daily trading report to Discord
    /// </summary>
    public async Task SendDailyReportAsync(DailyTradingReport report)
    {
        if (string.IsNullOrEmpty(_botToken) || string.IsNullOrEmpty(_channelId))
        {
            _logger.LogDebug("Discord bot token or channel ID not configured, skipping daily report");
            return;
        }

        try
        {
            // Main summary embed
            var summaryEmbed = CreateDailySummaryEmbed(report);

            // Performance metrics embed
            var performanceEmbed = CreatePerformanceEmbed(report);

            // Warnings and alerts embed (if any)
            var alertsEmbed = CreateAlertsEmbed(report);

            var embeds = new List<object> { summaryEmbed, performanceEmbed };
            if (alertsEmbed != null)
            {
                embeds.Add(alertsEmbed);
            }

            var payload = new { embeds = embeds.ToArray() };
            var json = JsonSerializer.Serialize(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Use Discord API endpoint for sending messages to a specific channel
            var apiUrl = $"https://discord.com/api/v10/channels/{_channelId}/messages";
            var response = await _httpClient.PostAsync(apiUrl, content);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully sent daily trading report to Discord");
            }
            else
            {
                _logger.LogWarning("Discord webhook returned {StatusCode} for daily report", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send daily report to Discord");
        }
    }

    /// <summary>
    /// Sends system health summary to Discord
    /// </summary>
    public async Task SendHealthSummaryAsync(string healthStatus, List<string> warnings)
    {
        if (string.IsNullOrEmpty(_botToken) || string.IsNullOrEmpty(_channelId))
        {
            _logger.LogDebug("Discord bot token or channel ID not configured, skipping health summary");
            return;
        }

        try
        {
            var statusEmoji = healthStatus.ToLower() switch
            {
                "healthy" => "✅",
                "degraded" => "⚠️",
                "unhealthy" => "❌",
                _ => "❓"
            };

            var message = $"{statusEmoji} **System Health: {healthStatus.ToUpper()}**\n";

            if (warnings.Any())
            {
                message += "\n⚠️ **Warnings:**\n";
                foreach (var warning in warnings.Take(5))
                {
                    message += $"• {warning}\n";
                }
            }
            else
            {
                message += "\n🎯 All systems operating normally";
            }

            var color = healthStatus.ToLower() switch
            {
                "healthy" => 0x00FF00,   // Green
                "degraded" => 0xFFFF00,  // Yellow
                "unhealthy" => 0xFF0000, // Red
                _ => 0x808080            // Gray
            };

            await SendDiscordMessageAsync("System Health", message, color);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending health summary to Discord");
        }
    }

    public async Task SendMessageAsync(string message)
    {
        if (string.IsNullOrEmpty(_botToken) || string.IsNullOrEmpty(_channelId))
        {
            _logger.LogDebug("Discord bot token or channel ID not configured, skipping message");
            return;
        }

        try
        {
            await SendDiscordMessageAsync("SmaTrendFollower Alert", message, 0x0099FF); // Blue color
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending message to Discord");
        }
    }

    private async Task SendDiscordMessageAsync(string title, string description, int color)
    {
        if (string.IsNullOrEmpty(_botToken) || string.IsNullOrEmpty(_channelId))
            return;

        try
        {
            var embed = new
            {
                title = title,
                description = description,
                color = color,
                timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                footer = new
                {
                    text = "SmaTrendFollower Bot",
                    icon_url = "https://cdn.discordapp.com/embed/avatars/0.png"
                }
            };

            var payload = new
            {
                embeds = new[] { embed }
            };

            var json = JsonSerializer.Serialize(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Use Discord API endpoint for sending messages to a specific channel
            var apiUrl = $"https://discord.com/api/v10/channels/{_channelId}/messages";
            var response = await _httpClient.PostAsync(apiUrl, content);

            if (!response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Discord API returned {StatusCode}: {ReasonPhrase}. Response: {Response}",
                    response.StatusCode, response.ReasonPhrase, responseContent);
            }
            else
            {
                _logger.LogDebug("Successfully sent Discord notification: {Title}", title);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending Discord message");
        }
    }

    private static int GetTradeColor(string action)
    {
        return action.ToUpper() switch
        {
            "BUY" => 0x00FF00,    // Green
            "SELL" => 0xFF0000,   // Red
            "STOP" => 0xFF6600,   // Orange
            _ => 0x0099FF         // Blue
        };
    }

    /// <summary>
    /// Creates daily summary embed
    /// </summary>
    private object CreateDailySummaryEmbed(DailyTradingReport report)
    {
        var dayPnlEmoji = report.DayPnL >= 0 ? "📈" : "📉";
        var winRateEmoji = report.WinRate >= 0.6m ? "🎯" : report.WinRate >= 0.4m ? "⚖️" : "📉";

        var description = $"📅 **Trading Summary for {report.Date:yyyy-MM-dd}**\n\n" +
                         $"💰 **Day P&L:** {dayPnlEmoji} ${report.DayPnL:N2}\n" +
                         $"📊 **Total Trades:** {report.TotalTrades}\n" +
                         $"✅ **Winning Trades:** {report.WinningTrades}\n" +
                         $"{winRateEmoji} **Win Rate:** {report.WinRate:P1}\n" +
                         $"📈 **Avg Win:** ${report.AverageWin:N2}\n" +
                         $"📉 **Avg Loss:** ${report.AverageLoss:N2}\n" +
                         $"🎲 **Sharpe Ratio:** {report.SharpeRatio:F2}";

        var color = report.DayPnL >= 0 ? 0x00FF00 : 0xFF0000;

        return new
        {
            title = "📊 Daily Trading Report",
            description = description,
            color = color,
            timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            footer = new
            {
                text = "SmaTrendFollower Bot",
                icon_url = "https://cdn.discordapp.com/embed/avatars/0.png"
            }
        };
    }

    /// <summary>
    /// Creates performance metrics embed
    /// </summary>
    private object CreatePerformanceEmbed(DailyTradingReport report)
    {
        var description = $"⚡ **Performance Metrics**\n\n" +
                         $"🚀 **Signals Generated:** {report.SignalsGenerated}\n" +
                         $"✅ **Signals Executed:** {report.SignalsExecuted}\n" +
                         $"🎯 **Execution Rate:** {(report.SignalsGenerated > 0 ? (decimal)report.SignalsExecuted / report.SignalsGenerated : 0):P1}\n" +
                         $"⏱️ **Avg Response Time:** {report.AverageResponseTime:F1}ms\n" +
                         $"🔄 **API Calls:** {report.ApiCalls}\n" +
                         $"❌ **Errors:** {report.Errors}";

        if (report.TopPerformers.Any())
        {
            description += "\n\n🏆 **Top Performers:**\n";
            foreach (var performer in report.TopPerformers.Take(3))
            {
                description += $"• {performer.Symbol}: ${performer.PnL:N2}\n";
            }
        }

        return new
        {
            title = "⚡ Performance Metrics",
            description = description,
            color = 0x0099FF, // Blue
            timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        };
    }

    /// <summary>
    /// Creates alerts embed if there are warnings
    /// </summary>
    private object? CreateAlertsEmbed(DailyTradingReport report)
    {
        if (!report.Warnings.Any() && !report.Alerts.Any())
            return null;

        var description = "";

        if (report.Warnings.Any())
        {
            description += "⚠️ **Warnings:**\n";
            foreach (var warning in report.Warnings.Take(5))
            {
                description += $"• {warning}\n";
            }
        }

        if (report.Alerts.Any())
        {
            if (description.Length > 0) description += "\n";
            description += "🚨 **Alerts:**\n";
            foreach (var alert in report.Alerts.Take(5))
            {
                description += $"• {alert}\n";
            }
        }

        return new
        {
            title = "⚠️ Warnings & Alerts",
            description = description,
            color = 0xFF6600, // Orange
            timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        };
    }
}

/// <summary>
/// Daily trading report structure for Discord notifications
/// </summary>
public record DailyTradingReport(
    DateTime Date,
    decimal DayPnL,
    int TotalTrades,
    int WinningTrades,
    decimal WinRate,
    decimal AverageWin,
    decimal AverageLoss,
    decimal SharpeRatio,
    int SignalsGenerated,
    int SignalsExecuted,
    double AverageResponseTime,
    int ApiCalls,
    int Errors,
    List<SymbolPerformance> TopPerformers,
    List<string> Warnings,
    List<string> Alerts
);

/// <summary>
/// Symbol performance for reporting
/// </summary>
public record SymbolPerformance(
    string Symbol,
    decimal PnL,
    int Trades
);
