using Microsoft.Extensions.Logging;
using SmaTrendFollower.Data;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// SQLite-based implementation of stock bar caching service.
/// Provides efficient caching of Alpaca/Polygon stock data to minimize API calls.
/// Implements 1-year retention with differential updates.
/// </summary>
public sealed class StockBarCacheService : IStockBarCacheService
{
    private readonly StockBarCacheDbContext _dbContext;
    private readonly ILogger<StockBarCacheService> _logger;

    public StockBarCacheService(StockBarCacheDbContext dbContext, ILogger<StockBarCacheService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<IReadOnlyList<IBar>> GetCachedBarsAsync(string symbol, string timeFrame, DateTime startDate, DateTime endDate)
    {
        try
        {
            _logger.LogDebug("Retrieving cached bars for {Symbol} {TimeFrame} from {StartDate} to {EndDate}", 
                symbol, timeFrame, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

            var cachedBars = await _dbContext.GetCachedBarsAsync(symbol, timeFrame, startDate, endDate);
            var bars = cachedBars.Select(cb => cb.ToIBar()).Cast<IBar>().ToList();

            _logger.LogDebug("Found {Count} cached bars for {Symbol} {TimeFrame}", bars.Count, symbol, timeFrame);
            return bars;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cached bars for {Symbol} {TimeFrame}", symbol, timeFrame);
            return new List<IBar>();
        }
    }

    public async Task CacheBarsAsync(string symbol, string timeFrame, IEnumerable<IBar> bars)
    {
        try
        {
            var barsList = bars.ToList();
            if (!barsList.Any())
            {
                _logger.LogDebug("No bars to cache for {Symbol} {TimeFrame}", symbol, timeFrame);
                return;
            }

            _logger.LogDebug("Caching {Count} bars for {Symbol} {TimeFrame}", barsList.Count, symbol, timeFrame);

            await _dbContext.AddOrUpdateCachedBarsAsync(symbol, timeFrame, barsList);

            _logger.LogInformation("Successfully cached {Count} bars for {Symbol} {TimeFrame}", barsList.Count, symbol, timeFrame);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching bars for {Symbol} {TimeFrame}", symbol, timeFrame);
            throw;
        }
    }

    public async Task<(DateTime startDate, DateTime endDate)?> GetMissingDateRangeAsync(string symbol, string timeFrame, DateTime requestedStartDate, DateTime requestedEndDate)
    {
        try
        {
            var latestCachedDate = await GetLatestCachedDateAsync(symbol, timeFrame);
            var earliestCachedDate = await GetEarliestCachedDateAsync(symbol, timeFrame);

            // If no cached data, fetch entire range
            if (!latestCachedDate.HasValue || !earliestCachedDate.HasValue)
            {
                _logger.LogDebug("No cached data for {Symbol} {TimeFrame}, need to fetch entire range", symbol, timeFrame);
                return (requestedStartDate, requestedEndDate);
            }

            // If cache covers the entire requested range, no fetch needed
            if (earliestCachedDate.Value <= requestedStartDate && latestCachedDate.Value >= requestedEndDate)
            {
                _logger.LogDebug("Cache for {Symbol} {TimeFrame} covers entire requested range", symbol, timeFrame);
                return null;
            }

            // Determine what range needs to be fetched
            DateTime fetchStartDate = requestedStartDate;
            DateTime fetchEndDate = requestedEndDate;

            // If we have some data, only fetch what's missing
            if (latestCachedDate.Value >= requestedStartDate)
            {
                // We have recent data, only fetch newer data
                fetchStartDate = latestCachedDate.Value.AddDays(1);
                if (fetchStartDate > requestedEndDate)
                {
                    return null; // Cache is up to date
                }
            }

            if (earliestCachedDate.Value <= requestedEndDate)
            {
                // We have older data, only fetch older data if needed
                var earliestMinusOne = earliestCachedDate.Value.AddDays(-1);
                fetchEndDate = requestedEndDate < earliestMinusOne ? requestedEndDate : earliestMinusOne;
                if (fetchEndDate < requestedStartDate)
                {
                    fetchEndDate = requestedEndDate;
                }
            }

            // Final validation to ensure startDate < endDate
            if (fetchStartDate >= fetchEndDate)
            {
                _logger.LogWarning("Invalid date range calculated for {Symbol} {TimeFrame}: startDate={StartDate}, endDate={EndDate}. Using original range.",
                    symbol, timeFrame, fetchStartDate.ToString("yyyy-MM-dd"), fetchEndDate.ToString("yyyy-MM-dd"));
                return (requestedStartDate, requestedEndDate);
            }

            _logger.LogDebug("Missing data for {Symbol} {TimeFrame}: need to fetch {StartDate} to {EndDate}",
                symbol, timeFrame, fetchStartDate.ToString("yyyy-MM-dd"), fetchEndDate.ToString("yyyy-MM-dd"));

            return (fetchStartDate, fetchEndDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error determining missing date range for {Symbol} {TimeFrame}", symbol, timeFrame);
            // On error, fetch entire range to be safe
            return (requestedStartDate, requestedEndDate);
        }
    }

    public async Task<DateTime?> GetLatestCachedDateAsync(string symbol, string timeFrame)
    {
        try
        {
            return await _dbContext.GetLatestCachedDateAsync(symbol, timeFrame);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting latest cached date for {Symbol} {TimeFrame}", symbol, timeFrame);
            return null;
        }
    }

    public async Task<DateTime?> GetEarliestCachedDateAsync(string symbol, string timeFrame)
    {
        try
        {
            return await _dbContext.GetEarliestCachedDateAsync(symbol, timeFrame);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting earliest cached date for {Symbol} {TimeFrame}", symbol, timeFrame);
            return null;
        }
    }

    public async Task<bool> IsCacheFreshAsync(string symbol, string timeFrame, DateTime requestedEndDate)
    {
        try
        {
            var latestCachedDate = await GetLatestCachedDateAsync(symbol, timeFrame);
            
            if (!latestCachedDate.HasValue)
                return false;

            // Consider cache fresh if it has data up to the requested end date
            var isFresh = latestCachedDate.Value >= requestedEndDate;
            
            _logger.LogDebug("Cache freshness for {Symbol} {TimeFrame}: latest={LatestDate}, requested={RequestedDate}, fresh={IsFresh}",
                symbol, timeFrame, latestCachedDate.Value.ToString("yyyy-MM-dd"), requestedEndDate.ToString("yyyy-MM-dd"), isFresh);
            
            return isFresh;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache freshness for {Symbol} {TimeFrame}", symbol, timeFrame);
            return false;
        }
    }

    public async Task PerformMaintenanceAsync(int retainDays = 365)
    {
        try
        {
            _logger.LogInformation("Starting stock cache maintenance, retaining {RetainDays} days of data", retainDays);

            await _dbContext.CleanupOldDataAsync(retainDays);

            _logger.LogInformation("Stock cache maintenance completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during stock cache maintenance");
            throw;
        }
    }

    public async Task InitializeCacheAsync()
    {
        try
        {
            _logger.LogDebug("Initializing stock cache database");

            await _dbContext.EnsureDatabaseCreatedAsync();

            _logger.LogDebug("Stock cache database initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing stock cache database");
            throw;
        }
    }

    public async Task<IDictionary<string, CacheStats>> GetCacheStatsAsync()
    {
        try
        {
            return await _dbContext.GetCacheStatsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache statistics");
            return new Dictionary<string, CacheStats>();
        }
    }
}
