using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Tick-by-tick volatility guard service for flash crash and microstructure instability protection
/// Detects if tick-by-tick volatility spikes above N stddev intraday with dynamic thresholds
/// Temporarily blocks trades to avoid flash crashes or microstructure instability
/// </summary>
public interface ITickVolatilityGuard : IDisposable
{
    // === Events ===
    
    /// <summary>
    /// Fired when volatility spike is detected
    /// </summary>
    event EventHandler<VolatilitySpikeDe​tectedEventArgs>? VolatilitySpikeDetected;
    
    /// <summary>
    /// Fired when trading is blocked due to volatility
    /// </summary>
    event EventHandler<TradingBlockedEventArgs>? TradingBlocked;
    
    /// <summary>
    /// Fired when trading block is lifted
    /// </summary>
    event EventHandler<TradingUnblockedEventArgs>? TradingUnblocked;
    
    // === Core Methods ===
    
    /// <summary>
    /// Start volatility monitoring for specified symbols
    /// </summary>
    Task StartMonitoringAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stop volatility monitoring
    /// </summary>
    Task StopMonitoringAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Check if trading is currently blocked for a symbol
    /// </summary>
    bool IsTradingBlocked(string symbol);
    
    /// <summary>
    /// Check if trading is blocked for any monitored symbol
    /// </summary>
    bool IsAnyTradingBlocked();
    
    /// <summary>
    /// Get current volatility metrics for a symbol
    /// </summary>
    Task<VolatilityMetrics?> GetVolatilityMetricsAsync(string symbol);
    
    /// <summary>
    /// Get list of currently blocked symbols
    /// </summary>
    IEnumerable<string> GetBlockedSymbols();
    
    /// <summary>
    /// Manually override trading block for a symbol
    /// </summary>
    Task OverrideBlockAsync(string symbol, bool blocked, string reason);
    
    /// <summary>
    /// Update volatility guard configuration
    /// </summary>
    Task UpdateConfigurationAsync(VolatilityGuardConfig config);
    
    // === Status ===
    
    /// <summary>
    /// Get monitoring status
    /// </summary>
    VolatilityGuardStatus GetStatus();
    
    /// <summary>
    /// Get list of monitored symbols
    /// </summary>
    IEnumerable<string> GetMonitoredSymbols();
}

/// <summary>
/// Volatility guard configuration with dynamic thresholds
/// </summary>
public record VolatilityGuardConfig(
    int VolatilityWindowMinutes = 5,
    decimal BaseStdDevThreshold = 3.0m,
    decimal MaxStdDevThreshold = 6.0m,
    decimal MinStdDevThreshold = 2.0m,
    int MinTicksRequired = 20,
    TimeSpan BlockDuration = default,
    decimal AccountSizeMultiplier = 1.0m,
    decimal VixAdjustmentFactor = 0.1m,
    bool EnableDynamicThresholds = true,
    decimal FlashCrashThreshold = 10.0m,
    TimeSpan CooldownPeriod = default
)
{
    public TimeSpan BlockDuration { get; init; } = BlockDuration == default ? TimeSpan.FromMinutes(2) : BlockDuration;
    public TimeSpan CooldownPeriod { get; init; } = CooldownPeriod == default ? TimeSpan.FromMinutes(1) : CooldownPeriod;
}

/// <summary>
/// Volatility metrics for a symbol
/// </summary>
public record VolatilityMetrics(
    string Symbol,
    decimal CurrentVolatility,
    decimal AverageVolatility,
    decimal StandardDeviation,
    decimal ZScore,
    decimal DynamicThreshold,
    int TickCount,
    DateTime LastUpdate,
    bool IsBlocked,
    string? BlockReason
);

/// <summary>
/// Volatility guard status
/// </summary>
public enum VolatilityGuardStatus
{
    Stopped,
    Starting,
    Active,
    Error
}

/// <summary>
/// Event args for volatility spike detection
/// </summary>
public class VolatilitySpikeDe​tectedEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required decimal VolatilityLevel { get; init; }
    public required decimal Threshold { get; init; }
    public required decimal ZScore { get; init; }
    public required DateTime Timestamp { get; init; }
    public required VolatilitySpikeType SpikeType { get; init; }
}

/// <summary>
/// Event args for trading blocked
/// </summary>
public class TradingBlockedEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required string Reason { get; init; }
    public required TimeSpan BlockDuration { get; init; }
    public required DateTime BlockedAt { get; init; }
    public required VolatilityMetrics Metrics { get; init; }
}

/// <summary>
/// Event args for trading unblocked
/// </summary>
public class TradingUnblockedEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required string Reason { get; init; }
    public required TimeSpan BlockedDuration { get; init; }
    public required DateTime UnblockedAt { get; init; }
}

/// <summary>
/// Types of volatility spikes
/// </summary>
public enum VolatilitySpikeType
{
    Normal,
    Moderate,
    Severe,
    FlashCrash
}
