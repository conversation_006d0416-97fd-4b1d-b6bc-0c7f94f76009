#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Comprehensive test performance optimization script
.DESCRIPTION
    This script identifies and fixes test performance issues including:
    - Tests making real HTTP requests
    - Tests using real databases instead of in-memory
    - Tests with excessive timeouts
    - Tests with unnecessary delays
.EXAMPLE
    ./fix-test-performance.ps1
    ./fix-test-performance.ps1 -DryRun
#>

param(
    [switch]$DryRun = $false,
    [switch]$Verbose = $false
)

Write-Host "🚀 Starting Test Performance Optimization..." -ForegroundColor Cyan

# Define patterns that indicate performance issues
$PerformanceIssuePatterns = @{
    # Real HTTP requests
    'RealHttpRequests' = @(
        'new HttpClient\(\)',
        'HttpClient\..*Async\(',
        'Invoke-RestMethod',
        'WebRequest',
        'HttpRequestMessage'
    )
    
    # Real database connections
    'RealDatabaseConnections' = @(
        'new SqlConnection',
        'Database\.EnsureCreated\(\)',
        'UseSqlServer\(',
        'UseNpgsql\(',
        'UseMySql\('
    )
    
    # Excessive delays
    'ExcessiveDelays' = @(
        'Task\.Delay\([0-9]{4,}',  # Delays > 1000ms
        'Thread\.Sleep\([0-9]{4,}',
        'await.*Delay\([0-9]{4,}'
    )
    
    # Long timeouts
    'LongTimeouts' = @(
        'TestTimeout\(.*[0-9]{5,}',  # Timeouts > 10 seconds
        'Timeout.*[0-9]{5,}',
        'TimeSpan\.FromSeconds\([3-9][0-9]',  # > 30 seconds
        'TimeSpan\.FromMinutes\([1-9]'  # > 1 minute
    )
}

# Define fixes for common patterns
$PerformanceFixes = @{
    # HTTP Client fixes
    'new HttpClient()' = 'Mock<HttpClient>()'
    'HttpClient client = new HttpClient()' = 'var mockHttpClient = new Mock<HttpClient>()'
    
    # Database fixes
    'Database.EnsureCreated()' = '// Database.EnsureCreated() - using in-memory database'
    'UseSqlServer(' = 'UseInMemoryDatabase('
    
    # Timeout fixes
    'TestTimeouts.Network' = 'TestTimeouts.Unit'
    'TestTimeouts.Database' = 'TestTimeouts.Unit'
    'TestTimeouts.Integration' = 'TestTimeouts.Unit'
    
    # Delay fixes
    'Task.Delay(5000)' = 'Task.Delay(10)'
    'Task.Delay(3000)' = 'Task.Delay(10)'
    'Task.Delay(2000)' = 'Task.Delay(10)'
    'Task.Delay(1000)' = 'Task.Delay(10)'
}

function Test-FileForPerformanceIssues {
    param(
        [string]$FilePath,
        [string]$Content
    )
    
    $issues = @()
    
    foreach ($category in $PerformanceIssuePatterns.Keys) {
        foreach ($pattern in $PerformanceIssuePatterns[$category]) {
            if ($Content -match $pattern) {
                $matches = [regex]::Matches($Content, $pattern)
                foreach ($match in $matches) {
                    $lineNumber = ($Content.Substring(0, $match.Index) -split "`n").Count
                    $issues += @{
                        Category = $category
                        Pattern = $pattern
                        Match = $match.Value
                        LineNumber = $lineNumber
                        File = $FilePath
                    }
                }
            }
        }
    }
    
    return $issues
}

function Fix-PerformanceIssues {
    param(
        [string]$FilePath,
        [string]$Content
    )
    
    $fixedContent = $Content
    $changesMade = $false
    
    # Apply automatic fixes
    foreach ($pattern in $PerformanceFixes.Keys) {
        $replacement = $PerformanceFixes[$pattern]
        if ($fixedContent -match [regex]::Escape($pattern)) {
            $fixedContent = $fixedContent -replace [regex]::Escape($pattern), $replacement
            $changesMade = $true
            if ($Verbose) {
                Write-Host "  Fixed: $pattern -> $replacement" -ForegroundColor Green
            }
        }
    }
    
    # Fix specific test timeout issues
    $fixedContent = $fixedContent -replace '\[TestTimeout\(TestTimeouts\.Database\)\]\s*\[Trait\("Category", TestCategories\.Database\)\]', '[TestTimeout(TestTimeouts.Unit)]`n    [Trait("Category", TestCategories.Database)]'
    $fixedContent = $fixedContent -replace '\[TestTimeout\(TestTimeouts\.Network\)\]\s*\[Trait\("Category", TestCategories\.Network\)\]', '[TestTimeout(TestTimeouts.Unit)]`n    [Trait("Category", TestCategories.Network)]'
    
    # Fix excessive Task.Delay calls
    $fixedContent = $fixedContent -replace 'Task\.Delay\(([0-9]{4,})\)', 'Task.Delay(10) // Optimized from $1ms'
    $fixedContent = $fixedContent -replace 'await Task\.Delay\(([0-9]{4,})\)', 'await Task.Delay(10) // Optimized from $1ms'
    
    if ($fixedContent -ne $Content) {
        $changesMade = $true
    }
    
    return @{
        Content = $fixedContent
        Changed = $changesMade
    }
}

# Get all test files
$testFiles = Get-ChildItem -Path "SmaTrendFollower.Tests" -Filter "*.cs" -Recurse | Where-Object { 
    $_.FullName -notlike "*\bin\*" -and $_.FullName -notlike "*\obj\*" 
}

Write-Host "Found $($testFiles.Count) test files to analyze" -ForegroundColor Cyan

$totalIssues = 0
$totalFilesFixed = 0
$issuesByCategory = @{}

foreach ($file in $testFiles) {
    Write-Host "Analyzing: $($file.Name)" -ForegroundColor Yellow
    
    try {
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        
        # Analyze for performance issues
        $issues = Test-FileForPerformanceIssues -FilePath $file.FullName -Content $content
        
        if ($issues.Count -gt 0) {
            $totalIssues += $issues.Count
            
            Write-Host "  Found $($issues.Count) performance issues:" -ForegroundColor Red
            
            foreach ($issue in $issues) {
                if (-not $issuesByCategory.ContainsKey($issue.Category)) {
                    $issuesByCategory[$issue.Category] = 0
                }
                $issuesByCategory[$issue.Category]++
                
                if ($Verbose) {
                    Write-Host "    Line $($issue.LineNumber): $($issue.Category) - $($issue.Match)" -ForegroundColor Red
                }
            }
            
            # Apply fixes
            $fixResult = Fix-PerformanceIssues -FilePath $file.FullName -Content $content
            
            if ($fixResult.Changed) {
                if (-not $DryRun) {
                    Set-Content -Path $file.FullName -Value $fixResult.Content -Encoding UTF8 -NoNewline
                    Write-Host "  ✅ Fixed performance issues" -ForegroundColor Green
                } else {
                    Write-Host "  🔍 Would fix performance issues (dry run)" -ForegroundColor Magenta
                }
                $totalFilesFixed++
            }
        } else {
            if ($Verbose) {
                Write-Host "  ✅ No performance issues found" -ForegroundColor Green
            }
        }
    }
    catch {
        Write-Host "  ❌ Error processing file: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Summary
Write-Host "`n📊 Performance Optimization Summary:" -ForegroundColor Cyan
Write-Host "Total files analyzed: $($testFiles.Count)" -ForegroundColor White
Write-Host "Total performance issues found: $totalIssues" -ForegroundColor White
Write-Host "Files with fixes applied: $totalFilesFixed" -ForegroundColor White

if ($issuesByCategory.Count -gt 0) {
    Write-Host "`nIssues by category:" -ForegroundColor Yellow
    foreach ($category in $issuesByCategory.Keys) {
        Write-Host "  $category`: $($issuesByCategory[$category])" -ForegroundColor White
    }
}

if ($DryRun) {
    Write-Host "`n⚠️  This was a dry run. Use -DryRun:`$false to apply fixes." -ForegroundColor Yellow
}

Write-Host "`n🎯 Next steps:" -ForegroundColor Cyan
Write-Host "1. Review the fixed files for correctness" -ForegroundColor White
Write-Host "2. Run tests to ensure they still pass: dotnet test --filter 'Category!=Integration'" -ForegroundColor White
Write-Host "3. Check for any remaining slow tests" -ForegroundColor White

Write-Host "`n✅ Test performance optimization complete!" -ForegroundColor Green
