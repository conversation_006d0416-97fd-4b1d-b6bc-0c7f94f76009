using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Comprehensive system health monitoring service that tracks API connectivity,
/// database health, memory usage, and overall system performance.
/// </summary>
public sealed class SystemHealthService : BackgroundService, ISystemHealthService
{
    private readonly IMarketDataService _marketDataService;
    private readonly ILiveStateStore _liveStateStore;
    private readonly ITradingMetricsService _metricsService;
    private readonly ILogger<SystemHealthService> _logger;
    private readonly HealthServiceConfig _config;

    private readonly ConcurrentDictionary<string, HealthCheckResult> _healthChecks = new();
    private readonly ConcurrentQueue<HealthEvent> _healthEvents = new();
    private SystemHealthStatus _currentStatus = SystemHealthStatus.Unknown;

    // Events for health status changes
    public event EventHandler<HealthStatusChangedEventArgs>? HealthStatusChanged;
    public event EventHandler<HealthEventOccurredEventArgs>? HealthEventOccurred;

    public SystemHealthService(
        IMarketDataService marketDataService,
        ILiveStateStore liveStateStore,
        ITradingMetricsService metricsService,
        ILogger<SystemHealthService> logger,
        HealthServiceConfig? config = null)
    {
        _marketDataService = marketDataService;
        _liveStateStore = liveStateStore;
        _metricsService = metricsService;
        _logger = logger;
        _config = config ?? new HealthServiceConfig();
    }

    /// <summary>
    /// Gets current system health status
    /// </summary>
    public SystemHealthStatus GetCurrentStatus()
    {
        return _currentStatus;
    }

    /// <summary>
    /// Gets detailed health report
    /// </summary>
    public Task<HealthReport> GetHealthReportAsync()
    {
        var checks = new Dictionary<string, HealthCheckResult>(_healthChecks);
        var overallStatus = DetermineOverallStatus(checks.Values);
        var events = _healthEvents.TakeLast(50).ToList();

        return Task.FromResult(new HealthReport(
            overallStatus,
            checks,
            events,
            DateTime.UtcNow
        ));
    }

    /// <summary>
    /// Performs manual health check
    /// </summary>
    public async Task<bool> PerformHealthCheckAsync()
    {
        try
        {
            await RunAllHealthChecksAsync();
            await RecordHealthMetricsAsync();
            return _currentStatus == SystemHealthStatus.Healthy;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing manual health check");
            return false;
        }
    }

    /// <summary>
    /// Gets recent health events
    /// </summary>
    public List<HealthEvent> GetRecentEvents(int count = 20)
    {
        return _healthEvents.TakeLast(count).ToList();
    }

    /// <summary>
    /// Background service execution
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("SystemHealthService started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await RunAllHealthChecksAsync();
                await RecordHealthMetricsAsync();
                await CleanupOldEventsAsync();
                
                await Task.Delay(_config.CheckInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in health monitoring");
                await RecordHealthEventAsync("SystemError", "Health monitoring error", HealthEventSeverity.Critical);
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }

        _logger.LogInformation("SystemHealthService stopped");
    }

    /// <summary>
    /// Runs all configured health checks
    /// </summary>
    private async Task RunAllHealthChecksAsync()
    {
        var tasks = new List<Task>
        {
            CheckMarketDataServiceAsync(),
            CheckLiveStateStoreAsync(),
            CheckSystemResourcesAsync(),
            CheckTradingSystemAsync(),
            CheckNetworkConnectivityAsync()
        };

        await Task.WhenAll(tasks);

        // Update overall status
        var previousStatus = _currentStatus;
        _currentStatus = DetermineOverallStatus(_healthChecks.Values);

        if (previousStatus != _currentStatus)
        {
            await RecordHealthEventAsync("StatusChange", 
                $"System status changed from {previousStatus} to {_currentStatus}", 
                GetSeverityForStatus(_currentStatus));

            HealthStatusChanged?.Invoke(this, new HealthStatusChangedEventArgs(
                previousStatus, _currentStatus, DateTime.UtcNow));
        }
    }

    /// <summary>
    /// Checks market data service health
    /// </summary>
    private async Task CheckMarketDataServiceAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            // Test API connectivity with a simple request
            var testSymbol = "SPY";
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-1);
            
            var response = await _marketDataService.GetStockBarsAsync(testSymbol, startDate, endDate);
            stopwatch.Stop();

            var isHealthy = response.Items.Any();
            var responseTime = stopwatch.Elapsed;

            var check = new HealthCheckResult(
                "MarketDataService",
                isHealthy ? HealthCheckStatus.Healthy : HealthCheckStatus.Unhealthy,
                $"Response time: {responseTime.TotalMilliseconds:F0}ms, Data points: {response.Items.Count()}",
                responseTime,
                DateTime.UtcNow
            );

            _healthChecks["MarketDataService"] = check;

            if (!isHealthy)
            {
                await RecordHealthEventAsync("MarketDataService", "API health check failed", HealthEventSeverity.High);
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            var check = new HealthCheckResult(
                "MarketDataService",
                HealthCheckStatus.Unhealthy,
                $"Error: {ex.Message}",
                stopwatch.Elapsed,
                DateTime.UtcNow
            );

            _healthChecks["MarketDataService"] = check;
            await RecordHealthEventAsync("MarketDataService", $"API error: {ex.Message}", HealthEventSeverity.Critical);
        }
    }

    /// <summary>
    /// Checks live state store health
    /// </summary>
    private async Task CheckLiveStateStoreAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            // Test Redis connectivity
            var testKey = "health_check_test";
            var testValue = DateTime.UtcNow.ToString();
            
            await _liveStateStore.SetMarketStateAsync(testKey, testValue, TimeSpan.FromMinutes(1));
            var retrieved = await _liveStateStore.GetMarketStateAsync<string>(testKey);
            
            stopwatch.Stop();

            var isHealthy = retrieved == testValue;
            var responseTime = stopwatch.Elapsed;

            var check = new HealthCheckResult(
                "LiveStateStore",
                isHealthy ? HealthCheckStatus.Healthy : HealthCheckStatus.Unhealthy,
                $"Response time: {responseTime.TotalMilliseconds:F0}ms",
                responseTime,
                DateTime.UtcNow
            );

            _healthChecks["LiveStateStore"] = check;

            if (!isHealthy)
            {
                await RecordHealthEventAsync("LiveStateStore", "Redis health check failed", HealthEventSeverity.High);
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            var check = new HealthCheckResult(
                "LiveStateStore",
                HealthCheckStatus.Unhealthy,
                $"Error: {ex.Message}",
                stopwatch.Elapsed,
                DateTime.UtcNow
            );

            _healthChecks["LiveStateStore"] = check;
            await RecordHealthEventAsync("LiveStateStore", $"Redis error: {ex.Message}", HealthEventSeverity.Critical);
        }
    }

    /// <summary>
    /// Checks system resource health
    /// </summary>
    private async Task CheckSystemResourcesAsync()
    {
        await Task.Run(() =>
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var memoryMB = process.WorkingSet64 / 1024 / 1024;
                var threadCount = process.Threads.Count;

                var memoryHealthy = memoryMB < _config.MaxMemoryMB;
                var threadHealthy = threadCount < _config.MaxThreadCount;

                var status = memoryHealthy && threadHealthy ? HealthCheckStatus.Healthy : 
                           memoryHealthy || threadHealthy ? HealthCheckStatus.Degraded : HealthCheckStatus.Unhealthy;

                var details = $"Memory: {memoryMB}MB, Threads: {threadCount}";

                var check = new HealthCheckResult(
                    "SystemResources",
                    status,
                    details,
                    TimeSpan.Zero,
                    DateTime.UtcNow
                );

                _healthChecks["SystemResources"] = check;

                if (!memoryHealthy)
                {
                    RecordHealthEventAsync("SystemResources", $"High memory usage: {memoryMB}MB", HealthEventSeverity.Medium).Wait();
                }

                if (!threadHealthy)
                {
                    RecordHealthEventAsync("SystemResources", $"High thread count: {threadCount}", HealthEventSeverity.Medium).Wait();
                }
            }
            catch (Exception ex)
            {
                var check = new HealthCheckResult(
                    "SystemResources",
                    HealthCheckStatus.Unhealthy,
                    $"Error: {ex.Message}",
                    TimeSpan.Zero,
                    DateTime.UtcNow
                );

                _healthChecks["SystemResources"] = check;
            }
        });
    }

    /// <summary>
    /// Checks trading system health
    /// </summary>
    private async Task CheckTradingSystemAsync()
    {
        try
        {
            var stats = await _metricsService.GetTradingStatisticsAsync();
            var kpis = _metricsService.GetKPIs();

            var recentActivity = stats.GeneratedAt > DateTime.UtcNow.AddHours(-1);
            var goodPerformance = stats.WinRate > 0.3m || stats.TotalTrades < 10; // Allow for new systems

            var status = recentActivity && goodPerformance ? HealthCheckStatus.Healthy :
                        recentActivity || goodPerformance ? HealthCheckStatus.Degraded : HealthCheckStatus.Unhealthy;

            var details = $"Trades: {stats.TotalTrades}, Signals: {stats.TotalSignals}, Win Rate: {stats.WinRate:P1}";

            var check = new HealthCheckResult(
                "TradingSystem",
                status,
                details,
                TimeSpan.Zero,
                DateTime.UtcNow
            );

            _healthChecks["TradingSystem"] = check;
        }
        catch (Exception ex)
        {
            var check = new HealthCheckResult(
                "TradingSystem",
                HealthCheckStatus.Unhealthy,
                $"Error: {ex.Message}",
                TimeSpan.Zero,
                DateTime.UtcNow
            );

            _healthChecks["TradingSystem"] = check;
        }
    }

    /// <summary>
    /// Checks network connectivity
    /// </summary>
    private async Task CheckNetworkConnectivityAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            using var client = new HttpClient();
            client.Timeout = TimeSpan.FromSeconds(10);
            
            var response = await client.GetAsync("https://api.alpaca.markets/v2/account");
            stopwatch.Stop();

            var isHealthy = response.IsSuccessStatusCode || response.StatusCode == System.Net.HttpStatusCode.Unauthorized;
            var responseTime = stopwatch.Elapsed;

            var check = new HealthCheckResult(
                "NetworkConnectivity",
                isHealthy ? HealthCheckStatus.Healthy : HealthCheckStatus.Unhealthy,
                $"Response time: {responseTime.TotalMilliseconds:F0}ms, Status: {response.StatusCode}",
                responseTime,
                DateTime.UtcNow
            );

            _healthChecks["NetworkConnectivity"] = check;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            var check = new HealthCheckResult(
                "NetworkConnectivity",
                HealthCheckStatus.Unhealthy,
                $"Error: {ex.Message}",
                stopwatch.Elapsed,
                DateTime.UtcNow
            );

            _healthChecks["NetworkConnectivity"] = check;
            await RecordHealthEventAsync("NetworkConnectivity", $"Network error: {ex.Message}", HealthEventSeverity.High);
        }
    }

    /// <summary>
    /// Records health metrics
    /// </summary>
    private async Task RecordHealthMetricsAsync()
    {
        foreach (var check in _healthChecks.Values)
        {
            await _metricsService.RecordSystemMetricAsync(
                $"health_check_{check.Name.ToLowerInvariant()}",
                (decimal)check.Status,
                "status"
            );

            if (check.ResponseTime > TimeSpan.Zero)
            {
                await _metricsService.RecordSystemMetricAsync(
                    $"health_response_time_{check.Name.ToLowerInvariant()}",
                    (decimal)check.ResponseTime.TotalMilliseconds,
                    "ms"
                );
            }
        }
    }

    /// <summary>
    /// Records a health event
    /// </summary>
    private Task RecordHealthEventAsync(string component, string message, HealthEventSeverity severity)
    {
        var healthEvent = new HealthEvent(
            component,
            message,
            severity,
            DateTime.UtcNow
        );

        _healthEvents.Enqueue(healthEvent);

        // Log based on severity
        switch (severity)
        {
            case HealthEventSeverity.Critical:
                _logger.LogCritical("Health Event [{Component}]: {Message}", component, message);
                break;
            case HealthEventSeverity.High:
                _logger.LogError("Health Event [{Component}]: {Message}", component, message);
                break;
            case HealthEventSeverity.Medium:
                _logger.LogWarning("Health Event [{Component}]: {Message}", component, message);
                break;
            case HealthEventSeverity.Low:
                _logger.LogInformation("Health Event [{Component}]: {Message}", component, message);
                break;
        }

        HealthEventOccurred?.Invoke(this, new HealthEventOccurredEventArgs(healthEvent));

        return Task.CompletedTask;
    }

    /// <summary>
    /// Determines overall system status from individual checks
    /// </summary>
    private SystemHealthStatus DetermineOverallStatus(IEnumerable<HealthCheckResult> checks)
    {
        var checkList = checks.ToList();
        
        if (!checkList.Any())
            return SystemHealthStatus.Unknown;

        if (checkList.Any(c => c.Status == HealthCheckStatus.Unhealthy))
            return SystemHealthStatus.Unhealthy;

        if (checkList.Any(c => c.Status == HealthCheckStatus.Degraded))
            return SystemHealthStatus.Degraded;

        return SystemHealthStatus.Healthy;
    }

    /// <summary>
    /// Gets event severity for status
    /// </summary>
    private HealthEventSeverity GetSeverityForStatus(SystemHealthStatus status)
    {
        return status switch
        {
            SystemHealthStatus.Healthy => HealthEventSeverity.Low,
            SystemHealthStatus.Degraded => HealthEventSeverity.Medium,
            SystemHealthStatus.Unhealthy => HealthEventSeverity.High,
            _ => HealthEventSeverity.Medium
        };
    }

    /// <summary>
    /// Cleans up old health events
    /// </summary>
    private async Task CleanupOldEventsAsync()
    {
        await Task.Run(() =>
        {
            while (_healthEvents.Count > _config.MaxEventHistory)
            {
                _healthEvents.TryDequeue(out _);
            }
        });
    }
}

/// <summary>
/// Interface for system health service
/// </summary>
public interface ISystemHealthService
{
    event EventHandler<HealthStatusChangedEventArgs>? HealthStatusChanged;
    event EventHandler<HealthEventOccurredEventArgs>? HealthEventOccurred;

    SystemHealthStatus GetCurrentStatus();
    Task<HealthReport> GetHealthReportAsync();
    Task<bool> PerformHealthCheckAsync();
    List<HealthEvent> GetRecentEvents(int count = 20);
}

/// <summary>
/// Health service configuration
/// </summary>
public record HealthServiceConfig(
    TimeSpan CheckInterval = default,
    long MaxMemoryMB = 1000,
    int MaxThreadCount = 100,
    int MaxEventHistory = 500
)
{
    public HealthServiceConfig() : this(TimeSpan.FromMinutes(1), 1000, 100, 500) { }
}

/// <summary>
/// System health status levels
/// </summary>
public enum SystemHealthStatus
{
    Unknown,
    Healthy,
    Degraded,
    Unhealthy
}

/// <summary>
/// Individual health check status
/// </summary>
public enum HealthCheckStatus
{
    Healthy = 1,
    Degraded = 2,
    Unhealthy = 3
}

/// <summary>
/// Health event severity levels
/// </summary>
public enum HealthEventSeverity
{
    Low,
    Medium,
    High,
    Critical
}

/// <summary>
/// Individual health check result
/// </summary>
public record HealthCheckResult(
    string Name,
    HealthCheckStatus Status,
    string Details,
    TimeSpan ResponseTime,
    DateTime CheckedAt
);

/// <summary>
/// Health event record
/// </summary>
public record HealthEvent(
    string Component,
    string Message,
    HealthEventSeverity Severity,
    DateTime Timestamp
);

/// <summary>
/// Complete health report
/// </summary>
public record HealthReport(
    SystemHealthStatus OverallStatus,
    Dictionary<string, HealthCheckResult> Checks,
    List<HealthEvent> RecentEvents,
    DateTime GeneratedAt
);

/// <summary>
/// Health status change event arguments
/// </summary>
public record HealthStatusChangedEventArgs(
    SystemHealthStatus PreviousStatus,
    SystemHealthStatus CurrentStatus,
    DateTime Timestamp
);

/// <summary>
/// Health event occurrence arguments
/// </summary>
public record HealthEventOccurredEventArgs(HealthEvent Event);
