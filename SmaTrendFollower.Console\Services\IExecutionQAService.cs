using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Execution Quality Assurance service for trade execution monitoring
/// Validates trade executions against market data and detects execution anomalies
/// </summary>
public interface IExecutionQAService
{
    /// <summary>
    /// Analyzes a trade execution for quality and accuracy
    /// </summary>
    /// <param name="execution">Trade execution to analyze</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Execution quality analysis result</returns>
    Task<ExecutionQualityAnalysis> AnalyzeExecutionAsync(TradeExecution execution, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates execution price against market data
    /// </summary>
    /// <param name="execution">Trade execution to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Price validation result</returns>
    Task<PriceValidationResult> ValidateExecutionPriceAsync(TradeExecution execution, CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculates execution slippage compared to expected price
    /// </summary>
    /// <param name="execution">Trade execution</param>
    /// <param name="expectedPrice">Expected execution price</param>
    /// <returns>Slippage analysis</returns>
    SlippageAnalysis CalculateSlippage(TradeExecution execution, decimal expectedPrice);

    /// <summary>
    /// Gets execution quality metrics for a symbol over a time period
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <param name="startDate">Start date for analysis</param>
    /// <param name="endDate">End date for analysis</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Execution quality metrics</returns>
    Task<ExecutionQualityMetrics> GetExecutionMetricsAsync(string symbol, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets overall execution quality summary
    /// </summary>
    /// <param name="startDate">Start date for analysis</param>
    /// <param name="endDate">End date for analysis</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Overall execution quality summary</returns>
    Task<ExecutionQualitySummary> GetExecutionSummaryAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Logs an execution for quality analysis
    /// </summary>
    /// <param name="execution">Trade execution to log</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task LogExecutionAsync(TradeExecution execution, CancellationToken cancellationToken = default);

    /// <summary>
    /// Clears execution logs older than specified days
    /// </summary>
    /// <param name="daysToKeep">Number of days to keep logs</param>
    Task CleanupOldLogsAsync(int daysToKeep = 30);
}

/// <summary>
/// Trade execution information
/// </summary>
public readonly record struct TradeExecution(
    string Symbol,
    string OrderId,
    decimal ExecutedPrice,
    long ExecutedQuantity,
    DateTime ExecutionTime,
    string Side, // "buy" or "sell"
    string OrderType, // "market", "limit", etc.
    decimal? LimitPrice,
    string Exchange,
    string ExecutionId
);

/// <summary>
/// Execution quality analysis result
/// </summary>
public readonly record struct ExecutionQualityAnalysis(
    TradeExecution Execution,
    PriceValidationResult PriceValidation,
    SlippageAnalysis Slippage,
    TimingAnalysis Timing,
    ExecutionQualityRating Rating,
    string Analysis,
    List<string> Issues
);

/// <summary>
/// Price validation result
/// </summary>
public readonly record struct PriceValidationResult(
    bool IsValid,
    decimal MarketPrice,
    decimal ExecutedPrice,
    decimal PriceDifference,
    decimal PriceDifferencePercent,
    string ValidationSource,
    DateTime ValidationTime
);

/// <summary>
/// Slippage analysis
/// </summary>
public readonly record struct SlippageAnalysis(
    decimal ExpectedPrice,
    decimal ExecutedPrice,
    decimal SlippageAmount,
    decimal SlippagePercent,
    SlippageDirection Direction,
    SlippageSeverity Severity
);

/// <summary>
/// Timing analysis for execution
/// </summary>
public readonly record struct TimingAnalysis(
    DateTime OrderTime,
    DateTime ExecutionTime,
    TimeSpan ExecutionDelay,
    bool IsWithinExpectedTime,
    string TimingIssues
);

/// <summary>
/// Execution quality metrics for a symbol
/// </summary>
public readonly record struct ExecutionQualityMetrics(
    string Symbol,
    int TotalExecutions,
    decimal AverageSlippage,
    decimal MedianSlippage,
    decimal MaxSlippage,
    decimal AverageExecutionTime,
    decimal SuccessRate,
    Dictionary<ExecutionQualityRating, int> RatingDistribution
);

/// <summary>
/// Overall execution quality summary
/// </summary>
public readonly record struct ExecutionQualitySummary(
    DateTime StartDate,
    DateTime EndDate,
    int TotalExecutions,
    decimal OverallSuccessRate,
    decimal AverageSlippage,
    decimal TotalSlippageCost,
    Dictionary<string, ExecutionQualityMetrics> SymbolMetrics,
    List<string> TopIssues
);

/// <summary>
/// Execution quality rating levels
/// </summary>
public enum ExecutionQualityRating
{
    Excellent,
    Good,
    Fair,
    Poor,
    Failed
}

/// <summary>
/// Slippage direction
/// </summary>
public enum SlippageDirection
{
    Favorable,   // Better than expected price
    Neutral,     // At expected price
    Unfavorable  // Worse than expected price
}

/// <summary>
/// Slippage severity levels
/// </summary>
public enum SlippageSeverity
{
    Minimal,     // < 0.1%
    Low,         // 0.1% - 0.25%
    Moderate,    // 0.25% - 0.5%
    High,        // 0.5% - 1.0%
    Severe       // > 1.0%
}

/// <summary>
/// Execution QA service configuration
/// </summary>
public class ExecutionQAConfig
{
    /// <summary>
    /// Maximum acceptable slippage percentage (default: 0.5%)
    /// </summary>
    public decimal MaxAcceptableSlippagePercent { get; set; } = 0.5m;

    /// <summary>
    /// Maximum acceptable execution delay in seconds (default: 30 seconds)
    /// </summary>
    public int MaxAcceptableExecutionDelaySeconds { get; set; } = 30;

    /// <summary>
    /// Price validation tolerance percentage (default: 0.1%)
    /// </summary>
    public decimal PriceValidationTolerancePercent { get; set; } = 0.1m;

    /// <summary>
    /// Number of days to keep execution logs (default: 90 days)
    /// </summary>
    public int LogRetentionDays { get; set; } = 90;

    /// <summary>
    /// Enable Discord notifications for execution issues (default: true)
    /// </summary>
    public bool EnableDiscordNotifications { get; set; } = true;

    /// <summary>
    /// Minimum slippage amount to trigger notification (default: $10)
    /// </summary>
    public decimal MinSlippageAmountForNotification { get; set; } = 10m;
}

/// <summary>
/// Event args for execution quality alerts
/// </summary>
public class ExecutionQualityAlertEventArgs : EventArgs
{
    public required ExecutionQualityAnalysis Analysis { get; init; }
    public required string AlertType { get; init; }
    public required string Message { get; init; }
}

/// <summary>
/// Event args for execution logged
/// </summary>
public class ExecutionLoggedEventArgs : EventArgs
{
    public required TradeExecution Execution { get; init; }
    public required DateTime LoggedAt { get; init; }
}
