using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using StackExchange.Redis;
using System.Collections.Concurrent;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Execution Quality Assurance service implementation
/// Monitors trade execution quality and detects anomalies
/// </summary>
public sealed class ExecutionQAService : IExecutionQAService, IDisposable
{
    private readonly IPolygonClientFactory _polygonFactory;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly IDiscordNotificationService _discordService;
    private readonly ILogger<ExecutionQAService> _logger;
    private readonly ExecutionQAConfig _config;
    
    private readonly ConcurrentDictionary<string, List<TradeExecution>> _executionCache = new();
    private readonly SemaphoreSlim _analysisLock = new(1, 1);
    private bool _disposed;

    // Redis key patterns
    private const string ExecutionLogKeyPattern = "execution:log:{0}:{1:yyyyMMdd}"; // symbol:date
    private const string ExecutionMetricsKeyPattern = "execution:metrics:{0}"; // symbol
    private const string ExecutionSummaryKeyPattern = "execution:summary:{0:yyyyMMdd}"; // date

    public ExecutionQAService(
        IPolygonClientFactory polygonFactory,
        IOptimizedRedisConnectionService redisService,
        IDiscordNotificationService discordService,
        IConfiguration configuration,
        ILogger<ExecutionQAService> logger)
    {
        _polygonFactory = polygonFactory ?? throw new ArgumentNullException(nameof(polygonFactory));
        _redisService = redisService ?? throw new ArgumentNullException(nameof(redisService));
        _discordService = discordService ?? throw new ArgumentNullException(nameof(discordService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _config = new ExecutionQAConfig();
        configuration.GetSection("ExecutionQA").Bind(_config);
    }

    // === Events ===
    
    public event EventHandler<ExecutionQualityAlertEventArgs>? QualityAlert;
    public event EventHandler<ExecutionLoggedEventArgs>? ExecutionLogged;

    // === Public Methods ===

    public async Task<ExecutionQualityAnalysis> AnalyzeExecutionAsync(TradeExecution execution, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(ExecutionQAService));

        await _analysisLock.WaitAsync(cancellationToken);
        try
        {
            _logger.LogInformation("Analyzing execution quality for {Symbol} order {OrderId}", 
                execution.Symbol, execution.OrderId);

            // Validate execution price against market data
            var priceValidation = await ValidateExecutionPriceAsync(execution, cancellationToken);
            
            // Calculate slippage (use limit price as expected price, or market price if market order)
            var expectedPrice = execution.LimitPrice ?? priceValidation.MarketPrice;
            var slippage = CalculateSlippage(execution, expectedPrice);
            
            // Analyze timing
            var timing = AnalyzeTiming(execution);
            
            // Determine overall rating
            var rating = DetermineQualityRating(priceValidation, slippage, timing);
            
            // Generate analysis text and identify issues
            var (analysis, issues) = GenerateAnalysisAndIssues(execution, priceValidation, slippage, timing, rating);

            var qualityAnalysis = new ExecutionQualityAnalysis(
                execution,
                priceValidation,
                slippage,
                timing,
                rating,
                analysis,
                issues
            );

            // Log the execution
            await LogExecutionAsync(execution, cancellationToken);

            // Send alerts if necessary
            await ProcessQualityAlertsAsync(qualityAnalysis);

            _logger.LogInformation("Execution quality analysis completed for {Symbol}: Rating={Rating}, Slippage={Slippage:F4}%", 
                execution.Symbol, rating, slippage.SlippagePercent);

            return qualityAnalysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing execution quality for {Symbol} order {OrderId}", 
                execution.Symbol, execution.OrderId);
            
            // Return safe default analysis
            return new ExecutionQualityAnalysis(
                execution,
                new PriceValidationResult(false, 0m, execution.ExecutedPrice, 0m, 0m, "Error", DateTime.UtcNow),
                new SlippageAnalysis(execution.ExecutedPrice, execution.ExecutedPrice, 0m, 0m, SlippageDirection.Neutral, SlippageSeverity.Minimal),
                new TimingAnalysis(execution.ExecutionTime, execution.ExecutionTime, TimeSpan.Zero, true, ""),
                ExecutionQualityRating.Failed,
                $"Analysis error: {ex.Message}",
                new List<string> { "Analysis failed due to error" }
            );
        }
        finally
        {
            _analysisLock.Release();
        }
    }

    public async Task<PriceValidationResult> ValidateExecutionPriceAsync(TradeExecution execution, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get market data from Polygon at execution time
            var httpClient = _polygonFactory.CreateClient();
            
            // Use trades endpoint to get actual market trades around execution time
            var executionTimestamp = ((DateTimeOffset)execution.ExecutionTime).ToUnixTimeMilliseconds();
            var startTime = executionTimestamp - 60000; // 1 minute before
            var endTime = executionTimestamp + 60000;   // 1 minute after
            
            var url = $"/v3/trades/{execution.Symbol}?timestamp.gte={startTime}&timestamp.lte={endTime}&limit=50";
            url = _polygonFactory.AddApiKeyToUrl(url);

            _logger.LogDebug("Validating execution price for {Symbol} at {ExecutionTime}", 
                execution.Symbol, execution.ExecutionTime);

            var response = await httpClient.GetAsync(url, cancellationToken);
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var tradesResponse = JsonSerializer.Deserialize<PolygonTradesResponse>(content);

            if (tradesResponse?.Results == null || !tradesResponse.Results.Any())
            {
                _logger.LogWarning("No market trades found for {Symbol} around execution time {ExecutionTime}", 
                    execution.Symbol, execution.ExecutionTime);
                
                return new PriceValidationResult(
                    false,
                    execution.ExecutedPrice,
                    execution.ExecutedPrice,
                    0m,
                    0m,
                    "No market data",
                    DateTime.UtcNow
                );
            }

            // Find the closest trade to execution time
            var closestTrade = tradesResponse.Results
                .OrderBy(t => Math.Abs(t.ParticipantTimestamp - executionTimestamp))
                .First();

            var marketPrice = closestTrade.Price;
            var priceDifference = execution.ExecutedPrice - marketPrice;
            var priceDifferencePercent = marketPrice > 0 ? (priceDifference / marketPrice) * 100m : 0m;
            
            var isValid = Math.Abs(priceDifferencePercent) <= _config.PriceValidationTolerancePercent;

            return new PriceValidationResult(
                isValid,
                marketPrice,
                execution.ExecutedPrice,
                priceDifference,
                priceDifferencePercent,
                "Polygon Trades API",
                DateTime.UtcNow
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating execution price for {Symbol}", execution.Symbol);
            
            return new PriceValidationResult(
                false,
                execution.ExecutedPrice,
                execution.ExecutedPrice,
                0m,
                0m,
                $"Validation error: {ex.Message}",
                DateTime.UtcNow
            );
        }
    }

    public SlippageAnalysis CalculateSlippage(TradeExecution execution, decimal expectedPrice)
    {
        var slippageAmount = execution.ExecutedPrice - expectedPrice;
        var slippagePercent = expectedPrice > 0 ? (slippageAmount / expectedPrice) * 100m : 0m;
        
        // Determine direction based on order side
        var direction = SlippageDirection.Neutral;
        if (Math.Abs(slippageAmount) > 0.001m) // Avoid floating point precision issues
        {
            if (execution.Side.ToLower() == "buy")
            {
                direction = slippageAmount > 0 ? SlippageDirection.Unfavorable : SlippageDirection.Favorable;
            }
            else // sell
            {
                direction = slippageAmount > 0 ? SlippageDirection.Favorable : SlippageDirection.Unfavorable;
            }
        }
        
        // Determine severity
        var severity = Math.Abs(slippagePercent) switch
        {
            < 0.1m => SlippageSeverity.Minimal,
            < 0.25m => SlippageSeverity.Low,
            < 0.5m => SlippageSeverity.Moderate,
            < 1.0m => SlippageSeverity.High,
            _ => SlippageSeverity.Severe
        };

        return new SlippageAnalysis(
            expectedPrice,
            execution.ExecutedPrice,
            slippageAmount,
            slippagePercent,
            direction,
            severity
        );
    }

    public async Task<ExecutionQualityMetrics> GetExecutionMetricsAsync(string symbol, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        try
        {
            var executions = await GetExecutionsForPeriodAsync(symbol, startDate, endDate);

            if (!executions.Any())
            {
                return new ExecutionQualityMetrics(
                    symbol,
                    0,
                    0m,
                    0m,
                    0m,
                    0m,
                    0m,
                    new Dictionary<ExecutionQualityRating, int>()
                );
            }

            var slippages = new List<decimal>();
            var executionTimes = new List<TimeSpan>();
            var ratingCounts = new Dictionary<ExecutionQualityRating, int>();

            foreach (var execution in executions)
            {
                var analysis = await AnalyzeExecutionAsync(execution, cancellationToken);
                slippages.Add(Math.Abs(analysis.Slippage.SlippagePercent));
                executionTimes.Add(analysis.Timing.ExecutionDelay);

                ratingCounts.TryGetValue(analysis.Rating, out var count);
                ratingCounts[analysis.Rating] = count + 1;
            }

            var averageSlippage = slippages.Average();
            var medianSlippage = slippages.OrderBy(s => s).Skip(slippages.Count / 2).First();
            var maxSlippage = slippages.Max();
            var averageExecutionTime = (decimal)executionTimes.Average(t => t.TotalSeconds);
            var successRate = (decimal)ratingCounts.Where(kvp => kvp.Key != ExecutionQualityRating.Failed).Sum(kvp => kvp.Value) / executions.Count * 100m;

            return new ExecutionQualityMetrics(
                symbol,
                executions.Count,
                averageSlippage,
                medianSlippage,
                maxSlippage,
                averageExecutionTime,
                successRate,
                ratingCounts
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting execution metrics for {Symbol}", symbol);
            return new ExecutionQualityMetrics(symbol, 0, 0m, 0m, 0m, 0m, 0m, new Dictionary<ExecutionQualityRating, int>());
        }
    }

    public async Task<ExecutionQualitySummary> GetExecutionSummaryAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        try
        {
            var allExecutions = await GetAllExecutionsForPeriodAsync(startDate, endDate);
            var symbolGroups = allExecutions.GroupBy(e => e.Symbol);

            var symbolMetrics = new Dictionary<string, ExecutionQualityMetrics>();
            var totalExecutions = 0;
            var totalSuccessful = 0;
            var totalSlippage = 0m;
            var totalSlippageCost = 0m;
            var allIssues = new List<string>();

            foreach (var group in symbolGroups)
            {
                var metrics = await GetExecutionMetricsAsync(group.Key, startDate, endDate, cancellationToken);
                symbolMetrics[group.Key] = metrics;

                totalExecutions += metrics.TotalExecutions;
                totalSuccessful += (int)(metrics.SuccessRate / 100m * metrics.TotalExecutions);
                totalSlippage += metrics.AverageSlippage * metrics.TotalExecutions;

                // Calculate slippage cost
                foreach (var execution in group)
                {
                    var slippageAmount = Math.Abs(execution.ExecutedPrice * 0.01m * metrics.AverageSlippage);
                    totalSlippageCost += slippageAmount * execution.ExecutedQuantity;
                }
            }

            var overallSuccessRate = totalExecutions > 0 ? (decimal)totalSuccessful / totalExecutions * 100m : 0m;
            var averageSlippage = totalExecutions > 0 ? totalSlippage / totalExecutions : 0m;

            // Identify top issues
            var topIssues = new List<string>();
            if (averageSlippage > _config.MaxAcceptableSlippagePercent)
                topIssues.Add($"High average slippage: {averageSlippage:F3}%");
            if (overallSuccessRate < 95m)
                topIssues.Add($"Low success rate: {overallSuccessRate:F1}%");
            if (totalSlippageCost > 1000m)
                topIssues.Add($"High slippage cost: ${totalSlippageCost:F2}");

            return new ExecutionQualitySummary(
                startDate,
                endDate,
                totalExecutions,
                overallSuccessRate,
                averageSlippage,
                totalSlippageCost,
                symbolMetrics,
                topIssues
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting execution summary");
            return new ExecutionQualitySummary(
                startDate,
                endDate,
                0,
                0m,
                0m,
                0m,
                new Dictionary<string, ExecutionQualityMetrics>(),
                new List<string> { $"Summary generation failed: {ex.Message}" }
            );
        }
    }

    public async Task LogExecutionAsync(TradeExecution execution, CancellationToken cancellationToken = default)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(ExecutionLogKeyPattern, execution.Symbol, execution.ExecutionTime.Date);
            var json = JsonSerializer.Serialize(execution);

            // Add to daily execution log
            await database.ListRightPushAsync(key, json);
            await database.KeyExpireAsync(key, TimeSpan.FromDays(_config.LogRetentionDays));

            // Update in-memory cache
            _executionCache.AddOrUpdate(execution.Symbol,
                new List<TradeExecution> { execution },
                (_, existing) =>
                {
                    existing.Add(execution);
                    return existing.TakeLast(100).ToList(); // Keep last 100 executions per symbol
                });

            ExecutionLogged?.Invoke(this, new ExecutionLoggedEventArgs
            {
                Execution = execution,
                LoggedAt = DateTime.UtcNow
            });

            _logger.LogDebug("Logged execution for {Symbol} order {OrderId}", execution.Symbol, execution.OrderId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging execution for {Symbol} order {OrderId}", execution.Symbol, execution.OrderId);
        }
    }

    public async Task CleanupOldLogsAsync(int daysToKeep = 30)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var server = database.Multiplexer.GetServer(database.Multiplexer.GetEndPoints().First());

            var cutoffDate = DateTime.Today.AddDays(-daysToKeep);
            var keysToDelete = new List<RedisKey>();

            await foreach (var key in server.KeysAsync(pattern: "execution:log:*"))
            {
                var keyString = key.ToString();
                var parts = keyString.Split(':');
                if (parts.Length >= 3 && DateTime.TryParseExact(parts[2], "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out var keyDate))
                {
                    if (keyDate < cutoffDate)
                    {
                        keysToDelete.Add(key);
                    }
                }
            }

            if (keysToDelete.Any())
            {
                await database.KeyDeleteAsync(keysToDelete.ToArray());
                _logger.LogInformation("Cleaned up {Count} old execution log keys", keysToDelete.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up old execution logs");
        }
    }

    // === Private Methods ===

    private TimingAnalysis AnalyzeTiming(TradeExecution execution)
    {
        // For now, assume order time is same as execution time since we don't have order placement time
        // In a real implementation, this would come from the order management system
        var orderTime = execution.ExecutionTime.AddSeconds(-5); // Assume 5 seconds before execution
        var executionDelay = execution.ExecutionTime - orderTime;
        var isWithinExpectedTime = executionDelay.TotalSeconds <= _config.MaxAcceptableExecutionDelaySeconds;

        var timingIssues = "";
        if (!isWithinExpectedTime)
        {
            timingIssues = $"Execution delay of {executionDelay.TotalSeconds:F1}s exceeds maximum of {_config.MaxAcceptableExecutionDelaySeconds}s";
        }

        return new TimingAnalysis(
            orderTime,
            execution.ExecutionTime,
            executionDelay,
            isWithinExpectedTime,
            timingIssues
        );
    }

    private ExecutionQualityRating DetermineQualityRating(PriceValidationResult priceValidation, SlippageAnalysis slippage, TimingAnalysis timing)
    {
        var score = 100;

        // Price validation issues
        if (!priceValidation.IsValid)
            score -= 30;

        // Slippage penalties
        score -= slippage.Severity switch
        {
            SlippageSeverity.Minimal => 0,
            SlippageSeverity.Low => 5,
            SlippageSeverity.Moderate => 15,
            SlippageSeverity.High => 25,
            SlippageSeverity.Severe => 40,
            _ => 0
        };

        // Timing penalties
        if (!timing.IsWithinExpectedTime)
            score -= 20;

        // Unfavorable slippage penalty
        if (slippage.Direction == SlippageDirection.Unfavorable)
            score -= 10;

        return score switch
        {
            >= 90 => ExecutionQualityRating.Excellent,
            >= 75 => ExecutionQualityRating.Good,
            >= 60 => ExecutionQualityRating.Fair,
            >= 40 => ExecutionQualityRating.Poor,
            _ => ExecutionQualityRating.Failed
        };
    }

    private (string analysis, List<string> issues) GenerateAnalysisAndIssues(
        TradeExecution execution,
        PriceValidationResult priceValidation,
        SlippageAnalysis slippage,
        TimingAnalysis timing,
        ExecutionQualityRating rating)
    {
        var analysis = $"Execution Quality: {rating}. ";
        analysis += $"Price: ${execution.ExecutedPrice:F4} vs Market: ${priceValidation.MarketPrice:F4}. ";
        analysis += $"Slippage: {slippage.SlippagePercent:F3}% ({slippage.Direction}). ";
        analysis += $"Timing: {timing.ExecutionDelay.TotalSeconds:F1}s delay.";

        var issues = new List<string>();

        if (!priceValidation.IsValid)
            issues.Add($"Price validation failed: {priceValidation.PriceDifferencePercent:F3}% difference from market");

        if (Math.Abs(slippage.SlippagePercent) > _config.MaxAcceptableSlippagePercent)
            issues.Add($"High slippage: {slippage.SlippagePercent:F3}% exceeds maximum of {_config.MaxAcceptableSlippagePercent:F3}%");

        if (!timing.IsWithinExpectedTime)
            issues.Add(timing.TimingIssues);

        if (slippage.Direction == SlippageDirection.Unfavorable && slippage.Severity >= SlippageSeverity.Moderate)
            issues.Add($"Unfavorable slippage of {slippage.SlippagePercent:F3}%");

        return (analysis, issues);
    }

    private async Task ProcessQualityAlertsAsync(ExecutionQualityAnalysis analysis)
    {
        try
        {
            var shouldAlert = false;
            var alertType = "";
            var message = "";

            // Check for alert conditions
            if (analysis.Rating == ExecutionQualityRating.Failed || analysis.Rating == ExecutionQualityRating.Poor)
            {
                shouldAlert = true;
                alertType = "Poor Execution Quality";
                message = $"Poor execution quality for {analysis.Execution.Symbol}: {analysis.Analysis}";
            }
            else if (Math.Abs(analysis.Slippage.SlippageAmount * analysis.Execution.ExecutedQuantity) >= _config.MinSlippageAmountForNotification)
            {
                shouldAlert = true;
                alertType = "High Slippage Cost";
                var slippageCost = Math.Abs(analysis.Slippage.SlippageAmount * analysis.Execution.ExecutedQuantity);
                message = $"High slippage cost for {analysis.Execution.Symbol}: ${slippageCost:F2} ({analysis.Slippage.SlippagePercent:F3}%)";
            }

            if (shouldAlert)
            {
                // Fire event
                QualityAlert?.Invoke(this, new ExecutionQualityAlertEventArgs
                {
                    Analysis = analysis,
                    AlertType = alertType,
                    Message = message
                });

                // Send Discord notification if enabled
                if (_config.EnableDiscordNotifications)
                {
                    var discordMessage = $"🚨 **{alertType}**\n{message}\n\nIssues: {string.Join(", ", analysis.Issues)}";
                    // Use the private SendDiscordMessageAsync method by creating a simple notification
                    await SendExecutionAlertToDiscordAsync(alertType, discordMessage);
                }

                _logger.LogWarning("Execution quality alert: {AlertType} - {Message}", alertType, message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing quality alerts");
        }
    }

    private async Task SendExecutionAlertToDiscordAsync(string alertType, string message)
    {
        try
        {
            // Use the existing trade notification method as a workaround
            // In a real implementation, we might add a generic message method to IDiscordNotificationService
            var parts = message.Split('\n');
            var title = parts.Length > 0 ? parts[0] : alertType;
            var details = parts.Length > 1 ? string.Join("\n", parts.Skip(1)) : message;

            // Use options notification as it's the most generic
            await _discordService.SendOptionsNotificationAsync("Execution Alert", title, details);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending execution alert to Discord");
        }
    }

    private async Task<List<TradeExecution>> GetExecutionsForPeriodAsync(string symbol, DateTime startDate, DateTime endDate)
    {
        var executions = new List<TradeExecution>();

        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var currentDate = startDate.Date;

            while (currentDate <= endDate.Date)
            {
                var key = string.Format(ExecutionLogKeyPattern, symbol, currentDate);
                var values = await database.ListRangeAsync(key);

                foreach (var value in values)
                {
                    if (value.HasValue)
                    {
                        var execution = JsonSerializer.Deserialize<TradeExecution>(value!);
                        if (execution.ExecutionTime >= startDate && execution.ExecutionTime <= endDate)
                        {
                            executions.Add(execution);
                        }
                    }
                }

                currentDate = currentDate.AddDays(1);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving executions for {Symbol} from {StartDate} to {EndDate}", symbol, startDate, endDate);
        }

        return executions;
    }

    private async Task<List<TradeExecution>> GetAllExecutionsForPeriodAsync(DateTime startDate, DateTime endDate)
    {
        var executions = new List<TradeExecution>();

        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var server = database.Multiplexer.GetServer(database.Multiplexer.GetEndPoints().First());

            await foreach (var key in server.KeysAsync(pattern: "execution:log:*"))
            {
                var keyString = key.ToString();
                var parts = keyString.Split(':');
                if (parts.Length >= 3 && DateTime.TryParseExact(parts[2], "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out var keyDate))
                {
                    if (keyDate >= startDate.Date && keyDate <= endDate.Date)
                    {
                        var values = await database.ListRangeAsync(key);
                        foreach (var value in values)
                        {
                            if (value.HasValue)
                            {
                                var execution = JsonSerializer.Deserialize<TradeExecution>(value!);
                                if (execution.ExecutionTime >= startDate && execution.ExecutionTime <= endDate)
                                {
                                    executions.Add(execution);
                                }
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all executions from {StartDate} to {EndDate}", startDate, endDate);
        }

        return executions;
    }

    // === IDisposable ===

    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            _analysisLock?.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing ExecutionQAService");
        }
        finally
        {
            _disposed = true;
        }
    }
}
