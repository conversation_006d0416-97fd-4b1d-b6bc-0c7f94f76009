using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.RegularExpressions;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("Testing VIX Web Scraping...");
        
        // Test with the actual Yahoo Finance content
        var httpClient = new HttpClient();
        httpClient.DefaultRequestHeaders.Add("User-Agent",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        
        try
        {
            var url = "https://finance.yahoo.com/quote/%5EVIX";
            Console.WriteLine($"Fetching: {url}");
            
            var response = await httpClient.GetStringAsync(url);
            Console.WriteLine($"Response length: {response.Length} characters");
            
            // Test the parsing patterns
            var vixValue = ParseYahooVix(response);
            
            if (vixValue.HasValue)
            {
                Console.WriteLine($"✅ SUCCESS: Extracted VIX value: {vixValue.Value:F2}");
                
                // Validate it's reasonable (between 8 and 80)
                if (vixValue.Value >= 8 && vixValue.Value <= 80)
                {
                    Console.WriteLine($"✅ VALIDATION: VIX value {vixValue.Value:F2} is within reasonable bounds");
                }
                else
                {
                    Console.WriteLine($"❌ VALIDATION: VIX value {vixValue.Value:F2} is outside reasonable bounds (8-80)");
                }
            }
            else
            {
                Console.WriteLine("❌ FAILED: Could not extract VIX value");
                
                // Debug: Show some content around VIX mentions
                var vixMatches = Regex.Matches(response, @"VIX.{0,50}", RegexOptions.IgnoreCase);
                Console.WriteLine($"\nFound {vixMatches.Count} VIX mentions:");
                foreach (Match match in vixMatches.Take(5))
                {
                    Console.WriteLine($"  {match.Value}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ ERROR: {ex.Message}");
        }
        finally
        {
            httpClient.Dispose();
        }
    }
    
    private static decimal? ParseYahooVix(string html)
    {
        var patterns = new[]
        {
            // Modern Yahoo Finance patterns (2024/2025)
            @"CBOE Volatility Index \(\^VIX\)[^0-9]*([0-9]+\.?[0-9]*)",
            @"(\d+\.\d+)\s*-\d+\.\d+\s*\(-\d+\.\d+%\)",  // Pattern: "20.86 -1.31 (-5.91%)"
            @">([0-9]+\.?[0-9]*)<[^>]*>-[0-9]+\.?[0-9]*<[^>]*>\(-[0-9]+\.?[0-9]*%\)",
            @"Follow\s*([0-9]+\.?[0-9]*)",  // "Follow 20.86"
            
            // Legacy patterns
            @"""regularMarketPrice"":\{""raw"":([0-9]+\.?[0-9]*)",
            @"data-symbol=""\^VIX""[^>]*data-field=""regularMarketPrice""[^>]*>([0-9]+\.?[0-9]*)<",
            @"""VIX""[^}]*""regularMarketPrice""[^}]*""raw"":([0-9]+\.?[0-9]*)",
            
            // Generic VIX patterns for Yahoo
            @"\^VIX[^0-9]*([0-9]+\.?[0-9]*)",
            @"VIX[^0-9]*([0-9]+\.?[0-9]*)"
        };

        return TryParseWithPatterns(html, patterns);
    }
    
    private static decimal? TryParseWithPatterns(string html, string[] patterns)
    {
        for (int i = 0; i < patterns.Length; i++)
        {
            var pattern = patterns[i];
            try
            {
                var match = Regex.Match(html, pattern, RegexOptions.IgnoreCase);
                if (match.Success && decimal.TryParse(match.Groups[1].Value, out var value) && value > 0)
                {
                    Console.WriteLine($"✅ Pattern {i + 1} matched: '{pattern}' -> {value:F2}");
                    return value;
                }
                else if (match.Success)
                {
                    Console.WriteLine($"⚠️ Pattern {i + 1} matched but failed parsing: '{pattern}' -> '{match.Groups[1].Value}'");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Pattern {i + 1} failed: '{pattern}' -> {ex.Message}");
            }
        }
        return null;
    }
}
