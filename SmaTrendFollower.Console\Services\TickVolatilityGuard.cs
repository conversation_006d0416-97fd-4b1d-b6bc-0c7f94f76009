using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using StackExchange.Redis;
using System.Collections.Concurrent;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Tick-by-tick volatility guard service implementation
/// Detects volatility spikes and temporarily blocks trading to avoid flash crashes
/// Uses dynamic thresholds based on market volatility and account metrics
/// </summary>
public sealed class TickVolatilityGuard : ITickVolatilityGuard, IDisposable
{
    private readonly ITickStreamService _tickStreamService;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly IMarketDataService _marketDataService;
    private readonly ILogger<TickVolatilityGuard> _logger;
    
    private readonly ConcurrentDictionary<string, VolatilityTracker> _volatilityTrackers = new();
    private readonly ConcurrentDictionary<string, TradingBlock> _tradingBlocks = new();
    private readonly HashSet<string> _monitoredSymbols = new();
    private readonly object _symbolsLock = new();
    
    private VolatilityGuardConfig _config;
    private VolatilityGuardStatus _status = VolatilityGuardStatus.Stopped;
    private bool _disposed;
    
    // Redis key patterns
    private const string VolatilityMetricsKeyPattern = "volatility:metrics:{0}";
    private const string TradingBlockKeyPattern = "volatility:block:{0}";
    
    public event EventHandler<VolatilitySpikeDe​tectedEventArgs>? VolatilitySpikeDetected;
    public event EventHandler<TradingBlockedEventArgs>? TradingBlocked;
    public event EventHandler<TradingUnblockedEventArgs>? TradingUnblocked;
    
    public TickVolatilityGuard(
        ITickStreamService tickStreamService,
        IOptimizedRedisConnectionService redisService,
        IMarketDataService marketDataService,
        IConfiguration configuration,
        ILogger<TickVolatilityGuard> logger)
    {
        _tickStreamService = tickStreamService ?? throw new ArgumentNullException(nameof(tickStreamService));
        _redisService = redisService ?? throw new ArgumentNullException(nameof(redisService));
        _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // Load configuration
        _config = LoadConfiguration(configuration);
        
        // Subscribe to tick stream events
        _tickStreamService.TradeReceived += OnTradeReceived;
        _tickStreamService.QuoteReceived += OnQuoteReceived;
        
        _logger.LogInformation("TickVolatilityGuard initialized with {WindowMinutes}min window, {BaseThreshold} base threshold",
            _config.VolatilityWindowMinutes, _config.BaseStdDevThreshold);
    }
    
    public async Task StartMonitoringAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(TickVolatilityGuard));
            
        var symbolList = symbols.ToList();
        if (!symbolList.Any())
            return;
            
        try
        {
            _status = VolatilityGuardStatus.Starting;
            _logger.LogInformation("Starting volatility monitoring for {Count} symbols", symbolList.Count);
            
            // Initialize volatility trackers
            foreach (var symbol in symbolList)
            {
                var tracker = new VolatilityTracker(symbol, _config, _logger);
                _volatilityTrackers.TryAdd(symbol, tracker);
                
                // Load cached metrics if available
                await LoadCachedMetricsAsync(symbol, tracker);
            }
            
            // Update monitored symbols
            lock (_symbolsLock)
            {
                foreach (var symbol in symbolList)
                {
                    _monitoredSymbols.Add(symbol);
                }
            }
            
            // Subscribe to tick stream
            await _tickStreamService.SubscribeAsync(symbolList, TickDataTypes.Trades | TickDataTypes.Quotes, cancellationToken);
            
            _status = VolatilityGuardStatus.Active;
            _logger.LogInformation("Volatility monitoring started successfully for {Count} symbols", symbolList.Count);
        }
        catch (Exception ex)
        {
            _status = VolatilityGuardStatus.Error;
            _logger.LogError(ex, "Error starting volatility monitoring");
            throw;
        }
    }
    
    public async Task StopMonitoringAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;
            
        try
        {
            _logger.LogInformation("Stopping volatility monitoring");
            
            // Clear all blocks
            _tradingBlocks.Clear();
            
            // Clear monitored symbols
            lock (_symbolsLock)
            {
                _monitoredSymbols.Clear();
            }
            
            // Clear trackers
            _volatilityTrackers.Clear();
            
            _status = VolatilityGuardStatus.Stopped;
            _logger.LogInformation("Volatility monitoring stopped");
        }
        catch (Exception ex)
        {
            _status = VolatilityGuardStatus.Error;
            _logger.LogError(ex, "Error stopping volatility monitoring");
            throw;
        }
    }
    
    public bool IsTradingBlocked(string symbol)
    {
        if (!_tradingBlocks.TryGetValue(symbol, out var block))
            return false;
            
        // Check if block has expired
        if (DateTime.UtcNow > block.ExpiresAt)
        {
            _tradingBlocks.TryRemove(symbol, out _);
            
            // Fire unblocked event
            TradingUnblocked?.Invoke(this, new TradingUnblockedEventArgs
            {
                Symbol = symbol,
                Reason = "Block expired",
                BlockedDuration = DateTime.UtcNow - block.BlockedAt,
                UnblockedAt = DateTime.UtcNow
            });
            
            return false;
        }
        
        return true;
    }
    
    public bool IsAnyTradingBlocked()
    {
        // Clean expired blocks and check if any remain
        var expiredBlocks = _tradingBlocks
            .Where(kvp => DateTime.UtcNow > kvp.Value.ExpiresAt)
            .Select(kvp => kvp.Key)
            .ToList();
            
        foreach (var symbol in expiredBlocks)
        {
            if (_tradingBlocks.TryRemove(symbol, out var block))
            {
                TradingUnblocked?.Invoke(this, new TradingUnblockedEventArgs
                {
                    Symbol = symbol,
                    Reason = "Block expired",
                    BlockedDuration = DateTime.UtcNow - block.BlockedAt,
                    UnblockedAt = DateTime.UtcNow
                });
            }
        }
        
        return _tradingBlocks.Any();
    }
    
    public async Task<VolatilityMetrics?> GetVolatilityMetricsAsync(string symbol)
    {
        if (!_volatilityTrackers.TryGetValue(symbol, out var tracker))
            return null;
            
        try
        {
            // Try Redis cache first
            var database = await _redisService.GetDatabaseAsync();
            var cacheKey = string.Format(VolatilityMetricsKeyPattern, symbol);
            var cachedData = await database.StringGetAsync(cacheKey);
            
            if (cachedData.HasValue)
            {
                var metrics = JsonSerializer.Deserialize<VolatilityMetrics>(cachedData!);
                if (DateTime.UtcNow - metrics.LastUpdate < TimeSpan.FromMinutes(1))
                {
                    return metrics;
                }
            }
            
            // Calculate current metrics
            return tracker.GetCurrentMetrics();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting volatility metrics for {Symbol}", symbol);
            return null;
        }
    }
    
    public IEnumerable<string> GetBlockedSymbols()
    {
        return _tradingBlocks.Keys.ToList();
    }
    
    public async Task OverrideBlockAsync(string symbol, bool blocked, string reason)
    {
        if (blocked)
        {
            var block = new TradingBlock(
                Symbol: symbol,
                Reason: $"Manual override: {reason}",
                BlockedAt: DateTime.UtcNow,
                ExpiresAt: DateTime.UtcNow.Add(_config.BlockDuration)
            );
            
            _tradingBlocks.AddOrUpdate(symbol, block, (_, _) => block);
            
            TradingBlocked?.Invoke(this, new TradingBlockedEventArgs
            {
                Symbol = symbol,
                Reason = block.Reason,
                BlockDuration = _config.BlockDuration,
                BlockedAt = block.BlockedAt,
                Metrics = await GetVolatilityMetricsAsync(symbol) ?? CreateDefaultMetrics(symbol)
            });
            
            _logger.LogWarning("Trading manually blocked for {Symbol}: {Reason}", symbol, reason);
        }
        else
        {
            if (_tradingBlocks.TryRemove(symbol, out var removedBlock))
            {
                TradingUnblocked?.Invoke(this, new TradingUnblockedEventArgs
                {
                    Symbol = symbol,
                    Reason = $"Manual override: {reason}",
                    BlockedDuration = DateTime.UtcNow - removedBlock.BlockedAt,
                    UnblockedAt = DateTime.UtcNow
                });
                
                _logger.LogInformation("Trading manually unblocked for {Symbol}: {Reason}", symbol, reason);
            }
        }
    }
    
    public async Task UpdateConfigurationAsync(VolatilityGuardConfig config)
    {
        _config = config;
        
        // Update all trackers with new config
        foreach (var tracker in _volatilityTrackers.Values)
        {
            tracker.UpdateConfig(config);
        }
        
        _logger.LogInformation("Volatility guard configuration updated");
    }
    
    public VolatilityGuardStatus GetStatus() => _status;
    
    public IEnumerable<string> GetMonitoredSymbols()
    {
        lock (_symbolsLock)
        {
            return _monitoredSymbols.ToList();
        }
    }
    
    private VolatilityGuardConfig LoadConfiguration(IConfiguration configuration)
    {
        var section = configuration.GetSection("VolatilityGuard");
        
        return new VolatilityGuardConfig(
            VolatilityWindowMinutes: section.GetValue("VolatilityWindowMinutes", 5),
            BaseStdDevThreshold: section.GetValue("BaseStdDevThreshold", 3.0m),
            MaxStdDevThreshold: section.GetValue("MaxStdDevThreshold", 6.0m),
            MinStdDevThreshold: section.GetValue("MinStdDevThreshold", 2.0m),
            MinTicksRequired: section.GetValue("MinTicksRequired", 20),
            BlockDuration: TimeSpan.FromMinutes(section.GetValue("BlockDurationMinutes", 2)),
            AccountSizeMultiplier: section.GetValue("AccountSizeMultiplier", 1.0m),
            VixAdjustmentFactor: section.GetValue("VixAdjustmentFactor", 0.1m),
            EnableDynamicThresholds: section.GetValue("EnableDynamicThresholds", true),
            FlashCrashThreshold: section.GetValue("FlashCrashThreshold", 10.0m),
            CooldownPeriod: TimeSpan.FromMinutes(section.GetValue("CooldownMinutes", 1))
        );
    }
    
    private async Task LoadCachedMetricsAsync(string symbol, VolatilityTracker tracker)
    {
        try
        {
            var metrics = await GetVolatilityMetricsAsync(symbol);
            if (metrics != null)
            {
                tracker.InitializeFromCache(metrics);
                _logger.LogDebug("Loaded cached volatility metrics for {Symbol}", symbol);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error loading cached volatility metrics for {Symbol}", symbol);
        }
    }
    
    private VolatilityMetrics CreateDefaultMetrics(string symbol)
    {
        return new VolatilityMetrics(
            Symbol: symbol,
            CurrentVolatility: 0,
            AverageVolatility: 0,
            StandardDeviation: 0,
            ZScore: 0,
            DynamicThreshold: _config.BaseStdDevThreshold,
            TickCount: 0,
            LastUpdate: DateTime.UtcNow,
            IsBlocked: false,
            BlockReason: null
        );
    }
    
    private async void OnTradeReceived(object? sender, TradeTickEventArgs e)
    {
        if (_disposed || !_volatilityTrackers.TryGetValue(e.TradeTick.Symbol, out var tracker))
            return;

        try
        {
            var metrics = tracker.UpdateWithTrade(e.TradeTick.Price, e.TradeTick.Timestamp);
            if (metrics != null)
            {
                await ProcessVolatilityUpdate(metrics);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing trade tick for volatility: {Symbol}", e.TradeTick.Symbol);
        }
    }

    private async void OnQuoteReceived(object? sender, QuoteTickEventArgs e)
    {
        if (_disposed || !_volatilityTrackers.TryGetValue(e.QuoteTick.Symbol, out var tracker))
            return;

        try
        {
            // Use mid-price for volatility calculation
            var midPrice = (e.QuoteTick.BidPrice + e.QuoteTick.AskPrice) / 2;
            var metrics = tracker.UpdateWithQuote(midPrice, e.QuoteTick.Timestamp);
            if (metrics != null)
            {
                await ProcessVolatilityUpdate(metrics);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing quote tick for volatility: {Symbol}", e.QuoteTick.Symbol);
        }
    }

    private async Task ProcessVolatilityUpdate(VolatilityMetrics metrics)
    {
        try
        {
            // Cache metrics in Redis
            await CacheVolatilityMetricsAsync(metrics);

            // Calculate dynamic threshold
            var dynamicThreshold = await CalculateDynamicThresholdAsync(metrics.Symbol);

            // Check for volatility spike
            if (metrics.ZScore >= dynamicThreshold)
            {
                var spikeType = DetermineSpikeType(metrics.ZScore, dynamicThreshold);

                // Fire spike detected event
                VolatilitySpikeDetected?.Invoke(this, new VolatilitySpikeDe​tectedEventArgs
                {
                    Symbol = metrics.Symbol,
                    VolatilityLevel = metrics.CurrentVolatility,
                    Threshold = dynamicThreshold,
                    ZScore = metrics.ZScore,
                    Timestamp = metrics.LastUpdate,
                    SpikeType = spikeType
                });

                // Block trading if severe spike
                if (spikeType >= VolatilitySpikeType.Severe && !IsTradingBlocked(metrics.Symbol))
                {
                    await BlockTradingAsync(metrics, dynamicThreshold, spikeType);
                }

                _logger.LogWarning("Volatility spike detected for {Symbol}: Z-Score {ZScore:F2} (threshold: {Threshold:F2}), Type: {SpikeType}",
                    metrics.Symbol, metrics.ZScore, dynamicThreshold, spikeType);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing volatility update for {Symbol}", metrics.Symbol);
        }
    }

    private async Task<decimal> CalculateDynamicThresholdAsync(string symbol)
    {
        if (!_config.EnableDynamicThresholds)
            return _config.BaseStdDevThreshold;

        try
        {
            // Get current VIX level for market volatility adjustment
            var vixLevel = await _marketDataService.GetIndexValueAsync("VIX");
            var vixAdjustment = vixLevel.HasValue ? (vixLevel.Value - 20) * _config.VixAdjustmentFactor : 0;

            // Account size adjustment (larger accounts = more conservative)
            var accountAdjustment = (_config.AccountSizeMultiplier - 1.0m) * 0.5m;

            // Calculate dynamic threshold
            var dynamicThreshold = _config.BaseStdDevThreshold + vixAdjustment + accountAdjustment;

            // Clamp to min/max bounds
            return Math.Max(_config.MinStdDevThreshold,
                   Math.Min(_config.MaxStdDevThreshold, dynamicThreshold));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error calculating dynamic threshold for {Symbol}, using base threshold", symbol);
            return _config.BaseStdDevThreshold;
        }
    }

    private static VolatilitySpikeType DetermineSpikeType(decimal zScore, decimal threshold)
    {
        if (zScore >= threshold * 3.0m)
            return VolatilitySpikeType.FlashCrash;
        if (zScore >= threshold * 2.0m)
            return VolatilitySpikeType.Severe;
        if (zScore >= threshold * 1.5m)
            return VolatilitySpikeType.Moderate;
        return VolatilitySpikeType.Normal;
    }

    private async Task BlockTradingAsync(VolatilityMetrics metrics, decimal threshold, VolatilitySpikeType spikeType)
    {
        var blockDuration = spikeType == VolatilitySpikeType.FlashCrash
            ? _config.BlockDuration.Add(_config.CooldownPeriod)
            : _config.BlockDuration;

        var block = new TradingBlock(
            Symbol: metrics.Symbol,
            Reason: $"Volatility spike: Z-Score {metrics.ZScore:F2} > {threshold:F2} ({spikeType})",
            BlockedAt: DateTime.UtcNow,
            ExpiresAt: DateTime.UtcNow.Add(blockDuration)
        );

        _tradingBlocks.AddOrUpdate(metrics.Symbol, block, (_, _) => block);

        // Cache block in Redis
        await CacheTradingBlockAsync(block);

        // Fire blocked event
        TradingBlocked?.Invoke(this, new TradingBlockedEventArgs
        {
            Symbol = metrics.Symbol,
            Reason = block.Reason,
            BlockDuration = blockDuration,
            BlockedAt = block.BlockedAt,
            Metrics = metrics
        });

        _logger.LogWarning("Trading blocked for {Symbol}: {Reason}, Duration: {Duration}",
            metrics.Symbol, block.Reason, blockDuration);
    }

    private async Task CacheVolatilityMetricsAsync(VolatilityMetrics metrics)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(VolatilityMetricsKeyPattern, metrics.Symbol);
            var json = JsonSerializer.Serialize(metrics);
            await database.StringSetAsync(key, json, TimeSpan.FromMinutes(5));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error caching volatility metrics for {Symbol}", metrics.Symbol);
        }
    }

    private async Task CacheTradingBlockAsync(TradingBlock block)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(TradingBlockKeyPattern, block.Symbol);
            var json = JsonSerializer.Serialize(block);
            var expiry = block.ExpiresAt - DateTime.UtcNow;
            if (expiry > TimeSpan.Zero)
            {
                await database.StringSetAsync(key, json, expiry);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error caching trading block for {Symbol}", block.Symbol);
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            _tickStreamService.TradeReceived -= OnTradeReceived;
            _tickStreamService.QuoteReceived -= OnQuoteReceived;

            _volatilityTrackers.Clear();
            _tradingBlocks.Clear();

            lock (_symbolsLock)
            {
                _monitoredSymbols.Clear();
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error disposing TickVolatilityGuard");
        }
    }
}
