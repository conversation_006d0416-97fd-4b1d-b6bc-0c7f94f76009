using SmaTrendFollower.Models;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced Polygon WebSocket client interface for Developer subscription
/// Supports unlimited streaming with multiple connection management
/// </summary>
public interface IEnhancedPolygonWebSocketClient : IPolygonWebSocketClient
{
    // === Enhanced Events ===
    
    /// <summary>
    /// Fired when options data is received
    /// </summary>
    event EventHandler<PolygonOptionsUpdateEventArgs>? OptionsUpdated;
    
    /// <summary>
    /// Fired when forex data is received
    /// </summary>
    event EventHandler<PolygonForexUpdateEventArgs>? ForexUpdated;
    
    /// <summary>
    /// Fired when crypto data is received
    /// </summary>
    event EventHandler<PolygonCryptoUpdateEventArgs>? CryptoUpdated;
    
    /// <summary>
    /// Fired when connection metrics are updated
    /// </summary>
    event EventHandler<ConnectionMetricsEventArgs>? MetricsUpdated;
    
    // === Enhanced Properties ===
    
    /// <summary>
    /// Number of active WebSocket connections
    /// </summary>
    int ActiveConnections { get; }
    
    /// <summary>
    /// Current message processing rate (messages/second)
    /// </summary>
    double MessageProcessingRate { get; }
    
    /// <summary>
    /// Total messages received since connection
    /// </summary>
    long TotalMessagesReceived { get; }
    
    /// <summary>
    /// Connection quality metrics
    /// </summary>
    ConnectionQualityMetrics ConnectionQuality { get; }
    
    // === Enhanced Subscription Management ===
    
    /// <summary>
    /// Subscribe to options data updates
    /// </summary>
    Task SubscribeToOptionsUpdatesAsync(IEnumerable<string> optionSymbols, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Subscribe to forex data updates
    /// </summary>
    Task SubscribeToForexUpdatesAsync(IEnumerable<string> forexPairs, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Subscribe to crypto data updates
    /// </summary>
    Task SubscribeToCryptoUpdatesAsync(IEnumerable<string> cryptoPairs, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Subscribe to multiple data types for a symbol in one call
    /// </summary>
    Task SubscribeToMultipleDataTypesAsync(string symbol, PolygonDataTypes dataTypes, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Bulk subscribe to multiple symbols and data types
    /// </summary>
    Task BulkSubscribeAsync(IDictionary<string, PolygonDataTypes> subscriptions, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get current subscription status for a symbol
    /// </summary>
    PolygonSubscriptionStatus GetSubscriptionStatus(string symbol);
    
    /// <summary>
    /// Get all active subscriptions
    /// </summary>
    IReadOnlyDictionary<string, PolygonDataTypes> GetActiveSubscriptions();
    
    // === Connection Management ===
    
    /// <summary>
    /// Connect with specific connection configuration
    /// </summary>
    Task ConnectAsync(PolygonConnectionConfiguration config, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Force reconnection of all connections
    /// </summary>
    Task ForceReconnectAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get connection health status
    /// </summary>
    Task<ConnectionHealthStatus> GetConnectionHealthAsync();
    
    // === Performance Monitoring ===
    
    /// <summary>
    /// Get real-time performance metrics
    /// </summary>
    PerformanceMetrics GetPerformanceMetrics();
    
    /// <summary>
    /// Reset performance counters
    /// </summary>
    void ResetPerformanceCounters();
    
    /// <summary>
    /// Configure performance monitoring settings
    /// </summary>
    void ConfigurePerformanceMonitoring(PerformanceMonitoringConfiguration config);
}

/// <summary>
/// Data types available for Polygon subscriptions
/// </summary>
[Flags]
public enum PolygonDataTypes
{
    None = 0,
    Trades = 1,
    Quotes = 2,
    Aggregates = 4,
    IndexValues = 8,
    Options = 16,
    Forex = 32,
    Crypto = 64,
    All = Trades | Quotes | Aggregates | IndexValues | Options | Forex | Crypto
}

/// <summary>
/// Subscription status for a symbol
/// </summary>
public enum PolygonSubscriptionStatus
{
    NotSubscribed,
    Subscribing,
    Subscribed,
    PartiallySubscribed,
    Failed,
    Unsubscribing
}

/// <summary>
/// Connection configuration for enhanced WebSocket client
/// </summary>
public class PolygonConnectionConfiguration
{
    public int MaxConnections { get; set; } = 5;
    public TimeSpan ConnectionTimeout { get; set; } = TimeSpan.FromSeconds(30);
    public TimeSpan HeartbeatInterval { get; set; } = TimeSpan.FromSeconds(30);
    public bool EnableCompression { get; set; } = true;
    public int MaxMessageQueueSize { get; set; } = 10000;
    public bool EnableMetrics { get; set; } = true;
    public LogLevel LogLevel { get; set; } = LogLevel.Information;
}

/// <summary>
/// Connection quality metrics
/// </summary>
public class ConnectionQualityMetrics
{
    public double Latency { get; set; }
    public double PacketLoss { get; set; }
    public double Throughput { get; set; }
    public DateTime LastUpdate { get; set; }
    public ConnectionQuality Quality { get; set; }
}

/// <summary>
/// Connection quality rating
/// </summary>
public enum ConnectionQuality
{
    Poor,
    Fair,
    Good,
    Excellent
}

/// <summary>
/// Connection health status
/// </summary>
public class ConnectionHealthStatus
{
    public bool IsHealthy { get; set; }
    public List<string> Issues { get; set; } = new();
    public DateTime LastHealthCheck { get; set; }
    public TimeSpan Uptime { get; set; }
    public int ReconnectionCount { get; set; }
}

/// <summary>
/// Performance metrics for WebSocket client
/// </summary>
public class PerformanceMetrics
{
    public long MessagesReceived { get; set; }
    public long MessagesProcessed { get; set; }
    public long MessagesFailed { get; set; }
    public double AverageProcessingTime { get; set; }
    public double MessagesPerSecond { get; set; }
    public long MemoryUsage { get; set; }
    public DateTime StartTime { get; set; }
    public TimeSpan Uptime { get; set; }
}

/// <summary>
/// Performance monitoring configuration
/// </summary>
public class PerformanceMonitoringConfiguration
{
    public bool EnableDetailedMetrics { get; set; } = true;
    public TimeSpan MetricsUpdateInterval { get; set; } = TimeSpan.FromSeconds(10);
    public int MetricsHistorySize { get; set; } = 100;
    public bool EnableMemoryMonitoring { get; set; } = true;
    public bool EnableLatencyTracking { get; set; } = true;
}

/// <summary>
/// Event arguments for connection metrics updates
/// </summary>
public class ConnectionMetricsEventArgs : EventArgs
{
    public required PerformanceMetrics Metrics { get; init; }
    public required DateTime Timestamp { get; init; }
}

/// <summary>
/// Event arguments for options updates
/// </summary>
public class PolygonOptionsUpdateEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required decimal Price { get; init; }
    public required long Size { get; init; }
    public required DateTime Timestamp { get; init; }
    public required string Exchange { get; init; }
    public decimal? ImpliedVolatility { get; init; }
    public decimal? Delta { get; init; }
    public decimal? Gamma { get; init; }
    public decimal? Theta { get; init; }
    public decimal? Vega { get; init; }
}

/// <summary>
/// Event arguments for forex updates
/// </summary>
public class PolygonForexUpdateEventArgs : EventArgs
{
    public required string Pair { get; init; }
    public required decimal BidPrice { get; init; }
    public required decimal AskPrice { get; init; }
    public required DateTime Timestamp { get; init; }
    public required string Exchange { get; init; }
}

/// <summary>
/// Event arguments for crypto updates
/// </summary>
public class PolygonCryptoUpdateEventArgs : EventArgs
{
    public required string Pair { get; init; }
    public required decimal Price { get; init; }
    public required decimal Size { get; init; }
    public required DateTime Timestamp { get; init; }
    public required string Exchange { get; init; }
}
