using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using System.Net;
using System.Text;
using System.Text.Json;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Comprehensive tests for ChatGptNlpService
/// Tests NLP functionality, API integration, caching, and error handling
/// </summary>
public class ChatGptNlpServiceTests : IDisposable
{
    private readonly Mock<HttpMessageHandler> _mockHttpHandler;
    private readonly Mock<ILiveStateStore> _mockLiveStateStore;
    private readonly Mock<ILogger<ChatGptNlpService>> _mockLogger;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly HttpClient _httpClient;
    private readonly ChatGptNlpService _service;

    public ChatGptNlpServiceTests()
    {
        _mockHttpHandler = new Mock<HttpMessageHandler>();
        _mockLiveStateStore = new Mock<ILiveStateStore>();
        _mockLogger = new Mock<ILogger<ChatGptNlpService>>();
        _mockConfiguration = new Mock<IConfiguration>();

        _httpClient = new HttpClient(_mockHttpHandler.Object);

        // Setup configuration
        _mockConfiguration.Setup(c => c["OPENAI_API_KEY"]).Returns("test-api-key");
        _mockConfiguration.Setup(c => c["OPENAI_MODEL"]).Returns("gpt-4");
        _mockConfiguration.Setup(c => c["OPENAI_MAX_TOKENS"]).Returns("1000");
        _mockConfiguration.Setup(c => c["OPENAI_TEMPERATURE"]).Returns("0.7");
        _mockConfiguration.Setup(c => c["OPENAI_RATE_LIMIT_PER_MINUTE"]).Returns("60");
        _mockConfiguration.Setup(c => c["NLP_CACHE_HOURS"]).Returns("24");

        _service = new ChatGptNlpService(_httpClient, _mockLiveStateStore.Object, _mockConfiguration.Object, _mockLogger.Object);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task SummarizeEarningsAsync_WithValidResponse_ShouldReturnSummary()
    {
        // Arrange
        var symbol = "AAPL";
        var earningsText = "Apple reported strong Q4 results with revenue of $89.5B...";
        
        var mockResponse = new
        {
            choices = new[]
            {
                new
                {
                    message = new
                    {
                        content = JsonSerializer.Serialize(new
                        {
                            summary = "Apple reported strong Q4 results",
                            sentiment = "Positive",
                            keyHighlights = new[] { "Revenue beat expectations", "Strong iPhone sales" }
                        })
                    }
                }
            }
        };

        SetupHttpResponse(HttpStatusCode.OK, JsonSerializer.Serialize(mockResponse));

        // Act
        var result = await _service.SummarizeEarningsAsync(symbol, earningsText);

        // Assert
        result.Should().NotBeNull();
        result.Symbol.Should().Be(symbol);
        result.Summary.Should().Be("Apple reported strong Q4 results");
        result.Sentiment.Should().Be(SentimentScore.Positive);
        result.KeyHighlights.Should().HaveCount(2);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task SummarizeEarningsAsync_WithApiError_ShouldThrowException()
    {
        // Arrange
        var symbol = "AAPL";
        var earningsText = "Test earnings text";
        
        SetupHttpResponse(HttpStatusCode.BadRequest, "API Error");

        // Act & Assert
        await Assert.ThrowsAsync<HttpRequestException>(() => 
            _service.SummarizeEarningsAsync(symbol, earningsText));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task AnalyzeSentimentAsync_WithValidResponse_ShouldReturnAnalysis()
    {
        // Arrange
        var text = "The market is looking very bullish today with strong momentum.";
        var context = "Market analysis";
        
        var mockResponse = new
        {
            choices = new[]
            {
                new
                {
                    message = new
                    {
                        content = JsonSerializer.Serialize(new
                        {
                            sentiment = "Positive",
                            confidence = 0.85,
                            reasoning = "Bullish language indicates positive sentiment"
                        })
                    }
                }
            }
        };

        SetupHttpResponse(HttpStatusCode.OK, JsonSerializer.Serialize(mockResponse));

        // Act
        var result = await _service.AnalyzeSentimentAsync(text, context);

        // Assert
        result.Should().NotBeNull();
        result.Score.Should().Be(SentimentScore.Positive);
        result.Confidence.Should().Be(0.85m);
        result.Reasoning.Should().Be("Bullish language indicates positive sentiment");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task AnalyzeSentimentAsync_WithNeutralSentiment_ShouldReturnNeutral()
    {
        // Arrange
        var text = "The market moved sideways today with mixed signals.";
        
        var mockResponse = new
        {
            choices = new[]
            {
                new
                {
                    message = new
                    {
                        content = JsonSerializer.Serialize(new
                        {
                            sentiment = "Neutral",
                            confidence = 0.70,
                            reasoning = "Mixed signals indicate neutral sentiment"
                        })
                    }
                }
            }
        };

        SetupHttpResponse(HttpStatusCode.OK, JsonSerializer.Serialize(mockResponse));

        // Act
        var result = await _service.AnalyzeSentimentAsync(text);

        // Assert
        result.Should().NotBeNull();
        result.Score.Should().Be(SentimentScore.Neutral);
        result.Confidence.Should().Be(0.70m);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GenerateLogEntryAsync_WithValidInput_ShouldReturnLogEntry()
    {
        // Arrange
        var action = "BUY";
        var symbol = "AAPL";
        var price = 150.00m;
        var reasoning = "Strong momentum and bullish trend";
        
        var mockResponse = new
        {
            choices = new[]
            {
                new
                {
                    message = new
                    {
                        content = "Executed BUY order for AAPL at $150.00 based on strong momentum and bullish trend analysis."
                    }
                }
            }
        };

        SetupHttpResponse(HttpStatusCode.OK, JsonSerializer.Serialize(mockResponse));

        // Act
        var result = await _service.GenerateLogEntryAsync(action, symbol, price, reasoning);

        // Assert
        result.Should().NotBeNull();
        result.Should().Contain("BUY");
        result.Should().Contain("AAPL");
        result.Should().Contain("$150.00");
        result.Should().Contain("momentum");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ExtractInsightsAsync_WithValidResponse_ShouldReturnInsights()
    {
        // Arrange
        var text = "Apple's iPhone sales exceeded expectations while services revenue grew 16% year-over-year.";
        var symbol = "AAPL";
        
        var mockResponse = new
        {
            choices = new[]
            {
                new
                {
                    message = new
                    {
                        content = JsonSerializer.Serialize(new[]
                        {
                            new { type = "Revenue", description = "iPhone sales beat expectations", importance = 3, timeframe = "Q4" },
                            new { type = "Growth", description = "Services revenue growth accelerated", importance = 2, timeframe = "Q4" }
                        })
                    }
                }
            }
        };

        SetupHttpResponse(HttpStatusCode.OK, JsonSerializer.Serialize(mockResponse));

        // Act
        var result = await _service.ExtractInsightsAsync(text, symbol);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.First().Description.Should().Be("iPhone sales beat expectations");
        result.First().Importance.Should().Be(3);
        result.First().Type.Should().Be("Revenue");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GenerateRiskCommentaryAsync_WithValidInput_ShouldReturnCommentary()
    {
        // Arrange
        var symbol = "TSLA";
        var volatility = 0.35m;
        var momentum = 0.15m;
        var marketCondition = "High Volatility";
        
        var mockResponse = new
        {
            choices = new[]
            {
                new
                {
                    message = new
                    {
                        content = "TSLA shows elevated risk with 35% volatility in current high volatility market conditions. Consider reduced position sizing."
                    }
                }
            }
        };

        SetupHttpResponse(HttpStatusCode.OK, JsonSerializer.Serialize(mockResponse));

        // Act
        var result = await _service.GenerateRiskCommentaryAsync(symbol, volatility, momentum, marketCondition);

        // Assert
        result.Should().NotBeNull();
        result.Should().Contain("TSLA");
        result.Should().Contain("35%");
        result.Should().Contain("volatility");
        result.Should().Contain("risk");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task SummarizeEarningsAsync_WithCaching_ShouldUseCachedResult()
    {
        // Arrange
        var symbol = "AAPL";
        var earningsText = "Same earnings text";
        
        var mockResponse = new
        {
            choices = new[]
            {
                new
                {
                    message = new
                    {
                        content = JsonSerializer.Serialize(new
                        {
                            summary = "Cached summary",
                            sentiment = "Positive",
                            keyHighlights = new[] { "Cached highlight" }
                        })
                    }
                }
            }
        };

        SetupHttpResponse(HttpStatusCode.OK, JsonSerializer.Serialize(mockResponse));

        // Act - Call twice with same input
        var result1 = await _service.SummarizeEarningsAsync(symbol, earningsText);
        var result2 = await _service.SummarizeEarningsAsync(symbol, earningsText);

        // Assert
        result1.Should().NotBeNull();
        result2.Should().NotBeNull();
        result1.Summary.Should().Be(result2.Summary);
        
        // Verify HTTP was only called once due to caching
        _mockHttpHandler.Protected().Verify(
            "SendAsync",
            Times.Once(),
            ItExpr.IsAny<HttpRequestMessage>(),
            ItExpr.IsAny<CancellationToken>());
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task AnalyzeSentimentAsync_WithRateLimiting_ShouldRespectLimits()
    {
        // Arrange
        var text = "Test sentiment text";
        
        var mockResponse = new
        {
            choices = new[]
            {
                new
                {
                    message = new
                    {
                        content = JsonSerializer.Serialize(new
                        {
                            sentiment = "Neutral",
                            confidence = 0.5,
                            reasoning = "Test reasoning"
                        })
                    }
                }
            }
        };

        SetupHttpResponse(HttpStatusCode.OK, JsonSerializer.Serialize(mockResponse));

        // Act - Make multiple rapid calls
        var tasks = new List<Task<SentimentAnalysis>>();
        for (int i = 0; i < 5; i++)
        {
            tasks.Add(_service.AnalyzeSentimentAsync($"{text} {i}"));
        }

        var results = await Task.WhenAll(tasks);

        // Assert
        results.Should().HaveCount(5);
        results.Should().OnlyContain(r => r != null);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ExtractInsightsAsync_WithEmptyResponse_ShouldReturnEmptyList()
    {
        // Arrange
        var text = "No meaningful insights here.";
        
        var mockResponse = new
        {
            choices = new[]
            {
                new
                {
                    message = new
                    {
                        content = JsonSerializer.Serialize(Array.Empty<object>())
                    }
                }
            }
        };

        SetupHttpResponse(HttpStatusCode.OK, JsonSerializer.Serialize(mockResponse));

        // Act
        var result = await _service.ExtractInsightsAsync(text);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GenerateLogEntryAsync_WithCancellation_ShouldRespectCancellation()
    {
        // Arrange
        var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(() => 
            _service.GenerateLogEntryAsync("BUY", "AAPL", 150m, "test", cts.Token));
    }

    private void SetupHttpResponse(HttpStatusCode statusCode, string content)
    {
        var response = new HttpResponseMessage(statusCode)
        {
            Content = new StringContent(content, Encoding.UTF8, "application/json")
        };

        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(response);
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
        _service?.Dispose();
    }
}
