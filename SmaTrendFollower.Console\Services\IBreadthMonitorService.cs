using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time market breadth monitoring service
/// Tracks advancing/declining stock count across dynamic universe
/// Calculates % above 50SMA/200SMA, new highs/lows
/// Injects breadth context into signal engine with real-time updates
/// </summary>
public interface IBreadthMonitorService : IDisposable
{
    // === Events ===
    
    /// <summary>
    /// Fired when breadth analysis is updated
    /// </summary>
    event EventHandler<BreadthAnalysisUpdatedEventArgs>? BreadthAnalysisUpdated;
    
    /// <summary>
    /// Fired when market breadth regime changes
    /// </summary>
    event EventHandler<BreadthRegimeChangedEventArgs>? BreadthRegimeChanged;
    
    /// <summary>
    /// Fired when breadth divergence is detected
    /// </summary>
    event EventHandler<BreadthDivergenceEventArgs>? BreadthDivergenceDetected;
    
    /// <summary>
    /// Fired when extreme breadth conditions are detected
    /// </summary>
    event EventHandler<ExtremeBreadthEventArgs>? ExtremeBreadthDetected;
    
    // === Core Methods ===
    
    /// <summary>
    /// Start real-time breadth monitoring
    /// </summary>
    Task StartMonitoringAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stop real-time breadth monitoring
    /// </summary>
    Task StopMonitoringAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get current real-time breadth analysis
    /// </summary>
    Task<RealTimeBreadthAnalysis> GetCurrentBreadthAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get cached breadth analysis
    /// </summary>
    Task<RealTimeBreadthAnalysis?> GetCachedBreadthAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Force breadth analysis refresh
    /// </summary>
    Task<RealTimeBreadthAnalysis> RefreshBreadthAnalysisAsync(CancellationToken cancellationToken = default);
    
    // === Analysis Methods ===
    
    /// <summary>
    /// Get advance/decline statistics
    /// </summary>
    Task<AdvanceDeclineStats> GetAdvanceDeclineStatsAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get moving average breadth statistics
    /// </summary>
    Task<MovingAverageBreadthStats> GetMovingAverageBreadthAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get new highs/lows statistics
    /// </summary>
    Task<NewHighsLowsStats> GetNewHighsLowsStatsAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get breadth momentum analysis
    /// </summary>
    Task<BreadthMomentumAnalysis> GetBreadthMomentumAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get breadth divergence analysis vs major indices
    /// </summary>
    Task<BreadthDivergenceAnalysis> GetBreadthDivergenceAsync(CancellationToken cancellationToken = default);
    
    // === Signal Integration ===
    
    /// <summary>
    /// Get breadth signal strength for trading decisions
    /// </summary>
    Task<BreadthSignalStrength> GetBreadthSignalStrengthAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Check if breadth supports bullish signals
    /// </summary>
    Task<bool> SupportsBullishSignalsAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Check if breadth suggests defensive positioning
    /// </summary>
    Task<bool> SuggestsDefensivePositioningAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get breadth-adjusted position sizing recommendation
    /// </summary>
    Task<decimal> GetBreadthAdjustedPositionSizeAsync(decimal baseSize, CancellationToken cancellationToken = default);
    
    // === Status and Configuration ===
    
    /// <summary>
    /// Get current monitoring status
    /// </summary>
    BreadthMonitorStatus GetStatus();
    
    /// <summary>
    /// Get monitored universe size
    /// </summary>
    int GetMonitoredUniverseSize();
    
    /// <summary>
    /// Update configuration
    /// </summary>
    Task UpdateConfigurationAsync(BreadthMonitorConfig config);
    
    /// <summary>
    /// Get breadth monitoring statistics
    /// </summary>
    Task<BreadthMonitoringStats> GetMonitoringStatsAsync();
}

/// <summary>
/// Real-time breadth analysis data
/// </summary>
public class RealTimeBreadthAnalysis
{
    public DateTime AnalyzedAt { get; set; }
    public int UniverseSize { get; set; }
    public AdvanceDeclineStats AdvanceDecline { get; set; } = new();
    public MovingAverageBreadthStats MovingAverageBreadth { get; set; } = new();
    public NewHighsLowsStats NewHighsLows { get; set; } = new();
    public BreadthMomentumAnalysis Momentum { get; set; } = new();
    public BreadthDivergenceAnalysis Divergence { get; set; } = new();
    public BreadthRegime Regime { get; set; }
    public BreadthSignalStrength SignalStrength { get; set; }
    public decimal OverallBreadthScore { get; set; }
    public string Summary { get; set; } = string.Empty;
}

/// <summary>
/// Advance/decline statistics
/// </summary>
public class AdvanceDeclineStats
{
    public int Advancing { get; set; }
    public int Declining { get; set; }
    public int Unchanged { get; set; }
    public decimal AdvanceDeclineRatio { get; set; }
    public decimal AdvanceDeclinePercent { get; set; }
    public decimal CumulativeAdvanceDecline { get; set; }
    public AdvanceDeclineTrend Trend { get; set; }
}

/// <summary>
/// Moving average breadth statistics
/// </summary>
public class MovingAverageBreadthStats
{
    public int Above50SMA { get; set; }
    public int Below50SMA { get; set; }
    public decimal PercentAbove50SMA { get; set; }
    public int Above200SMA { get; set; }
    public int Below200SMA { get; set; }
    public decimal PercentAbove200SMA { get; set; }
    public MovingAverageTrend Trend50SMA { get; set; }
    public MovingAverageTrend Trend200SMA { get; set; }
}

/// <summary>
/// New highs/lows statistics
/// </summary>
public class NewHighsLowsStats
{
    public int NewHighs52Week { get; set; }
    public int NewLows52Week { get; set; }
    public int NewHighs20Day { get; set; }
    public int NewLows20Day { get; set; }
    public decimal HighLowRatio { get; set; }
    public decimal HighLowPercent { get; set; }
    public NewHighsLowsTrend Trend { get; set; }
}

/// <summary>
/// Breadth momentum analysis
/// </summary>
public class BreadthMomentumAnalysis
{
    public decimal BreadthMomentum1Day { get; set; }
    public decimal BreadthMomentum5Day { get; set; }
    public decimal BreadthMomentum20Day { get; set; }
    public decimal BreadthAcceleration { get; set; }
    public BreadthMomentumDirection Direction { get; set; }
    public BreadthMomentumStrength Strength { get; set; }
}

/// <summary>
/// Breadth divergence analysis vs indices
/// </summary>
public class BreadthDivergenceAnalysis
{
    public decimal SpyBreadthDivergence { get; set; }
    public decimal QqqBreadthDivergence { get; set; }
    public decimal IwmBreadthDivergence { get; set; }
    public BreadthDivergenceType DivergenceType { get; set; }
    public BreadthDivergenceSignificance Significance { get; set; }
    public bool IsWarningSignal { get; set; }
}

/// <summary>
/// Breadth monitoring configuration
/// </summary>
public record BreadthMonitorConfig(
    int UpdateIntervalSeconds = 30,
    int MinUniverseSize = 100,
    decimal ExtremeBreadthThreshold = 0.9m,
    decimal DivergenceThreshold = 0.3m,
    int MomentumLookbackDays = 20,
    bool EnableRealTimeUpdates = true,
    int CacheExpiryMinutes = 5
);

/// <summary>
/// Breadth monitoring status
/// </summary>
public enum BreadthMonitorStatus
{
    Stopped,
    Starting,
    Active,
    Error
}

/// <summary>
/// Breadth regime classification
/// </summary>
public enum BreadthRegime
{
    Healthy,
    Deteriorating,
    Weak,
    Extreme,
    Recovering
}

/// <summary>
/// Breadth signal strength
/// </summary>
public enum BreadthSignalStrength
{
    VeryWeak,
    Weak,
    Neutral,
    Strong,
    VeryStrong
}

/// <summary>
/// Advance/decline trend
/// </summary>
public enum AdvanceDeclineTrend
{
    Improving,
    Stable,
    Deteriorating
}

/// <summary>
/// Moving average trend
/// </summary>
public enum MovingAverageTrend
{
    Improving,
    Stable,
    Deteriorating
}

/// <summary>
/// New highs/lows trend
/// </summary>
public enum NewHighsLowsTrend
{
    Improving,
    Stable,
    Deteriorating
}

/// <summary>
/// Breadth momentum direction
/// </summary>
public enum BreadthMomentumDirection
{
    Bullish,
    Neutral,
    Bearish
}

/// <summary>
/// Breadth momentum strength
/// </summary>
public enum BreadthMomentumStrength
{
    Weak,
    Moderate,
    Strong
}

/// <summary>
/// Breadth divergence type
/// </summary>
public enum BreadthDivergenceType
{
    Bullish,
    Bearish,
    Neutral
}

/// <summary>
/// Breadth divergence significance
/// </summary>
public enum BreadthDivergenceSignificance
{
    Insignificant,
    Mild,
    Moderate,
    Strong
}

/// <summary>
/// Event args for breadth analysis updated
/// </summary>
public class BreadthAnalysisUpdatedEventArgs : EventArgs
{
    public RealTimeBreadthAnalysis Analysis { get; set; } = new();
    public DateTime UpdatedAt { get; set; }
    public TimeSpan UpdateDuration { get; set; }
}

/// <summary>
/// Event args for breadth regime changed
/// </summary>
public class BreadthRegimeChangedEventArgs : EventArgs
{
    public BreadthRegime PreviousRegime { get; set; }
    public BreadthRegime NewRegime { get; set; }
    public DateTime ChangedAt { get; set; }
    public string Reason { get; set; } = string.Empty;
    public decimal Confidence { get; set; }
}

/// <summary>
/// Event args for breadth divergence detected
/// </summary>
public class BreadthDivergenceEventArgs : EventArgs
{
    public BreadthDivergenceType DivergenceType { get; set; }
    public BreadthDivergenceSignificance Significance { get; set; }
    public decimal DivergenceScore { get; set; }
    public string IndexSymbol { get; set; } = string.Empty;
    public DateTime DetectedAt { get; set; }
}

/// <summary>
/// Event args for extreme breadth detected
/// </summary>
public class ExtremeBreadthEventArgs : EventArgs
{
    public ExtremeBreadthType ExtremeBreadthType { get; set; }
    public decimal BreadthValue { get; set; }
    public decimal Threshold { get; set; }
    public DateTime DetectedAt { get; set; }
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Breadth monitoring statistics
/// </summary>
public class BreadthMonitoringStats
{
    public DateTime LastUpdate { get; set; }
    public TimeSpan AverageUpdateTime { get; set; }
    public int TotalUpdates { get; set; }
    public int FailedUpdates { get; set; }
    public decimal SuccessRate { get; set; }
    public Dictionary<BreadthRegime, int> RegimeHistory { get; set; } = new();
    public DateTime MonitoringStartedAt { get; set; }
    public TimeSpan TotalMonitoringTime { get; set; }
}

/// <summary>
/// Extreme breadth type
/// </summary>
public enum ExtremeBreadthType
{
    ExtremelyBullish,
    ExtremelyBearish,
    ExtremelyNarrow,
    ExtremelyBroad
}
