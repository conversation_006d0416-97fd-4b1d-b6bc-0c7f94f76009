using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for real-time options flow analysis service
/// Leverages Polygon Developer subscription for enhanced options intelligence
/// </summary>
public interface IOptionsFlowAnalysisService : IDisposable
{
    // === Events ===
    
    /// <summary>
    /// Fired when unusual options activity is detected
    /// </summary>
    event EventHandler<UnusualOptionsActivityEventArgs>? UnusualActivityDetected;
    
    /// <summary>
    /// Fired when options flow sentiment changes significantly
    /// </summary>
    event EventHandler<OptionsFlowSentimentEventArgs>? SentimentChanged;
    
    /// <summary>
    /// Fired when large block trades are detected
    /// </summary>
    event EventHandler<LargeBlockTradeEventArgs>? LargeBlockDetected;
    
    // === Properties ===
    
    /// <summary>
    /// Current options flow sentiment
    /// </summary>
    OptionsFlowSentiment CurrentSentiment { get; }
    
    /// <summary>
    /// Number of symbols being monitored
    /// </summary>
    int MonitoredSymbols { get; }
    
    // === Analysis Methods ===
    
    /// <summary>
    /// Analyzes options flow for a specific symbol
    /// </summary>
    Task<OptionsFlowAnalysis> AnalyzeOptionsFlowAsync(string symbol, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets current put/call ratio for a symbol
    /// </summary>
    Task<PutCallRatioAnalysis> GetPutCallRatioAsync(string symbol, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Detects unusual options volume activity
    /// </summary>
    Task<UnusualVolumeAnalysis> DetectUnusualVolumeAsync(string symbol, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Analyzes options gamma exposure for market impact
    /// </summary>
    Task<GammaExposureAnalysis> AnalyzeGammaExposureAsync(string symbol, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets dark pool and block trade analysis
    /// </summary>
    Task<DarkPoolAnalysis> AnalyzeDarkPoolActivityAsync(string symbol, CancellationToken cancellationToken = default);
    
    // === Monitoring Methods ===
    
    /// <summary>
    /// Starts monitoring options flow for specified symbols
    /// </summary>
    Task StartMonitoringAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stops monitoring options flow
    /// </summary>
    Task StopMonitoringAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Adds symbols to monitoring list
    /// </summary>
    Task AddSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Removes symbols from monitoring list
    /// </summary>
    Task RemoveSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    // === Historical Analysis ===
    
    /// <summary>
    /// Gets historical options flow summary
    /// </summary>
    Task<OptionsFlowSummary> GetHistoricalFlowSummaryAsync(string symbol, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets options flow correlation with price movements
    /// </summary>
    Task<FlowCorrelationAnalysis> GetFlowCorrelationAsync(string symbol, TimeSpan lookbackPeriod, CancellationToken cancellationToken = default);
}

/// <summary>
/// Options flow sentiment levels
/// </summary>
public enum OptionsFlowSentiment
{
    ExtremeFear,
    Fear,
    Neutral,
    Greed,
    ExtremeGreed
}

/// <summary>
/// Options flow analysis result
/// </summary>
public readonly record struct OptionsFlowAnalysis(
    string Symbol,
    DateTime AnalysisTime,
    OptionsFlowSentiment Sentiment,
    PutCallRatioAnalysis PutCallRatio,
    UnusualVolumeAnalysis UnusualVolume,
    GammaExposureAnalysis GammaExposure,
    DarkPoolAnalysis DarkPool,
    decimal FlowScore,
    string Analysis,
    List<string> KeySignals
);

/// <summary>
/// Put/call ratio analysis
/// </summary>
public readonly record struct PutCallRatioAnalysis(
    decimal CurrentRatio,
    decimal AverageRatio,
    decimal ZScore,
    bool IsExtreme,
    string Interpretation
);

/// <summary>
/// Unusual volume analysis
/// </summary>
public readonly record struct UnusualVolumeAnalysis(
    long CurrentVolume,
    long AverageVolume,
    decimal VolumeMultiplier,
    bool IsUnusual,
    List<UnusualOptionsContract> UnusualContracts
);

/// <summary>
/// Unusual options contract data
/// </summary>
public readonly record struct UnusualOptionsContract(
    string Symbol,
    string OptionType,
    decimal Strike,
    DateTime Expiration,
    long Volume,
    long AverageVolume,
    decimal VolumeMultiplier,
    decimal Premium,
    decimal ImpliedVolatility
);

/// <summary>
/// Gamma exposure analysis
/// </summary>
public readonly record struct GammaExposureAnalysis(
    decimal TotalGammaExposure,
    decimal NetGammaExposure,
    decimal GammaFlipLevel,
    bool IsAboveGammaFlip,
    Dictionary<decimal, decimal> GammaByStrike,
    string MarketImpact
);

/// <summary>
/// Dark pool activity analysis
/// </summary>
public readonly record struct DarkPoolAnalysis(
    decimal DarkPoolVolume,
    decimal TotalVolume,
    decimal DarkPoolPercentage,
    bool IsHighDarkPoolActivity,
    List<LargeBlockTrade> LargeBlocks,
    string Analysis
);

/// <summary>
/// Large block trade data
/// </summary>
public readonly record struct LargeBlockTrade(
    string Symbol,
    decimal Price,
    long Size,
    DateTime Timestamp,
    string Exchange,
    bool IsDarkPool,
    decimal NotionalValue
);

/// <summary>
/// Options flow summary for historical analysis
/// </summary>
public readonly record struct OptionsFlowSummary(
    string Symbol,
    DateTime StartDate,
    DateTime EndDate,
    long TotalOptionsVolume,
    decimal AveragePutCallRatio,
    int UnusualActivityDays,
    decimal FlowAccuracy,
    List<SignificantFlowEvent> SignificantEvents
);

/// <summary>
/// Significant options flow event
/// </summary>
public readonly record struct SignificantFlowEvent(
    DateTime Date,
    string EventType,
    string Description,
    decimal Impact,
    decimal PriceChange
);

/// <summary>
/// Flow correlation analysis
/// </summary>
public readonly record struct FlowCorrelationAnalysis(
    decimal Correlation,
    decimal PredictiveAccuracy,
    TimeSpan OptimalLookAhead,
    Dictionary<string, decimal> FactorCorrelations,
    string Analysis
);

/// <summary>
/// Event arguments for unusual options activity
/// </summary>
public class UnusualOptionsActivityEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required UnusualVolumeAnalysis Analysis { get; init; }
    public required DateTime DetectedAt { get; init; }
    public required string AlertMessage { get; init; }
}

/// <summary>
/// Event arguments for options flow sentiment changes
/// </summary>
public class OptionsFlowSentimentEventArgs : EventArgs
{
    public required OptionsFlowSentiment PreviousSentiment { get; init; }
    public required OptionsFlowSentiment CurrentSentiment { get; init; }
    public required DateTime ChangeTime { get; init; }
    public required string Reason { get; init; }
}

/// <summary>
/// Event arguments for large block trades
/// </summary>
public class LargeBlockTradeEventArgs : EventArgs
{
    public required LargeBlockTrade Trade { get; init; }
    public required DateTime DetectedAt { get; init; }
    public required string Analysis { get; init; }
}
