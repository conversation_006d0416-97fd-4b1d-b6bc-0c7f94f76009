using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time microstructure tracker for individual symbols
/// Maintains tick sequences and detects microstructure patterns
/// </summary>
internal sealed class MicrostructureTracker
{
    private readonly string _symbol;
    private readonly ILogger _logger;
    private MicrostructurePatternConfig _config;
    
    private readonly ConcurrentQueue<TickSequencePoint> _tickSequence = new();
    private decimal _currentPrice;
    private decimal _bidPrice;
    private decimal _askPrice;
    private long _bidSize;
    private long _askSize;
    private DateTime _lastUpdate = DateTime.UtcNow;
    private readonly object _calculationLock = new();
    
    // Pattern tracking state
    private int _consecutiveUpticks;
    private int _consecutiveDownticks;
    private decimal _lastTradePrice;
    private PatternType _activePattern = PatternType.None;
    private DateTime _patternStartTime = DateTime.UtcNow;
    
    public MicrostructureTracker(string symbol, MicrostructurePatternConfig config, ILogger logger)
    {
        _symbol = symbol ?? throw new ArgumentNullException(nameof(symbol));
        _config = config ?? throw new ArgumentNullException(nameof(config));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }
    
    public MicrostructureAnalysis? UpdateWithTrade(decimal price, long volume, DateTime timestamp)
    {
        lock (_calculationLock)
        {
            _currentPrice = price;
            _lastUpdate = timestamp;
            
            // Determine tick direction
            var tickDirection = DetermineTickDirection(price);
            
            // Add to tick sequence
            var sequencePoint = new TickSequencePoint(price, volume, timestamp, TickSequenceType.Trade, tickDirection);
            _tickSequence.Enqueue(sequencePoint);
            
            // Update consecutive tick counters
            UpdateConsecutiveTickCounters(tickDirection);
            
            // Clean old sequence data
            CleanOldSequenceData(timestamp);
            
            // Update pattern detection
            UpdatePatternDetection();
            
            _lastTradePrice = price;
            
            return AnalyzeMicrostructure(timestamp);
        }
    }
    
    public MicrostructureAnalysis? UpdateWithQuote(decimal bidPrice, decimal askPrice, long bidSize, long askSize, DateTime timestamp)
    {
        lock (_calculationLock)
        {
            _bidPrice = bidPrice;
            _askPrice = askPrice;
            _bidSize = bidSize;
            _askSize = askSize;
            _lastUpdate = timestamp;
            
            // Use mid-price if no recent trade
            if (_currentPrice == 0 || timestamp - _lastUpdate > TimeSpan.FromSeconds(10))
            {
                _currentPrice = (bidPrice + askPrice) / 2;
            }
            
            // Add quote to sequence
            var sequencePoint = new TickSequencePoint((bidPrice + askPrice) / 2, 0, timestamp, TickSequenceType.Quote, TickDirection.None);
            _tickSequence.Enqueue(sequencePoint);
            
            // Clean old sequence data
            CleanOldSequenceData(timestamp);
            
            // Update pattern detection
            UpdatePatternDetection();
            
            return AnalyzeMicrostructure(timestamp);
        }
    }
    
    public MicrostructureAnalysis? GetCurrentAnalysis()
    {
        lock (_calculationLock)
        {
            return AnalyzeMicrostructure(DateTime.UtcNow);
        }
    }
    
    public void UpdateConfig(MicrostructurePatternConfig config)
    {
        _config = config;
    }
    
    private TickDirection DetermineTickDirection(decimal currentPrice)
    {
        if (_lastTradePrice == 0)
            return TickDirection.None;
            
        var priceChange = currentPrice - _lastTradePrice;
        var changePercent = Math.Abs(priceChange) / _lastTradePrice * 100;
        
        if (changePercent < _config.MinUptickThreshold)
            return TickDirection.None;
            
        return priceChange > 0 ? TickDirection.Up : TickDirection.Down;
    }
    
    private void UpdateConsecutiveTickCounters(TickDirection direction)
    {
        switch (direction)
        {
            case TickDirection.Up:
                _consecutiveUpticks++;
                _consecutiveDownticks = 0;
                break;
            case TickDirection.Down:
                _consecutiveDownticks++;
                _consecutiveUpticks = 0;
                break;
            case TickDirection.None:
                // Don't reset counters for flat ticks
                break;
        }
    }
    
    private void UpdatePatternDetection()
    {
        var previousPattern = _activePattern;
        
        // Detect uptick + bid support pattern
        if (_consecutiveUpticks >= _config.MinConsecutiveTicks && _bidPrice > 0 && _currentPrice > 0)
        {
            var bidSupportRatio = _bidPrice / _currentPrice;
            if (bidSupportRatio >= _config.MinBidSupportRatio)
            {
                _activePattern = PatternType.UptickBidSupport;
            }
        }
        // Detect down-tick + spread widening pattern
        else if (_consecutiveDownticks >= _config.MinConsecutiveTicks && _bidPrice > 0 && _askPrice > 0)
        {
            var spread = _askPrice - _bidPrice;
            var spreadPercent = spread / _currentPrice * 100;
            if (spreadPercent >= _config.MaxSpreadWidening)
            {
                _activePattern = PatternType.DowntickSpreadWidening;
            }
        }
        // Detect bid stack building
        else if (_bidSize >= _config.MinLiquidityThreshold && _bidSize > _askSize * 1.5m)
        {
            _activePattern = PatternType.BidStackBuilding;
        }
        // Detect ask stack building
        else if (_askSize >= _config.MinLiquidityThreshold && _askSize > _bidSize * 1.5m)
        {
            _activePattern = PatternType.AskStackBuilding;
        }
        // Detect liquidity drying up
        else if (_bidSize + _askSize < _config.MinLiquidityThreshold)
        {
            _activePattern = PatternType.LiquidityDrying;
        }
        else
        {
            _activePattern = PatternType.None;
        }
        
        // Update pattern start time if pattern changed
        if (_activePattern != previousPattern)
        {
            _patternStartTime = DateTime.UtcNow;
        }
    }
    
    private MicrostructureAnalysis? AnalyzeMicrostructure(DateTime timestamp)
    {
        if (_bidPrice == 0 || _askPrice == 0 || _currentPrice == 0)
            return null;
            
        var spread = _askPrice - _bidPrice;
        var spreadPercent = spread / _currentPrice * 100;
        
        var bidSupportRatio = _bidPrice / _currentPrice;
        var askPressureRatio = _askPrice / _currentPrice;
        
        var liquidityScore = CalculateLiquidityScore();
        var patternStrength = CalculatePatternStrength();
        var condition = DetermineMicrostructureCondition(spreadPercent, liquidityScore, patternStrength);
        
        var isFavorableForLong = DetermineIfFavorableForLong(condition, bidSupportRatio, liquidityScore);
        var isFavorableForShort = DetermineIfFavorableForShort(condition, askPressureRatio, liquidityScore);
        
        return new MicrostructureAnalysis(
            Symbol: _symbol,
            CurrentPrice: _currentPrice,
            BidPrice: _bidPrice,
            AskPrice: _askPrice,
            Spread: spread,
            SpreadPercent: spreadPercent,
            Condition: condition,
            ActivePattern: _activePattern,
            PatternStrength: patternStrength,
            ConsecutiveUpticks: _consecutiveUpticks,
            ConsecutiveDownticks: _consecutiveDownticks,
            BidSupportRatio: bidSupportRatio,
            AskPressureRatio: askPressureRatio,
            BidSize: _bidSize,
            AskSize: _askSize,
            LiquidityScore: liquidityScore,
            LastUpdate: timestamp,
            IsFavorableForLong: isFavorableForLong,
            IsFavorableForShort: isFavorableForShort
        );
    }
    
    private decimal CalculateLiquidityScore()
    {
        var totalLiquidity = _bidSize + _askSize;
        var liquidityBalance = Math.Min(_bidSize, _askSize) / (decimal)Math.Max(_bidSize, _askSize);
        
        // Score based on total liquidity and balance
        var liquidityScore = Math.Min(totalLiquidity / _config.MinLiquidityThreshold, 1.0m) * 50;
        var balanceScore = liquidityBalance * 50;
        
        return liquidityScore + balanceScore;
    }
    
    private decimal CalculatePatternStrength()
    {
        var strength = 0m;
        
        switch (_activePattern)
        {
            case PatternType.UptickBidSupport:
                strength += Math.Min(_consecutiveUpticks * 10, 40); // Up to 40 points
                strength += (_bidPrice / _currentPrice - _config.MinBidSupportRatio) * 100; // Bid support strength
                break;
                
            case PatternType.DowntickSpreadWidening:
                strength += Math.Min(_consecutiveDownticks * 10, 40); // Up to 40 points
                strength += Math.Min((_askPrice - _bidPrice) / _currentPrice * 100 / _config.MaxSpreadWidening * 30, 30); // Spread widening strength
                break;
                
            case PatternType.BidStackBuilding:
                strength += Math.Min(_bidSize / _config.MinLiquidityThreshold * 50, 70);
                break;
                
            case PatternType.AskStackBuilding:
                strength += Math.Min(_askSize / _config.MinLiquidityThreshold * 50, 70);
                break;
                
            case PatternType.LiquidityDrying:
                strength += Math.Max(50 - (_bidSize + _askSize) / _config.MinLiquidityThreshold * 50, 0);
                break;
        }
        
        // Time decay factor
        var patternAge = DateTime.UtcNow - _patternStartTime;
        if (patternAge > _config.PatternValidityPeriod)
        {
            var decayFactor = 1 - (decimal)patternAge.TotalMinutes / (decimal)_config.PatternValidityPeriod.TotalMinutes;
            strength *= Math.Max(decayFactor, 0.1m);
        }
        
        return Math.Min(strength, 100m);
    }
    
    private MicrostructureCondition DetermineMicrostructureCondition(decimal spreadPercent, decimal liquidityScore, decimal patternStrength)
    {
        if (patternStrength >= 80 && liquidityScore >= 70 && spreadPercent <= 0.5m)
            return MicrostructureCondition.Favorable;
        if (patternStrength >= 60 && liquidityScore >= 50 && spreadPercent <= 1.0m)
            return MicrostructureCondition.Neutral;
        if (liquidityScore < 30 || spreadPercent > 2.0m)
            return MicrostructureCondition.Deteriorating;
        return MicrostructureCondition.Unfavorable;
    }
    
    private bool DetermineIfFavorableForLong(MicrostructureCondition condition, decimal bidSupportRatio, decimal liquidityScore)
    {
        return condition == MicrostructureCondition.Favorable &&
               _activePattern == PatternType.UptickBidSupport &&
               bidSupportRatio >= _config.MinBidSupportRatio &&
               liquidityScore >= 60;
    }
    
    private bool DetermineIfFavorableForShort(MicrostructureCondition condition, decimal askPressureRatio, decimal liquidityScore)
    {
        return condition == MicrostructureCondition.Favorable &&
               _activePattern == PatternType.DowntickSpreadWidening &&
               liquidityScore >= 60;
    }
    
    private void CleanOldSequenceData(DateTime currentTime)
    {
        var cutoffTime = currentTime.AddMinutes(-5); // Keep 5 minutes of data
        
        while (_tickSequence.TryPeek(out var oldestPoint) && oldestPoint.Timestamp < cutoffTime)
        {
            _tickSequence.TryDequeue(out _);
        }
    }
}

/// <summary>
/// Internal tick sequence point for pattern analysis
/// </summary>
internal readonly record struct TickSequencePoint(
    decimal Price,
    long Volume,
    DateTime Timestamp,
    TickSequenceType Type,
    TickDirection Direction
);

/// <summary>
/// Type of tick sequence data
/// </summary>
internal enum TickSequenceType
{
    Trade,
    Quote
}

/// <summary>
/// Tick direction classification
/// </summary>
internal enum TickDirection
{
    None,
    Up,
    Down
}
