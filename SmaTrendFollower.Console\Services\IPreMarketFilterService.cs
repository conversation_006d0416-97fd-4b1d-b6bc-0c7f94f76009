using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Pre-market filter service for risk management
/// Analyzes pre-market activity to block trades during high volatility or anomalous conditions
/// </summary>
public interface IPreMarketFilterService
{
    /// <summary>
    /// Analyzes pre-market conditions for a symbol and determines if trading should be blocked
    /// </summary>
    /// <param name="symbol">Stock symbol to analyze</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Pre-market analysis result with trading recommendation</returns>
    Task<PreMarketAnalysis> AnalyzePreMarketConditionsAsync(string symbol, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if trading should be blocked for a symbol based on pre-market conditions
    /// </summary>
    /// <param name="symbol">Stock symbol to check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if trading should be blocked, false otherwise</returns>
    Task<bool> ShouldBlockTradingAsync(string symbol, CancellationToken cancellationToken = default);

    /// <summary>
    /// Analyzes pre-market conditions for multiple symbols in batch
    /// </summary>
    /// <param name="symbols">Stock symbols to analyze</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary of symbol to pre-market analysis results</returns>
    Task<IDictionary<string, PreMarketAnalysis>> AnalyzeBatchAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets cached pre-market analysis for a symbol if available
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <returns>Cached analysis or null if not available</returns>
    Task<PreMarketAnalysis?> GetCachedAnalysisAsync(string symbol);

    /// <summary>
    /// Clears cached pre-market analysis data
    /// </summary>
    Task ClearCacheAsync();
}

/// <summary>
/// Pre-market analysis result
/// </summary>
public readonly record struct PreMarketAnalysis(
    string Symbol,
    DateTime AnalysisTime,
    bool ShouldBlockTrading,
    PreMarketRiskLevel RiskLevel,
    decimal VolatilityPercent,
    decimal GapPercent,
    decimal VolumeAnomalyScore,
    string BlockingReason,
    PreMarketMetrics Metrics
);

/// <summary>
/// Pre-market risk levels
/// </summary>
public enum PreMarketRiskLevel
{
    Low,
    Medium,
    High,
    Extreme
}

/// <summary>
/// Pre-market metrics for analysis
/// </summary>
public readonly record struct PreMarketMetrics(
    decimal PreMarketHigh,
    decimal PreMarketLow,
    decimal PreMarketClose,
    decimal PreviousClose,
    long PreMarketVolume,
    long AverageVolume,
    decimal AtrPercent,
    int BarCount
);

/// <summary>
/// Pre-market filter configuration
/// </summary>
public class PreMarketFilterConfig
{
    /// <summary>
    /// Maximum allowed volatility percentage (default: 2%)
    /// </summary>
    public decimal MaxVolatilityPercent { get; set; } = 2.0m;

    /// <summary>
    /// Maximum allowed gap percentage (default: 4%)
    /// </summary>
    public decimal MaxGapPercent { get; set; } = 4.0m;

    /// <summary>
    /// Volume anomaly threshold multiplier (default: 3x average)
    /// </summary>
    public decimal VolumeAnomalyThreshold { get; set; } = 3.0m;

    /// <summary>
    /// Minimum number of pre-market bars required for analysis (default: 5)
    /// </summary>
    public int MinimumBarsRequired { get; set; } = 5;

    /// <summary>
    /// Cache expiry time for pre-market analysis (default: 1 hour)
    /// </summary>
    public TimeSpan CacheExpiry { get; set; } = TimeSpan.FromHours(1);

    /// <summary>
    /// Pre-market session start time (default: 4:00 AM ET)
    /// </summary>
    public TimeOnly PreMarketStartTime { get; set; } = new(4, 0);

    /// <summary>
    /// Pre-market session end time (default: 9:30 AM ET)
    /// </summary>
    public TimeOnly PreMarketEndTime { get; set; } = new(9, 30);
}

/// <summary>
/// Event args for pre-market analysis updates
/// </summary>
public class PreMarketAnalysisEventArgs : EventArgs
{
    public required PreMarketAnalysis Analysis { get; init; }
}

/// <summary>
/// Event args for trading block notifications
/// </summary>
public class TradingBlockEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required string Reason { get; init; }
    public required PreMarketRiskLevel RiskLevel { get; init; }
    public required DateTime BlockTime { get; init; }
}
