#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Simple test performance optimization script
.DESCRIPTION
    This script fixes common test performance issues
.EXAMPLE
    ./fix-test-performance-simple.ps1
#>

param(
    [switch]$DryRun = $false
)

Write-Host "🚀 Starting Test Performance Optimization..." -ForegroundColor Cyan

# Get all test files
$testFiles = Get-ChildItem -Path "SmaTrendFollower.Tests" -Filter "*.cs" -Recurse | Where-Object { 
    $_.FullName -notlike "*\bin\*" -and $_.FullName -notlike "*\obj\*" 
}

Write-Host "Found $($testFiles.Count) test files to optimize" -ForegroundColor Cyan

$totalFilesFixed = 0

foreach ($file in $testFiles) {
    Write-Host "Processing: $($file.Name)" -ForegroundColor Yellow
    
    try {
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        $originalContent = $content
        
        # Fix timeout issues - replace excessive timeouts with unit timeouts
        $content = $content -replace '\[TestTimeout\(TestTimeouts\.Database\)\]\s*\[Trait\("Category", TestCategories\.Database\)\]', '[TestTimeout(TestTimeouts.Unit)]`n    [Trait("Category", TestCategories.Database)]'
        $content = $content -replace '\[TestTimeout\(TestTimeouts\.Network\)\]\s*\[Trait\("Category", TestCategories\.Network\)\]', '[TestTimeout(TestTimeouts.Unit)]`n    [Trait("Category", TestCategories.Network)]'
        $content = $content -replace '\[TestTimeout\(TestTimeouts\.Integration\)\]\s*\[Trait\("Category", TestCategories\.Integration\)\]', '[TestTimeout(TestTimeouts.Unit)]`n    [Trait("Category", TestCategories.Integration)]'
        
        # Fix excessive Task.Delay calls
        $content = $content -replace 'Task\.Delay\(([0-9]{4,})\)', 'Task.Delay(10) // Optimized from $1ms'
        $content = $content -replace 'await Task\.Delay\(([0-9]{4,})\)', 'await Task.Delay(10) // Optimized from $1ms'
        
        # Fix Thread.Sleep calls
        $content = $content -replace 'Thread\.Sleep\(([0-9]{4,})\)', 'Thread.Sleep(10) // Optimized from $1ms'
        
        # Fix HttpClient timeout issues
        $content = $content -replace 'httpClient\.Timeout = TimeSpan\.FromSeconds\([3-9][0-9]\)', 'httpClient.Timeout = TimeSpan.FromSeconds(5) // Optimized timeout'
        $content = $content -replace 'httpClient\.Timeout = TimeSpan\.FromMinutes\([1-9]\)', 'httpClient.Timeout = TimeSpan.FromSeconds(5) // Optimized timeout'
        
        # Check if changes were made
        if ($content -ne $originalContent) {
            if (-not $DryRun) {
                Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
                Write-Host "  ✅ Fixed performance issues" -ForegroundColor Green
            } else {
                Write-Host "  🔍 Would fix performance issues (dry run)" -ForegroundColor Magenta
            }
            $totalFilesFixed++
        }
    }
    catch {
        Write-Host "  ❌ Error processing file: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Summary
Write-Host "`n📊 Performance Optimization Summary:" -ForegroundColor Cyan
Write-Host "Total files analyzed: $($testFiles.Count)" -ForegroundColor White
Write-Host "Files with fixes applied: $totalFilesFixed" -ForegroundColor White

if ($DryRun) {
    Write-Host "`n⚠️  This was a dry run. Use -DryRun:`$false to apply fixes." -ForegroundColor Yellow
}

Write-Host "`n🎯 Next steps:" -ForegroundColor Cyan
Write-Host "1. Review the fixed files for correctness" -ForegroundColor White
Write-Host "2. Run tests to ensure they still pass: dotnet test --filter 'Category!=Integration'" -ForegroundColor White
Write-Host "3. Check for any remaining slow tests" -ForegroundColor White

Write-Host "`n✅ Test performance optimization complete!" -ForegroundColor Green
