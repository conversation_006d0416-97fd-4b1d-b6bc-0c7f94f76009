using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Exceptions;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced trading service with options overlay strategies and VIX-based risk management
/// </summary>
public sealed class EnhancedTradingService : ITradingService
{
    private readonly ISignalGenerator _signalGenerator;
    private readonly IRiskManager _riskManager;
    private readonly IPortfolioGate _portfolioGate;
    private readonly ITradeExecutor _tradeExecutor;
    private readonly IVolatilityManager _volatilityManager;
    private readonly IOptionsStrategyManager _optionsManager;
    private readonly IDiscordNotificationService _discordService;
    private readonly IMarketDataService _marketDataService;
    private readonly IStrategyOptimizationOrchestrator _optimizationOrchestrator;

    // Phase 6: Advanced Filters and Reactive Triggers
    private readonly IVWAPMonitorService _vwapMonitor;
    private readonly ITickVolatilityGuard _volatilityGuard;
    private readonly IRealTimeBreakoutSignal _breakoutSignal;
    private readonly IMicrostructurePatternDetector _microstructureDetector;

    // Phase 6: Real-Time Intelligence & Signal Architecture
    private readonly IIndexRegimeService _indexRegimeService;
    private readonly IVIXResolverService _vixResolverService;
    private readonly IBreadthMonitorService _breadthMonitorService;
    private readonly IRealTimeExecutionService _realTimeExecutionService;

    private readonly ILogger<EnhancedTradingService> _logger;
    private readonly int _tradeDelayMs;

    public EnhancedTradingService(
        ISignalGenerator signalGenerator,
        IRiskManager riskManager,
        IPortfolioGate portfolioGate,
        ITradeExecutor tradeExecutor,
        IVolatilityManager volatilityManager,
        IOptionsStrategyManager optionsManager,
        IDiscordNotificationService discordService,
        IMarketDataService marketDataService,
        IStrategyOptimizationOrchestrator optimizationOrchestrator,
        IVWAPMonitorService vwapMonitor,
        ITickVolatilityGuard volatilityGuard,
        IRealTimeBreakoutSignal breakoutSignal,
        IMicrostructurePatternDetector microstructureDetector,
        IIndexRegimeService indexRegimeService,
        IVIXResolverService vixResolverService,
        IBreadthMonitorService breadthMonitorService,
        IRealTimeExecutionService realTimeExecutionService,
        ILogger<EnhancedTradingService> logger,
        int tradeDelayMs = 1000)
    {
        _signalGenerator = signalGenerator;
        _riskManager = riskManager;
        _portfolioGate = portfolioGate;
        _tradeExecutor = tradeExecutor;
        _volatilityManager = volatilityManager;
        _optionsManager = optionsManager;
        _discordService = discordService;
        _marketDataService = marketDataService;
        _optimizationOrchestrator = optimizationOrchestrator;

        // Phase 6 services
        _vwapMonitor = vwapMonitor;
        _volatilityGuard = volatilityGuard;
        _breakoutSignal = breakoutSignal;
        _microstructureDetector = microstructureDetector;

        // Phase 6: Real-Time Intelligence & Signal Architecture
        _indexRegimeService = indexRegimeService;
        _vixResolverService = vixResolverService;
        _breadthMonitorService = breadthMonitorService;
        _realTimeExecutionService = realTimeExecutionService;

        _logger = logger;
        _tradeDelayMs = tradeDelayMs;
    }

    public async Task ExecuteCycleAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting enhanced trading cycle");

            // Step 1: Check portfolio gate (SPY SMA200)
            if (!await _portfolioGate.ShouldTradeAsync())
            {
                _logger.LogInformation("Portfolio gate blocked trading - SPY below SMA200");
                await SendPortfolioSnapshot("Portfolio gate blocked");
                return;
            }

            // Step 2: Analyze volatility regime
            VolatilityRegime vixRegime;
            try
            {
                vixRegime = await _volatilityManager.GetCurrentRegimeAsync();
                _logger.LogInformation("Current volatility regime: {RegimeName} (VIX: {CurrentVix})",
                    vixRegime.RegimeName, vixRegime.CurrentVix);
            }
            catch (VixDataUnavailableException ex)
            {
                _logger.LogCritical("🚨 VIX DATA UNAVAILABLE - HALTING TRADING FOR 15 MINUTES: {Message}", ex.Message);
                await _discordService.SendMessageAsync($"🚨 **TRADING HALTED** - VIX data unavailable from all sources. Waiting 15 minutes before retry. Time: {DateTime.UtcNow:HH:mm:ss UTC}");

                // Wait for 15 minutes before allowing the next trading cycle
                await Task.Delay(ex.RecommendedWaitTime);

                _logger.LogInformation("15-minute VIX data halt completed, trading cycle will retry");
                await _discordService.SendMessageAsync($"⏰ **TRADING RESUMED** - 15-minute VIX data halt completed. Retrying trading cycle. Time: {DateTime.UtcNow:HH:mm:ss UTC}");
                return; // Exit this cycle, let the next scheduled cycle retry
            }

            // Step 3: Check for VIX spike and send alert if needed
            if (vixRegime.IsVixSpike)
            {
                await _discordService.SendVixSpikeAlertAsync(vixRegime.CurrentVix, 25.0m, 
                    "Reducing position sizes and evaluating protective puts");
            }

            // Step 4: Check volatility guard for trading blocks
            if (_volatilityGuard.IsAnyTradingBlocked())
            {
                var blockedSymbols = _volatilityGuard.GetBlockedSymbols();
                _logger.LogWarning("Trading blocked for {Count} symbols due to volatility spikes: {Symbols}",
                    blockedSymbols.Count(), string.Join(", ", blockedSymbols));
                await _discordService.SendMessageAsync($"⚠️ Trading blocked for {blockedSymbols.Count()} symbols due to volatility spikes");
                return;
            }

            // Step 5: Generate trading signals with VIX considerations
            var topN = GetAdjustedPositionCount(vixRegime);
            var signals = await _signalGenerator.RunAsync(topN);
            var signalList = signals.ToList();

            _logger.LogInformation("Generated {Count} trading signals for {Regime} regime",
                signalList.Count, vixRegime.RegimeName);

            // Step 6: Start Phase 6 monitoring for signal symbols
            await StartPhase6MonitoringAsync(signalList.Select(s => s.Symbol));

            // Step 7: Execute equity trades with Phase 6 filters
            var executedTrades = 0;
            foreach (var signal in signalList)
            {
                try
                {
                    // Phase 6 Filter 1: Check if trading is blocked for this symbol
                    if (_volatilityGuard.IsTradingBlocked(signal.Symbol))
                    {
                        _logger.LogInformation("Skipping {Symbol} - trading blocked due to volatility", signal.Symbol);
                        continue;
                    }

                    // Phase 6 Filter 2: Check VWAP condition for trending regime entry
                    var isPriceAboveVWAP = await _vwapMonitor.IsPriceAboveVWAPAsync(signal.Symbol, signal.Price);
                    if (!isPriceAboveVWAP)
                    {
                        _logger.LogInformation("Skipping {Symbol} - price {Price:F2} not above VWAP", signal.Symbol, signal.Price);
                        continue;
                    }

                    // Phase 6 Filter 3: Check microstructure conditions
                    var isFavorableForEntry = await _microstructureDetector.IsFavorableForEntryAsync(signal.Symbol, MicrostructureOrderSide.Buy);
                    if (!isFavorableForEntry)
                    {
                        _logger.LogInformation("Skipping {Symbol} - unfavorable microstructure conditions", signal.Symbol);
                        continue;
                    }

                    // Phase 6 Filter 4: Check index regime conditions
                    var indexRegime = await _indexRegimeService.GetCurrentRegimeAsync();
                    if (indexRegime == IndexMarketRegime.Panic || indexRegime == IndexMarketRegime.Volatile)
                    {
                        _logger.LogInformation("Skipping {Symbol} - unfavorable index regime: {Regime}", signal.Symbol, indexRegime);
                        continue;
                    }

                    // Phase 6 Filter 5: Check VIX data freshness and level
                    var vixResult = await _vixResolverService.GetCurrentVixAsync();
                    if (vixResult.ShouldHaltTrading)
                    {
                        _logger.LogWarning("Halting trading due to VIX data issues: {Error}", vixResult.ErrorMessage);
                        break; // Exit the entire trading loop
                    }
                    if (vixResult.VixValue > 30m) // High VIX threshold
                    {
                        _logger.LogInformation("Skipping {Symbol} - VIX too high: {VIX:F1}", signal.Symbol, vixResult.VixValue);
                        continue;
                    }

                    // Phase 6 Filter 6: Check market breadth conditions
                    var breadthSupportsTrading = await _breadthMonitorService.SupportsBullishSignalsAsync();
                    if (!breadthSupportsTrading)
                    {
                        _logger.LogInformation("Skipping {Symbol} - market breadth does not support bullish signals", signal.Symbol);
                        continue;
                    }

                    // Phase 6 Filter 7: Check real-time execution conditions
                    var executionAllowed = await _realTimeExecutionService.IsExecutionAllowedAsync(signal.Symbol);
                    if (!executionAllowed)
                    {
                        _logger.LogInformation("Skipping {Symbol} - real-time execution not allowed", signal.Symbol);
                        continue;
                    }

                    // Phase 6 Filter 8: Check for breakout confirmation (optional enhancement)
                    var isInBreakout = await _breakoutSignal.IsInBreakoutAsync(signal.Symbol);
                    var signalStrength = await _breakoutSignal.GetSignalStrengthAsync(signal.Symbol);

                    var baseQuantity = await _riskManager.CalculateQuantityAsync(signal);
                    if (baseQuantity > 0)
                    {
                        // Phase 6 Enhancement: Adjust position size based on breadth
                        var adjustedQuantity = await _breadthMonitorService.GetBreadthAdjustedPositionSizeAsync(baseQuantity);

                        // Phase 6 Enhancement: Get optimal execution strategy
                        var executionStrategy = await _realTimeExecutionService.EvaluateExecutionStrategyAsync(
                            signal.Symbol, SmaOrderSide.Buy, adjustedQuantity);

                        // Enhance signal with Phase 6 data
                        _logger.LogInformation("Executing trade for {Symbol}: VWAP✓, Microstructure✓, Breakout: {Breakout} (strength: {Strength:F1}%), " +
                            "IndexRegime: {IndexRegime}, VIX: {VIX:F1}, Breadth: ✓, Execution: {ExecutionType}",
                            signal.Symbol, isInBreakout ? "✓" : "○", signalStrength, indexRegime, vixResult.VixValue, executionStrategy.RecommendedOrderType);

                        // Use enhanced execution if available, otherwise fall back to standard execution
                        if (executionStrategy.ConfidenceScore > 0.5m)
                        {
                            // Create enhanced signal with execution strategy
                            var enhancedSignal = new TradingSignal(
                                signal.Symbol,
                                executionStrategy.LimitPrice ?? signal.Price,
                                signal.Atr,
                                signal.SixMonthReturn,
                                signal.Momentum,
                                signal.Timestamp
                            );

                            await _tradeExecutor.ExecuteTradeAsync(enhancedSignal, adjustedQuantity);
                        }
                        else
                        {
                            await _tradeExecutor.ExecuteTradeAsync(signal, adjustedQuantity);
                        }

                        executedTrades++;

                        // Small delay between trades to avoid overwhelming the API
                        if (_tradeDelayMs > 0)
                        {
                            await Task.Delay(_tradeDelayMs);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error executing trade for {Symbol}", signal.Symbol);
                }
            }

            _logger.LogInformation("Executed {ExecutedTrades} equity trades", executedTrades);

            // Step 6: Evaluate and execute options overlay strategies
            await ExecuteOptionsStrategies(vixRegime);

            // Step 7: Manage existing options positions
            await _optionsManager.ManageExistingOptionsAsync();
            await _optionsManager.ManageExpirationRiskAsync();

            // Step 8: Send portfolio snapshot
            await SendPortfolioSnapshot($"Completed cycle: {executedTrades} trades, {vixRegime.RegimeName} regime");

            // Step 9: 🚀 Adaptive Strategy Optimization
            await RunAdaptiveOptimizationAsync();

            _logger.LogInformation("Enhanced trading cycle completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in enhanced trading cycle");
            await _discordService.SendTradeNotificationAsync("ERROR", "SYSTEM", 0, 0, 0);
        }
    }

    private async Task ExecuteOptionsStrategies(VolatilityRegime vixRegime)
    {
        try
        {
            _logger.LogInformation("Evaluating options overlay strategies");

            var account = await _marketDataService.GetAccountAsync();
            var positions = await _marketDataService.GetPositionsAsync();
            var equityPositions = positions.Where(p => !IsOptionsSymbol(p.Symbol)).ToList();

            // Evaluate protective puts for portfolio protection
            if (vixRegime.IsVixSpike || vixRegime.IsHighVol)
            {
                var portfolioValue = account.Equity ?? 0m;
                var spyResult = await _optionsManager.EvaluateProtectivePutAsync("SPY", portfolioValue, 0);
                
                if (spyResult.ShouldExecute)
                {
                    await _discordService.SendOptionsNotificationAsync("Protective Put", "SPY", spyResult.Reason);
                    _logger.LogInformation("Protective put recommended for SPY: {Reason}", spyResult.Reason);
                }
            }

            // Evaluate covered calls for income generation
            foreach (var position in equityPositions.Where(p => p.Quantity >= 100))
            {
                try
                {
                    var currentPrice = await GetCurrentPrice(position.Symbol);
                    if (currentPrice > 0)
                    {
                        var ccResult = await _optionsManager.EvaluateCoveredCallAsync(
                            position.Symbol, position.Quantity, currentPrice);
                        
                        if (ccResult.ShouldExecute)
                        {
                            await _discordService.SendOptionsNotificationAsync("Covered Call", position.Symbol, ccResult.Reason);
                            _logger.LogInformation("Covered call opportunity for {Symbol}: {Reason}", 
                                position.Symbol, ccResult.Reason);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error evaluating covered call for {Symbol}", position.Symbol);
                }
            }

            // Evaluate delta-efficient exposure for capital efficiency
            if (!vixRegime.IsHighVol && account.Equity > 100_000m)
            {
                var deltaResult = await _optionsManager.EvaluateDeltaEfficientExposureAsync("SPY", 50_000m, 0);
                
                if (deltaResult.ShouldExecute)
                {
                    await _discordService.SendOptionsNotificationAsync("Delta Efficient", "SPY", deltaResult.Reason);
                    _logger.LogInformation("Delta-efficient exposure opportunity: {Reason}", deltaResult.Reason);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing options strategies");
        }
    }

    private async Task SendPortfolioSnapshot(string context)
    {
        try
        {
            var account = await _marketDataService.GetAccountAsync();
            var positions = await _marketDataService.GetPositionsAsync();
            
            var totalEquity = account.Equity ?? 0m;
            var dayPnl = account.DayTradeCount > 0 ? (account.Equity - account.LastEquity) ?? 0m : 0m;
            var totalPnl = positions.Sum(p => p.UnrealizedProfitLoss ?? 0m);
            var positionCount = positions.Count;

            await _discordService.SendPortfolioSnapshotAsync(totalEquity, dayPnl, totalPnl, positionCount);
            
            _logger.LogInformation("Portfolio snapshot - Context: {Context}, Equity: {Equity:C}, Day P&L: {DayPnl:C}, Positions: {Count}",
                context, totalEquity, dayPnl, positionCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending portfolio snapshot");
        }
    }

    private static int GetAdjustedPositionCount(VolatilityRegime vixRegime)
    {
        // Adjust position count based on VIX level for more granular control
        var baseCount = 10;
        var vixLevel = vixRegime.CurrentVix;

        // VIX-based position adjustment with smooth scaling
        return vixLevel switch
        {
            >= 50.0m => 3,  // Extreme VIX -> Minimal positions
            >= 40.0m => 4,  // Very high VIX -> Very few positions
            >= 35.0m => 5,  // High VIX -> Fewer positions
            >= 30.0m => 6,  // Elevated VIX -> Reduced positions
            >= 25.0m => 7,  // Medium VIX -> Moderately reduced positions
            >= 20.0m => baseCount, // Normal VIX -> Standard positions
            >= 15.0m => baseCount, // Low VIX -> Standard positions
            _ => Math.Min(12, baseCount + 2) // Very low VIX -> Slightly more positions
        };
    }

    private async Task<decimal> GetCurrentPrice(string symbol)
    {
        try
        {
            var startDate = DateTime.UtcNow.AddDays(-1);
            var endDate = DateTime.UtcNow;
            var bars = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
            return bars.Items.LastOrDefault()?.Close ?? 0m;
        }
        catch
        {
            return 0m;
        }
    }

    /// <summary>
    /// 🚀 Runs the adaptive strategy optimization system for continuous learning and improvement
    /// </summary>
    private async Task RunAdaptiveOptimizationAsync()
    {
        try
        {
            _logger.LogInformation("🤖 Running adaptive strategy optimization");

            // Check if optimization should be triggered
            if (await _optimizationOrchestrator.ShouldTriggerOptimizationAsync())
            {
                _logger.LogInformation("🚀 Optimization triggered - running full analysis pipeline");

                var result = await _optimizationOrchestrator.RunOptimizationPipelineAsync();

                if (result.Success)
                {
                    _logger.LogInformation("✅ Optimization completed successfully");

                    // Send optimization results to Discord
                    await _discordService.SendTradeNotificationAsync(
                        "OPTIMIZATION", "SUCCESS", 1, 0,
                        result.Metrics?.ExpectedImprovement * 100 ?? 0);

                    // Log key metrics if available
                    if (result.Metrics != null)
                    {
                        _logger.LogInformation("📊 Optimization Metrics - OOS Sharpe: {Sharpe:F2}, Expected Return: {Return:P2}, Max DD: {DD:P2}, Parameters Adjusted: {Adjusted}",
                            result.Metrics.OutOfSampleSharpe,
                            result.Metrics.ExpectedReturn,
                            result.Metrics.MaxDrawdown,
                            result.Metrics.ParametersAdjusted);
                    }
                }
                else
                {
                    _logger.LogWarning("⚠️ Optimization failed: {Error}", result.Summary);

                    await _discordService.SendTradeNotificationAsync(
                        "ERROR", "OPTIMIZATION", 0, 0, 0);
                }
            }
            else
            {
                _logger.LogDebug("📊 Optimization not triggered - conditions not met");

                // Get current optimization status for logging
                var status = await _optimizationOrchestrator.GetOptimizationStatusAsync();
                _logger.LogDebug("Optimization Status - Last: {LastOptimization}, Trades: {Trades}, Model Accuracy: {Accuracy:P1}",
                    status.LastOptimization,
                    status.TotalTrades,
                    status.ModelAccuracy);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error in adaptive optimization system");

            await _discordService.SendTradeNotificationAsync(
                "ERROR", "OPTIMIZATION", 0, 0, 0);
        }
    }

    /// <summary>
    /// Start Phase 6 monitoring services for the given symbols
    /// </summary>
    private async Task StartPhase6MonitoringAsync(IEnumerable<string> symbols)
    {
        try
        {
            var symbolList = symbols.ToList();
            if (!symbolList.Any())
                return;

            _logger.LogInformation("Starting Phase 6 monitoring for {Count} symbols", symbolList.Count);

            // Start existing Phase 6 services
            await _vwapMonitor.AddSymbolsAsync(symbolList);
            await _volatilityGuard.StartMonitoringAsync(symbolList);
            await _breakoutSignal.AddSymbolsAsync(symbolList);
            await _microstructureDetector.AddSymbolsAsync(symbolList);

            // Start new Phase 6: Real-Time Intelligence & Signal Architecture services
            await _indexRegimeService.StartMonitoringAsync();
            await _breadthMonitorService.StartMonitoringAsync();
            await _realTimeExecutionService.StartMonitoringAsync(symbolList);

            _logger.LogDebug("Phase 6 monitoring started for symbols: {Symbols}", string.Join(", ", symbolList));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting Phase 6 monitoring");
        }
    }

    /// <summary>
    /// Determines if a symbol represents an options contract rather than an equity
    /// Options symbols typically follow the format: SYMBOL + YYMMDD + C/P + Strike
    /// Examples: AAPL240119C00150000, SPY240315P00400000
    /// </summary>
    private static bool IsOptionsSymbol(string symbol)
    {
        if (string.IsNullOrEmpty(symbol) || symbol.Length < 15)
            return false;

        // Options symbols are typically 21 characters long for standard format
        // But can vary, so we check for the pattern: ends with C/P followed by 8 digits
        var pattern = @"[CP]\d{8}$";
        return System.Text.RegularExpressions.Regex.IsMatch(symbol, pattern);
    }
}
