using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Services;
using System;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Manual;

/// <summary>
/// Manual test to verify VIX web scraping works with real web requests
/// </summary>
public class TestVixWebScrapingManual
{
    private readonly ITestOutputHelper _output;

    public TestVixWebScrapingManual(ITestOutputHelper output)
    {
        _output = output;
    }

    [Fact(Skip = "Manual test - requires internet connection")]
    public async Task TestRealVixWebScraping()
    {
        // Arrange
        var logger = new Mock<ILogger<VixFallbackService>>();
        var alpacaFactory = new Mock<IAlpacaClientFactory>();
        var polygonFactory = new Mock<IPolygonClientFactory>();
        var httpClientFactory = new Mock<IHttpClientFactory>();

        var httpClient = new HttpClient();
        httpClient.DefaultRequestHeaders.Add("User-Agent",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");

        httpClientFactory.Setup(x => x.CreateClient("vix-fallback"))
            .Returns(httpClient);

        var service = new VixFallbackService(
            logger.Object,
            alpacaFactory.Object,
            polygonFactory.Object,
            httpClientFactory.Object);

        // Act
        var result = await service.GetVixFromWebAsync();

        // Assert and Output
        _output.WriteLine($"VIX Web Scraping Result: {result}");

        if (result.HasValue)
        {
            _output.WriteLine($"✅ SUCCESS: Extracted VIX value: {result.Value:F2}");
            
            // Validate it's reasonable (between 8 and 80)
            if (result.Value >= 8 && result.Value <= 80)
            {
                _output.WriteLine($"✅ VALIDATION: VIX value {result.Value:F2} is within reasonable bounds");
            }
            else
            {
                _output.WriteLine($"❌ VALIDATION: VIX value {result.Value:F2} is outside reasonable bounds (8-80)");
            }
        }
        else
        {
            _output.WriteLine("❌ FAILED: Could not extract VIX value from web scraping");
        }

        // Cleanup
        service.Dispose();
    }

    [Fact]
    public void TestVixRegexPatterns()
    {
        // Test with sample Yahoo Finance content
        var sampleContent = @"
            CBOE Volatility Index (^VIX)
            Follow
            20.86
            -1.31
            (-5.91%)
            As of 2:40:16 PM CDT. Market Open.
        ";

        var patterns = new[]
        {
            @"CBOE Volatility Index \(\^VIX\)[^0-9]*([0-9]+\.?[0-9]*)",
            @"(\d+\.\d+)\s*-\d+\.\d+\s*\(-\d+\.\d+%\)",  // Pattern: "20.86 -1.31 (-5.91%)"
            @">([0-9]+\.?[0-9]*)<[^>]*>-[0-9]+\.?[0-9]*<[^>]*>\(-[0-9]+\.?[0-9]*%\)",
            @"Follow\s*([0-9]+\.?[0-9]*)",  // "Follow 20.86"
            @"\^VIX[^0-9]*([0-9]+\.?[0-9]*)",
            @"VIX[^0-9]*([0-9]+\.?[0-9]*)"
        };

        _output.WriteLine("Testing VIX regex patterns:");
        _output.WriteLine($"Sample content: {sampleContent}");

        for (int i = 0; i < patterns.Length; i++)
        {
            var pattern = patterns[i];
            try
            {
                var match = System.Text.RegularExpressions.Regex.Match(sampleContent, pattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                if (match.Success && decimal.TryParse(match.Groups[1].Value, out var value) && value > 0)
                {
                    _output.WriteLine($"✅ Pattern {i + 1} matched: '{pattern}' -> {value:F2}");
                    
                    // If we found 20.86, that's what we expect
                    if (value == 20.86m)
                    {
                        _output.WriteLine($"🎯 PERFECT: Found expected VIX value 20.86");
                        return; // Success!
                    }
                }
                else if (match.Success)
                {
                    _output.WriteLine($"⚠️ Pattern {i + 1} matched but failed parsing: '{pattern}' -> '{match.Groups[1].Value}'");
                }
                else
                {
                    _output.WriteLine($"❌ Pattern {i + 1} no match: '{pattern}'");
                }
            }
            catch (Exception ex)
            {
                _output.WriteLine($"❌ Pattern {i + 1} failed: '{pattern}' -> {ex.Message}");
            }
        }
    }
}
