using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Microstructure pattern detector for precise entry timing in volatile environments
/// Looks for uptick + bid support or down-tick + spread widening patterns
/// Used to time entries in volatile environments with optimal microstructure conditions
/// </summary>
public interface IMicrostructurePatternDetector : IDisposable
{
    // === Events ===
    
    /// <summary>
    /// Fired when an uptick + bid support pattern is detected
    /// </summary>
    event EventHandler<UptickBidSupportEventArgs>? UptickBidSupportDetected;
    
    /// <summary>
    /// Fired when a down-tick + spread widening pattern is detected
    /// </summary>
    event EventHandler<DowntickSpreadWideningEventArgs>? DowntickSpreadWideningDetected;
    
    /// <summary>
    /// Fired when optimal entry conditions are met
    /// </summary>
    event EventHandler<OptimalEntryEventArgs>? OptimalEntryDetected;
    
    /// <summary>
    /// Fired when microstructure conditions deteriorate
    /// </summary>
    event EventHandler<MicrostructureDeteriorationEventArgs>? MicrostructureDeterioration;
    
    // === Core Methods ===
    
    /// <summary>
    /// Start pattern detection for specified symbols
    /// </summary>
    Task StartDetectionAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stop pattern detection
    /// </summary>
    Task StopDetectionAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Add symbols to pattern detection
    /// </summary>
    Task AddSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Remove symbols from pattern detection
    /// </summary>
    Task RemoveSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    // === Pattern Analysis ===
    
    /// <summary>
    /// Get current microstructure analysis for a symbol
    /// </summary>
    Task<MicrostructureAnalysis?> GetMicrostructureAnalysisAsync(string symbol);
    
    /// <summary>
    /// Check if current microstructure is favorable for entry
    /// </summary>
    Task<bool> IsFavorableForEntryAsync(string symbol, MicrostructureOrderSide orderSide);
    
    /// <summary>
    /// Get pattern strength score (0-100)
    /// </summary>
    Task<decimal> GetPatternStrengthAsync(string symbol);
    
    /// <summary>
    /// Get recent pattern history for a symbol
    /// </summary>
    Task<IEnumerable<MicrostructurePattern>> GetPatternHistoryAsync(string symbol, int minutes = 30);
    
    // === Configuration ===
    
    /// <summary>
    /// Update pattern detection configuration
    /// </summary>
    Task UpdateConfigurationAsync(MicrostructurePatternConfig config);
    
    /// <summary>
    /// Get detection status
    /// </summary>
    MicrostructureDetectionStatus GetStatus();
    
    /// <summary>
    /// Get list of monitored symbols
    /// </summary>
    IEnumerable<string> GetMonitoredSymbols();
}

/// <summary>
/// Microstructure pattern detection configuration
/// </summary>
public record MicrostructurePatternConfig(
    int TickSequenceLength = 10,
    decimal MinUptickThreshold = 0.01m,
    decimal MinBidSupportRatio = 0.95m,
    decimal MaxSpreadWidening = 2.0m,
    decimal MinLiquidityThreshold = 1000m,
    bool RequireVolumeConfirmation = true,
    int MinConsecutiveTicks = 3,
    TimeSpan PatternValidityPeriod = default,
    decimal VolatilityAdjustmentFactor = 0.5m,
    TimeSpan CacheExpiry = default
)
{
    public TimeSpan PatternValidityPeriod { get; init; } = PatternValidityPeriod == default ? TimeSpan.FromMinutes(2) : PatternValidityPeriod;
    public TimeSpan CacheExpiry { get; init; } = CacheExpiry == default ? TimeSpan.FromMinutes(1) : CacheExpiry;
}

/// <summary>
/// Microstructure analysis result
/// </summary>
public record MicrostructureAnalysis(
    string Symbol,
    decimal CurrentPrice,
    decimal BidPrice,
    decimal AskPrice,
    decimal Spread,
    decimal SpreadPercent,
    MicrostructureCondition Condition,
    PatternType ActivePattern,
    decimal PatternStrength,
    int ConsecutiveUpticks,
    int ConsecutiveDownticks,
    decimal BidSupportRatio,
    decimal AskPressureRatio,
    long BidSize,
    long AskSize,
    decimal LiquidityScore,
    DateTime LastUpdate,
    bool IsFavorableForLong,
    bool IsFavorableForShort
);

/// <summary>
/// Microstructure pattern record
/// </summary>
public record MicrostructurePattern(
    string Symbol,
    PatternType Type,
    decimal TriggerPrice,
    decimal PatternStrength,
    int TickSequenceLength,
    DateTime StartTime,
    DateTime EndTime,
    TimeSpan Duration,
    bool WasSuccessful
);

/// <summary>
/// Microstructure condition classification
/// </summary>
public enum MicrostructureCondition
{
    Unknown,
    Favorable,
    Neutral,
    Unfavorable,
    Deteriorating
}

/// <summary>
/// Pattern type classification
/// </summary>
public enum PatternType
{
    None,
    UptickBidSupport,
    DowntickSpreadWidening,
    BidStackBuilding,
    AskStackBuilding,
    LiquidityDrying,
    SpreadCompression
}

/// <summary>
/// Detection status
/// </summary>
public enum MicrostructureDetectionStatus
{
    Stopped,
    Starting,
    Active,
    Error
}

/// <summary>
/// Order side for entry analysis
/// </summary>
public enum MicrostructureOrderSide
{
    Buy,
    Sell
}

/// <summary>
/// Event args for uptick + bid support pattern
/// </summary>
public class UptickBidSupportEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required decimal TriggerPrice { get; init; }
    public required decimal BidPrice { get; init; }
    public required decimal BidSupportRatio { get; init; }
    public required int ConsecutiveUpticks { get; init; }
    public required decimal PatternStrength { get; init; }
    public required DateTime Timestamp { get; init; }
    public required MicrostructureAnalysis Analysis { get; init; }
}

/// <summary>
/// Event args for down-tick + spread widening pattern
/// </summary>
public class DowntickSpreadWideningEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required decimal TriggerPrice { get; init; }
    public required decimal SpreadWidening { get; init; }
    public required int ConsecutiveDownticks { get; init; }
    public required decimal PatternStrength { get; init; }
    public required DateTime Timestamp { get; init; }
    public required MicrostructureAnalysis Analysis { get; init; }
}

/// <summary>
/// Event args for optimal entry conditions
/// </summary>
public class OptimalEntryEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required MicrostructureOrderSide RecommendedSide { get; init; }
    public required decimal OptimalPrice { get; init; }
    public required decimal PatternStrength { get; init; }
    public required PatternType PatternType { get; init; }
    public required DateTime Timestamp { get; init; }
    public required MicrostructureAnalysis Analysis { get; init; }
}

/// <summary>
/// Event args for microstructure deterioration
/// </summary>
public class MicrostructureDeteriorationEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required string Reason { get; init; }
    public required decimal CurrentPrice { get; init; }
    public required DateTime Timestamp { get; init; }
}
