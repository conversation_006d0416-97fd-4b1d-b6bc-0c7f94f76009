using Alpaca.Markets;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Tests.TestHelpers;

/// <summary>
/// Interface for index bar data used in testing.
/// Provides a mockable interface for IndexBar struct.
/// </summary>
public interface IIndexBar
{
    DateTime Timestamp { get; }
    decimal Open { get; }
    decimal High { get; }
    decimal Low { get; }
    decimal Close { get; }
    long Volume { get; }
}

/// <summary>
/// Wrapper that implements IIndexBar interface for IndexBar compatibility in tests.
/// </summary>
public class IndexBarTestWrapper : IIndexBar
{
    private readonly IndexBar _indexBar;

    public IndexBarTestWrapper(IndexBar indexBar)
    {
        _indexBar = indexBar;
    }

    public DateTime Timestamp => _indexBar.TimeUtc;
    public decimal Open => _indexBar.Open;
    public decimal High => _indexBar.High;
    public decimal Low => _indexBar.Low;
    public decimal Close => _indexBar.Close;
    public long Volume => _indexBar.Volume;
}
