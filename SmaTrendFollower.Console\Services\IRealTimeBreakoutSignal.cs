using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time breakout signal service for precise entry timing
/// Combines quote + trade data for precise breakout detection
/// Entry only if last trade > prior high and bid >= signal price
/// </summary>
public interface IRealTimeBreakoutSignal : IDisposable
{
    // === Events ===
    
    /// <summary>
    /// Fired when a breakout signal is detected
    /// </summary>
    event EventHandler<BreakoutSignalEventArgs>? BreakoutDetected;
    
    /// <summary>
    /// Fired when a breakdown signal is detected
    /// </summary>
    event EventHandler<BreakdownSignalEventArgs>? BreakdownDetected;
    
    /// <summary>
    /// Fired when breakout conditions are invalidated
    /// </summary>
    event EventHandler<BreakoutInvalidatedEventArgs>? BreakoutInvalidated;
    
    // === Core Methods ===
    
    /// <summary>
    /// Start monitoring breakout signals for specified symbols
    /// </summary>
    Task StartMonitoringAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stop monitoring breakout signals
    /// </summary>
    Task StopMonitoringAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Add symbols to breakout monitoring
    /// </summary>
    Task AddSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Remove symbols from breakout monitoring
    /// </summary>
    Task RemoveSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    // === Signal Analysis ===
    
    /// <summary>
    /// Get current breakout status for a symbol
    /// </summary>
    Task<BreakoutStatus?> GetBreakoutStatusAsync(string symbol);
    
    /// <summary>
    /// Check if symbol is currently in breakout condition
    /// </summary>
    Task<bool> IsInBreakoutAsync(string symbol);
    
    /// <summary>
    /// Get breakout signal strength (0-100)
    /// </summary>
    Task<decimal> GetSignalStrengthAsync(string symbol);
    
    /// <summary>
    /// Get recent breakout history for a symbol
    /// </summary>
    Task<IEnumerable<BreakoutEvent>> GetBreakoutHistoryAsync(string symbol, int hours = 24);
    
    // === Configuration ===
    
    /// <summary>
    /// Update breakout signal configuration
    /// </summary>
    Task UpdateConfigurationAsync(BreakoutSignalConfig config);
    
    /// <summary>
    /// Get monitoring status
    /// </summary>
    BreakoutMonitorStatus GetStatus();
    
    /// <summary>
    /// Get list of monitored symbols
    /// </summary>
    IEnumerable<string> GetMonitoredSymbols();
}

/// <summary>
/// Breakout signal configuration
/// </summary>
public record BreakoutSignalConfig(
    int LookbackPeriodMinutes = 60,
    decimal MinBreakoutPercent = 0.5m,
    decimal BidSupportThreshold = 0.8m,
    int MinVolumeMultiplier = 2,
    bool RequireVolumeConfirmation = true,
    bool RequireBidSupport = true,
    TimeSpan SignalValidityPeriod = default,
    decimal MinPriceThreshold = 5.0m,
    TimeSpan CacheExpiry = default
)
{
    public TimeSpan SignalValidityPeriod { get; init; } = SignalValidityPeriod == default ? TimeSpan.FromMinutes(5) : SignalValidityPeriod;
    public TimeSpan CacheExpiry { get; init; } = CacheExpiry == default ? TimeSpan.FromMinutes(2) : CacheExpiry;
}

/// <summary>
/// Breakout status for a symbol
/// </summary>
public record BreakoutStatus(
    string Symbol,
    decimal CurrentPrice,
    decimal PriorHigh,
    decimal PriorLow,
    decimal BidPrice,
    decimal AskPrice,
    decimal BreakoutLevel,
    decimal BreakdownLevel,
    BreakoutCondition Condition,
    decimal SignalStrength,
    long CurrentVolume,
    long AverageVolume,
    DateTime LastUpdate,
    bool IsValid
);

/// <summary>
/// Breakout event record
/// </summary>
public record BreakoutEvent(
    string Symbol,
    BreakoutType Type,
    decimal TriggerPrice,
    decimal BreakoutLevel,
    decimal SignalStrength,
    long Volume,
    DateTime Timestamp,
    TimeSpan Duration
);

/// <summary>
/// Breakout condition classification
/// </summary>
public enum BreakoutCondition
{
    None,
    Approaching,
    Triggered,
    Confirmed,
    Failed
}

/// <summary>
/// Breakout type
/// </summary>
public enum BreakoutType
{
    Upward,
    Downward
}

/// <summary>
/// Breakout monitoring status
/// </summary>
public enum BreakoutMonitorStatus
{
    Stopped,
    Starting,
    Active,
    Error
}

/// <summary>
/// Event args for breakout signal
/// </summary>
public class BreakoutSignalEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required decimal TriggerPrice { get; init; }
    public required decimal BreakoutLevel { get; init; }
    public required decimal BidPrice { get; init; }
    public required decimal SignalStrength { get; init; }
    public required long Volume { get; init; }
    public required DateTime Timestamp { get; init; }
    public required BreakoutStatus Status { get; init; }
}

/// <summary>
/// Event args for breakdown signal
/// </summary>
public class BreakdownSignalEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required decimal TriggerPrice { get; init; }
    public required decimal BreakdownLevel { get; init; }
    public required decimal AskPrice { get; init; }
    public required decimal SignalStrength { get; init; }
    public required long Volume { get; init; }
    public required DateTime Timestamp { get; init; }
    public required BreakoutStatus Status { get; init; }
}

/// <summary>
/// Event args for breakout invalidation
/// </summary>
public class BreakoutInvalidatedEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required string Reason { get; init; }
    public required decimal CurrentPrice { get; init; }
    public required DateTime Timestamp { get; init; }
}
