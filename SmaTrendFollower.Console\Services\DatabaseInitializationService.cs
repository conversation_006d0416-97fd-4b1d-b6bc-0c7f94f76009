using Microsoft.Extensions.Logging;
using SmaTrendFollower.Data;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for initializing all database contexts and ensuring proper schema creation
/// </summary>
public sealed class DatabaseInitializationService : IDatabaseInitializationService
{
    private readonly IndexCacheDbContext _indexCacheContext;
    private readonly StockBarCacheDbContext _stockCacheContext;
    private readonly ILogger<DatabaseInitializationService> _logger;

    public DatabaseInitializationService(
        IndexCacheDbContext indexCacheContext,
        StockBarCacheDbContext stockCacheContext,
        ILogger<DatabaseInitializationService> logger)
    {
        _indexCacheContext = indexCacheContext;
        _stockCacheContext = stockCacheContext;
        _logger = logger;
    }

    public async Task InitializeAllDatabasesAsync()
    {
        _logger.LogInformation("Initializing all database contexts...");

        try
        {
            await InitializeIndexCacheAsync();
            await InitializeStockCacheAsync();

            _logger.LogInformation("All databases initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize databases");
            throw;
        }
    }

    public async Task InitializeIndexCacheAsync()
    {
        try
        {
            _logger.LogDebug("Initializing index cache database...");

            // Use the context's built-in method
            await _indexCacheContext.EnsureDatabaseCreatedAsync();

            _logger.LogDebug("Index cache database initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize index cache database");
            throw;
        }
    }

    public async Task InitializeStockCacheAsync()
    {
        try
        {
            _logger.LogDebug("Initializing stock cache database...");

            // Use the context's built-in method that handles optimizations
            await _stockCacheContext.EnsureDatabaseCreatedAsync();

            _logger.LogDebug("Stock cache database initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize stock cache database");
            throw;
        }
    }

    public async Task<bool> AreAllDatabasesInitializedAsync()
    {
        try
        {
            var indexCanConnect = await _indexCacheContext.Database.CanConnectAsync();
            var stockCanConnect = await _stockCacheContext.Database.CanConnectAsync();

            return indexCanConnect && stockCanConnect;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error checking database connectivity");
            return false;
        }
    }
}
