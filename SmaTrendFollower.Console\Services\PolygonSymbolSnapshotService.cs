using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Diagnostics;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for Polygon symbol snapshot and filtering service
/// </summary>
public interface IPolygonSymbolSnapshotService
{
    /// <summary>
    /// Get real-time snapshots for all symbols
    /// </summary>
    Task<IEnumerable<PolygonSnapshotTicker>> GetAllSnapshotsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get snapshots for specific symbols
    /// </summary>
    Task<IEnumerable<PolygonSnapshotTicker>> GetSnapshotsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);

    /// <summary>
    /// Filter and rank symbols based on real-time criteria
    /// </summary>
    Task<IEnumerable<UniverseCandidate>> FilterAndRankSymbolsAsync(
        IEnumerable<PolygonSymbolInfo> symbols,
        CandidateFilterCriteria criteria,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get historical volatility for symbols
    /// </summary>
    Task<Dictionary<string, decimal>> GetHistoricalVolatilityAsync(
        IEnumerable<string> symbols,
        int periodDays = 20,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get average volume for symbols
    /// </summary>
    Task<Dictionary<string, long>> GetAverageVolumeAsync(
        IEnumerable<string> symbols,
        int periodDays = 20,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service for fetching real-time symbol snapshots and filtering candidates
/// Uses Polygon /v2/snapshot/locale/us/markets/stocks/tickers endpoint
/// </summary>
public sealed class PolygonSymbolSnapshotService : IPolygonSymbolSnapshotService
{
    private readonly IPolygonClientFactory _polygonFactory;
    private readonly ILogger<PolygonSymbolSnapshotService> _logger;
    private readonly SnapshotServiceConfig _config;

    public PolygonSymbolSnapshotService(
        IPolygonClientFactory polygonFactory,
        IConfiguration configuration,
        ILogger<PolygonSymbolSnapshotService> logger)
    {
        _polygonFactory = polygonFactory ?? throw new ArgumentNullException(nameof(polygonFactory));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // Load configuration
        _config = SnapshotServiceConfig.Default;
        configuration.GetSection("PolygonSnapshot").Bind(_config);

        _logger.LogInformation("PolygonSymbolSnapshotService initialized with config: BatchSize={BatchSize}, MaxConcurrency={MaxConcurrency}",
            _config.BatchSize, _config.MaxConcurrency);
    }

    public async Task<IEnumerable<PolygonSnapshotTicker>> GetAllSnapshotsAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        _logger.LogInformation("Fetching all symbol snapshots from Polygon API");

        try
        {
            var httpClient = _polygonFactory.CreateClient();
            var rateLimitHelper = _polygonFactory.GetRateLimitHelper();

            var response = await rateLimitHelper.ExecuteAsync(async () =>
            {
                var url = "v2/snapshot/locale/us/markets/stocks/tickers";
                var urlWithApiKey = _polygonFactory.AddApiKeyToUrl(url);
                
                _logger.LogDebug("Fetching snapshots from: {Url}", url);
                return await httpClient.GetAsync(urlWithApiKey, cancellationToken);
            }, "AllSnapshotsFetch");

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Polygon API returned {StatusCode} for snapshots fetch", response.StatusCode);
                return Enumerable.Empty<PolygonSnapshotTicker>();
            }

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var polygonResponse = JsonSerializer.Deserialize<PolygonSnapshotResponse>(content);

            if (polygonResponse?.Results == null)
            {
                _logger.LogWarning("No results in Polygon snapshot response");
                return Enumerable.Empty<PolygonSnapshotTicker>();
            }

            stopwatch.Stop();
            _logger.LogInformation("Fetched {SnapshotCount} snapshots in {ElapsedMs}ms",
                polygonResponse.Results.Count, stopwatch.ElapsedMilliseconds);

            return polygonResponse.Results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching snapshots from Polygon API");
            throw;
        }
    }

    public async Task<IEnumerable<PolygonSnapshotTicker>> GetSnapshotsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        var symbolList = symbols.ToList();
        if (!symbolList.Any())
        {
            return Enumerable.Empty<PolygonSnapshotTicker>();
        }

        var stopwatch = Stopwatch.StartNew();
        _logger.LogInformation("Fetching snapshots for {SymbolCount} symbols", symbolList.Count);

        try
        {
            // For specific symbols, we need to make individual calls or use the grouped snapshot endpoint
            // Polygon doesn't support filtering the all-snapshots endpoint by specific symbols
            var allSnapshots = await GetAllSnapshotsAsync(cancellationToken);
            var filteredSnapshots = allSnapshots
                .Where(s => symbolList.Contains(s.Ticker, StringComparer.OrdinalIgnoreCase))
                .ToList();

            stopwatch.Stop();
            _logger.LogInformation("Filtered {FilteredCount}/{TotalCount} snapshots for requested symbols in {ElapsedMs}ms",
                filteredSnapshots.Count, symbolList.Count, stopwatch.ElapsedMilliseconds);

            return filteredSnapshots;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching snapshots for specific symbols");
            throw;
        }
    }

    public async Task<IEnumerable<UniverseCandidate>> FilterAndRankSymbolsAsync(
        IEnumerable<PolygonSymbolInfo> symbols,
        CandidateFilterCriteria criteria,
        CancellationToken cancellationToken = default)
    {
        var symbolList = symbols.ToList();
        var stopwatch = Stopwatch.StartNew();
        var candidates = new List<UniverseCandidate>();

        _logger.LogInformation("Filtering and ranking {SymbolCount} symbols with criteria: MinPrice={MinPrice}, MinVolume={MinVolume}, MinVolatility={MinVolatility}%",
            symbolList.Count, criteria.MinPrice, criteria.MinAverageVolume, criteria.MinVolatilityPercent);

        try
        {
            // Get real-time snapshots
            var snapshots = await GetAllSnapshotsAsync(cancellationToken);
            var snapshotDict = snapshots.ToDictionary(s => s.Ticker, StringComparer.OrdinalIgnoreCase);

            // Get historical data for volatility and volume calculations
            var symbolTickers = symbolList.Select(s => s.Ticker).ToList();
            var volatilityTask = GetHistoricalVolatilityAsync(symbolTickers, criteria.AnalysisPeriodDays, cancellationToken);
            var volumeTask = GetAverageVolumeAsync(symbolTickers, criteria.AnalysisPeriodDays, cancellationToken);

            await Task.WhenAll(volatilityTask, volumeTask);

            var volatilityData = await volatilityTask;
            var volumeData = await volumeTask;

            // Process symbols in parallel batches
            var semaphore = new SemaphoreSlim(_config.MaxConcurrency, _config.MaxConcurrency);
            var batchTasks = new List<Task<List<UniverseCandidate>>>();

            for (int i = 0; i < symbolList.Count; i += _config.BatchSize)
            {
                var batch = symbolList.Skip(i).Take(_config.BatchSize);
                var batchTask = ProcessSymbolBatch(batch, snapshotDict, volatilityData, volumeData, criteria, semaphore, cancellationToken);
                batchTasks.Add(batchTask);
            }

            var batchResults = await Task.WhenAll(batchTasks);
            candidates = batchResults.SelectMany(batch => batch).ToList();

            // Sort by ranking score and take top candidates
            candidates = candidates
                .OrderByDescending(c => c.RankingScore)
                .Take(criteria.MaxCandidates)
                .ToList();

            stopwatch.Stop();
            _logger.LogInformation("Filtered and ranked {CandidateCount}/{SymbolCount} symbols in {ElapsedMs}ms",
                candidates.Count, symbolList.Count, stopwatch.ElapsedMilliseconds);

            return candidates;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error filtering and ranking symbols");
            throw;
        }
    }

    public async Task<Dictionary<string, decimal>> GetHistoricalVolatilityAsync(
        IEnumerable<string> symbols,
        int periodDays = 20,
        CancellationToken cancellationToken = default)
    {
        var symbolList = symbols.ToList();
        var volatilityData = new Dictionary<string, decimal>(StringComparer.OrdinalIgnoreCase);

        _logger.LogDebug("Calculating historical volatility for {SymbolCount} symbols over {PeriodDays} days",
            symbolList.Count, periodDays);

        try
        {
            var httpClient = _polygonFactory.CreateClient();
            var rateLimitHelper = _polygonFactory.GetRateLimitHelper();
            var endDate = DateTime.UtcNow.Date;
            var startDate = endDate.AddDays(-periodDays * 2); // Get extra data to ensure we have enough

            var semaphore = new SemaphoreSlim(_config.MaxConcurrency, _config.MaxConcurrency);
            var tasks = symbolList.Select(async symbol =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    var volatility = await CalculateSymbolVolatilityAsync(symbol, startDate, endDate, periodDays, httpClient, rateLimitHelper, cancellationToken);
                    if (volatility.HasValue)
                    {
                        lock (volatilityData)
                        {
                            volatilityData[symbol] = volatility.Value;
                        }
                    }
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(tasks);

            _logger.LogDebug("Calculated volatility for {CalculatedCount}/{RequestedCount} symbols",
                volatilityData.Count, symbolList.Count);

            return volatilityData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating historical volatility");
            return volatilityData;
        }
    }

    public async Task<Dictionary<string, long>> GetAverageVolumeAsync(
        IEnumerable<string> symbols,
        int periodDays = 20,
        CancellationToken cancellationToken = default)
    {
        var symbolList = symbols.ToList();
        var volumeData = new Dictionary<string, long>(StringComparer.OrdinalIgnoreCase);

        _logger.LogDebug("Calculating average volume for {SymbolCount} symbols over {PeriodDays} days",
            symbolList.Count, periodDays);

        try
        {
            var httpClient = _polygonFactory.CreateClient();
            var rateLimitHelper = _polygonFactory.GetRateLimitHelper();
            var endDate = DateTime.UtcNow.Date;
            var startDate = endDate.AddDays(-periodDays * 2); // Get extra data to ensure we have enough

            var semaphore = new SemaphoreSlim(_config.MaxConcurrency, _config.MaxConcurrency);
            var tasks = symbolList.Select(async symbol =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    var avgVolume = await CalculateSymbolAverageVolumeAsync(symbol, startDate, endDate, periodDays, httpClient, rateLimitHelper, cancellationToken);
                    if (avgVolume.HasValue)
                    {
                        lock (volumeData)
                        {
                            volumeData[symbol] = avgVolume.Value;
                        }
                    }
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(tasks);

            _logger.LogDebug("Calculated average volume for {CalculatedCount}/{RequestedCount} symbols",
                volumeData.Count, symbolList.Count);

            return volumeData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating average volume");
            return volumeData;
        }
    }

    private async Task<List<UniverseCandidate>> ProcessSymbolBatch(
        IEnumerable<PolygonSymbolInfo> batch,
        Dictionary<string, PolygonSnapshotTicker> snapshotDict,
        Dictionary<string, decimal> volatilityData,
        Dictionary<string, long> volumeData,
        CandidateFilterCriteria criteria,
        SemaphoreSlim semaphore,
        CancellationToken cancellationToken)
    {
        await semaphore.WaitAsync(cancellationToken);
        try
        {
            var candidates = new List<UniverseCandidate>();

            foreach (var symbol in batch)
            {
                try
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    // Get snapshot data
                    if (!snapshotDict.TryGetValue(symbol.Ticker, out var snapshot))
                    {
                        continue; // No snapshot data available
                    }

                    // Extract current price
                    var currentPrice = snapshot.LastTrade?.Price ?? snapshot.PrevDay?.Close;
                    if (!currentPrice.HasValue || currentPrice.Value < criteria.MinPrice)
                    {
                        continue; // Price filter failed
                    }

                    // Check volume filter
                    if (!volumeData.TryGetValue(symbol.Ticker, out var avgVolume) || avgVolume < criteria.MinAverageVolume)
                    {
                        continue; // Volume filter failed
                    }

                    // Check volatility filter
                    if (!volatilityData.TryGetValue(symbol.Ticker, out var volatility) || volatility < criteria.MinVolatilityPercent)
                    {
                        continue; // Volatility filter failed
                    }

                    // Calculate ranking score
                    var rankingScore = CalculateRankingScore(currentPrice.Value, avgVolume, volatility, snapshot);

                    var candidate = new UniverseCandidate
                    {
                        Symbol = symbol.Ticker,
                        Price = currentPrice.Value,
                        AverageVolume = avgVolume,
                        VolatilityPercent = volatility,
                        RankingScore = rankingScore,
                        LastUpdated = DateTime.UtcNow,
                        Metadata = new Dictionary<string, object>
                        {
                            ["Exchange"] = symbol.PrimaryExchange,
                            ["Type"] = symbol.Type,
                            ["Market"] = symbol.Market,
                            ["TodaysChangePerc"] = snapshot.TodaysChangePerc ?? 0m
                        }
                    };

                    candidates.Add(candidate);
                }
                catch (Exception ex)
                {
                    _logger.LogDebug("Error processing symbol {Symbol}: {Error}", symbol.Ticker, ex.Message);
                }
            }

            return candidates;
        }
        finally
        {
            semaphore.Release();
        }
    }

    private static decimal CalculateRankingScore(decimal price, long volume, decimal volatility, PolygonSnapshotTicker snapshot)
    {
        // Composite scoring algorithm
        // Higher volume, moderate volatility, and positive momentum get higher scores
        var volumeScore = (decimal)(Math.Log10(volume) * 10); // Logarithmic volume scoring
        var volatilityScore = Math.Min(volatility * 2, 20m); // Cap volatility contribution
        var momentumScore = (snapshot.TodaysChangePerc ?? 0m) * 5m; // Today's performance
        var priceScore = Math.Min(price / 100m, 5m); // Price contribution (capped)

        return volumeScore + volatilityScore + momentumScore + priceScore;
    }

    private async Task<decimal?> CalculateSymbolVolatilityAsync(
        string symbol,
        DateTime startDate,
        DateTime endDate,
        int periodDays,
        HttpClient httpClient,
        IPolygonRateLimitHelper rateLimitHelper,
        CancellationToken cancellationToken)
    {
        try
        {
            var response = await rateLimitHelper.ExecuteAsync(async () =>
            {
                var url = $"v2/aggs/ticker/{symbol}/range/1/day/{startDate:yyyy-MM-dd}/{endDate:yyyy-MM-dd}?adjusted=true&sort=asc";
                var urlWithApiKey = _polygonFactory.AddApiKeyToUrl(url);
                return await httpClient.GetAsync(urlWithApiKey, cancellationToken);
            }, $"VolatilityCalc_{symbol}");

            if (!response.IsSuccessStatusCode)
            {
                return null;
            }

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var polygonResponse = JsonSerializer.Deserialize<PolygonAggregatesResponse>(content);

            if (polygonResponse?.Results == null || polygonResponse.Results.Count < periodDays)
            {
                return null;
            }

            // Calculate daily returns and volatility
            var closes = polygonResponse.Results
                .Where(r => r.Close.HasValue)
                .Select(r => (double)r.Close!.Value)
                .TakeLast(periodDays)
                .ToList();

            if (closes.Count < periodDays)
            {
                return null;
            }

            var returns = new List<double>();
            for (int i = 1; i < closes.Count; i++)
            {
                var dailyReturn = (closes[i] - closes[i - 1]) / closes[i - 1];
                returns.Add(dailyReturn);
            }

            if (!returns.Any())
            {
                return null;
            }

            var meanReturn = returns.Average();
            var variance = returns.Select(r => Math.Pow(r - meanReturn, 2)).Average();
            var volatility = Math.Sqrt(variance) * Math.Sqrt(252) * 100; // Annualized volatility as percentage

            return (decimal)volatility;
        }
        catch (Exception ex)
        {
            _logger.LogDebug("Error calculating volatility for {Symbol}: {Error}", symbol, ex.Message);
            return null;
        }
    }

    private async Task<long?> CalculateSymbolAverageVolumeAsync(
        string symbol,
        DateTime startDate,
        DateTime endDate,
        int periodDays,
        HttpClient httpClient,
        IPolygonRateLimitHelper rateLimitHelper,
        CancellationToken cancellationToken)
    {
        try
        {
            var response = await rateLimitHelper.ExecuteAsync(async () =>
            {
                var url = $"v2/aggs/ticker/{symbol}/range/1/day/{startDate:yyyy-MM-dd}/{endDate:yyyy-MM-dd}?adjusted=true&sort=asc";
                var urlWithApiKey = _polygonFactory.AddApiKeyToUrl(url);
                return await httpClient.GetAsync(urlWithApiKey, cancellationToken);
            }, $"VolumeCalc_{symbol}");

            if (!response.IsSuccessStatusCode)
            {
                return null;
            }

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var polygonResponse = JsonSerializer.Deserialize<PolygonAggregatesResponse>(content);

            if (polygonResponse?.Results == null || polygonResponse.Results.Count < periodDays)
            {
                return null;
            }

            var volumes = polygonResponse.Results
                .Where(r => r.Volume.HasValue)
                .Select(r => r.Volume!.Value)
                .TakeLast(periodDays)
                .ToList();

            if (volumes.Count < periodDays)
            {
                return null;
            }

            return (long)volumes.Average();
        }
        catch (Exception ex)
        {
            _logger.LogDebug("Error calculating average volume for {Symbol}: {Error}", symbol, ex.Message);
            return null;
        }
    }
}

/// <summary>
/// Configuration for snapshot service
/// </summary>
public class SnapshotServiceConfig
{
    public int BatchSize { get; set; } = 50;
    public int MaxConcurrency { get; set; } = 5;
    public int DelayBetweenCalls { get; set; } = 200;

    public static SnapshotServiceConfig Default => new()
    {
        BatchSize = 50,
        MaxConcurrency = 5,
        DelayBetweenCalls = 200
    };
}
