using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time VWAP calculator for individual symbols
/// Maintains rolling window of trades and calculates volume-weighted average price
/// </summary>
internal sealed class VWAPCalculator
{
    private readonly string _symbol;
    private readonly ILogger _logger;
    private VWAPMonitorConfig _config;
    
    private readonly ConcurrentQueue<TradeData> _trades = new();
    private decimal _cumulativeValue;
    private long _cumulativeVolume;
    private int _tradeCount;
    private decimal _lastPrice;
    private DateTime _lastUpdate = DateTime.UtcNow;
    private readonly object _calculationLock = new();
    
    public VWAPCalculator(string symbol, VWAPMonitorConfig config, ILogger logger)
    {
        _symbol = symbol ?? throw new ArgumentNullException(nameof(symbol));
        _config = config ?? throw new ArgumentNullException(nameof(config));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }
    
    public VWAPData? UpdateWithTrade(decimal price, long volume, DateTime timestamp)
    {
        if (volume < _config.MinVolumeThreshold)
            return null;
            
        lock (_calculationLock)
        {
            // Add new trade
            var trade = new TradeData(price, volume, timestamp);
            _trades.Enqueue(trade);
            
            _cumulativeValue += price * volume;
            _cumulativeVolume += volume;
            _tradeCount++;
            _lastPrice = price;
            _lastUpdate = timestamp;
            
            // Remove old trades outside rolling window
            CleanOldTrades(timestamp);
            
            return CalculateVWAP(timestamp);
        }
    }
    
    public VWAPData? UpdateWithAggregate(AggregateTick aggregate)
    {
        lock (_calculationLock)
        {
            // Use aggregate VWAP if available and reliable
            if (aggregate.Vwap > 0 && aggregate.Volume >= _config.MinVolumeThreshold)
            {
                // Add aggregate as a single large trade
                var trade = new TradeData(aggregate.Vwap, aggregate.Volume, aggregate.Timestamp);
                _trades.Enqueue(trade);
                
                _cumulativeValue += aggregate.Vwap * aggregate.Volume;
                _cumulativeVolume += aggregate.Volume;
                _tradeCount += (int)aggregate.TradeCount;
                _lastPrice = aggregate.Close;
                _lastUpdate = aggregate.Timestamp;
                
                // Remove old trades outside rolling window
                CleanOldTrades(aggregate.Timestamp);
                
                return CalculateVWAP(aggregate.Timestamp);
            }
            
            // Fallback to using close price with volume
            return UpdateWithTrade(aggregate.Close, aggregate.Volume, aggregate.Timestamp);
        }
    }
    
    public VWAPData? GetCurrentVWAP()
    {
        lock (_calculationLock)
        {
            return CalculateVWAP(DateTime.UtcNow);
        }
    }
    
    public void UpdateConfig(VWAPMonitorConfig config)
    {
        _config = config;
    }
    
    public void InitializeFromCache(VWAPData cachedData)
    {
        lock (_calculationLock)
        {
            _cumulativeValue = cachedData.CumulativeValue;
            _cumulativeVolume = cachedData.CumulativeVolume;
            _tradeCount = cachedData.TradeCount;
            _lastPrice = cachedData.CurrentPrice;
            _lastUpdate = cachedData.Timestamp;
        }
    }
    
    private VWAPData? CalculateVWAP(DateTime timestamp)
    {
        if (_tradeCount < _config.MinTradesRequired || _cumulativeVolume == 0)
            return null;
            
        var vwap = _cumulativeValue / _cumulativeVolume;
        var deviationPercent = vwap > 0 ? (_lastPrice - vwap) / vwap * 100 : 0;
        
        var trend = DetermineTrend(_lastPrice, vwap);
        
        return new VWAPData(
            Symbol: _symbol,
            VWAP: vwap,
            CurrentPrice: _lastPrice,
            DeviationPercent: deviationPercent,
            CumulativeVolume: _cumulativeVolume,
            CumulativeValue: _cumulativeValue,
            Timestamp: timestamp,
            TradeCount: _tradeCount,
            Trend: trend
        );
    }
    
    private void CleanOldTrades(DateTime currentTime)
    {
        var cutoffTime = currentTime.AddMinutes(-_config.RollingMinutes);
        
        while (_trades.TryPeek(out var oldestTrade) && oldestTrade.Timestamp < cutoffTime)
        {
            if (_trades.TryDequeue(out var removedTrade))
            {
                _cumulativeValue -= removedTrade.Price * removedTrade.Volume;
                _cumulativeVolume -= removedTrade.Volume;
                _tradeCount--;
            }
        }
        
        // Ensure we don't go negative due to floating point precision
        if (_cumulativeValue < 0) _cumulativeValue = 0;
        if (_cumulativeVolume < 0) _cumulativeVolume = 0;
        if (_tradeCount < 0) _tradeCount = 0;
    }
    
    private static VWAPTrend DetermineTrend(decimal currentPrice, decimal vwap)
    {
        const decimal tolerance = 0.001m; // 0.1% tolerance for "at VWAP"
        
        var deviation = Math.Abs(currentPrice - vwap) / vwap;
        
        if (deviation <= tolerance)
            return VWAPTrend.AtVWAP;
        
        return currentPrice > vwap ? VWAPTrend.AboveVWAP : VWAPTrend.BelowVWAP;
    }
}

/// <summary>
/// Internal trade data structure for VWAP calculation
/// </summary>
internal readonly record struct TradeData(
    decimal Price,
    long Volume,
    DateTime Timestamp
);
