using SmaTrendFollower.Services;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

public class StopManagerTests
{
    private readonly Mock<IAlpacaClientFactory> _mockClientFactory;
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<ILogger<StopManager>> _mockLogger;
    private readonly Mock<IAlpacaTradingClient> _mockTradingClient;
    private readonly Mock<IAlpacaRateLimitHelper> _mockRateLimitHelper;
    private readonly StopManager _stopManager;

    public StopManagerTests()
    {
        _mockClientFactory = new Mock<IAlpacaClientFactory>();
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockLogger = new Mock<ILogger<StopManager>>();
        _mockTradingClient = new Mock<IAlpacaTradingClient>();
        _mockRateLimitHelper = new Mock<IAlpacaRateLimitHelper>();

        _mockClientFactory.Setup(x => x.CreateTradingClient()).Returns(_mockTradingClient.Object);
        _mockClientFactory.Setup(x => x.GetRateLimitHelper()).Returns(_mockRateLimitHelper.Object);

        // Setup rate limit helper to execute actions directly
        _mockRateLimitHelper.Setup(x => x.ExecuteAsync<object>(It.IsAny<Func<Task<object>>>(), It.IsAny<string>()))
            .Returns<Func<Task<object>>, string>((func, key) => func());

        _stopManager = new StopManager(_mockClientFactory.Object, _mockMarketDataService.Object, _mockLogger.Object);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SetInitialStopAsync_WithValidParameters_CreatesStopOrder()
    {
        // Arrange
        var symbol = "AAPL";
        var entryPrice = 150m;
        var atr = 3m;
        var quantity = 10m;
        var expectedStopPrice = entryPrice - (2m * atr); // 150 - 6 = 144

        var mockOrder = new Mock<IOrder>();
        mockOrder.Setup(x => x.OrderId).Returns(Guid.NewGuid());

        _mockTradingClient.Setup(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockOrder.Object);

        // Act
        await _stopManager.SetInitialStopAsync(symbol, entryPrice, atr, quantity);

        // Assert
        _mockTradingClient.Verify(x => x.PostOrderAsync(
            It.Is<NewOrderRequest>(order =>
                order.Symbol == symbol &&
                order.Side == OrderSide.Sell &&
                order.Type == OrderType.Stop),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task UpdateTrailingStopsAsync_WithNoPositions_LogsAndReturns()
    {
        // Arrange
        var emptyPositions = new List<IPosition>();
        _mockTradingClient.Setup(x => x.ListPositionsAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptyPositions);

        // Act
        await _stopManager.UpdateTrailingStopsAsync();

        // Assert
        _mockTradingClient.Verify(x => x.ListPositionsAsync(It.IsAny<CancellationToken>()), Times.Once);
        // Should not attempt to update any stops
        _mockMarketDataService.Verify(x => x.GetStockBarsAsync(It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<DateTime>()), Times.Never);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task UpdateTrailingStopsAsync_WithLongPosition_UpdatesTrailingStopUp()
    {
        // Arrange
        var symbol = "AAPL";
        var currentPrice = 160m;
        var currentAtr = 4m;
        var expectedNewStopPrice = currentPrice - (2m * currentAtr); // 160 - 8 = 152

        // Mock position
        var mockPosition = new Mock<IPosition>();
        mockPosition.Setup(x => x.Symbol).Returns(symbol);
        mockPosition.Setup(x => x.Quantity).Returns(100m); // Long position

        _mockTradingClient.Setup(x => x.ListPositionsAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<IPosition> { mockPosition.Object });

        // Mock market data
        var bars = CreateMockBars(currentPrice, currentAtr);
        var mockResponse = new Mock<IPage<IBar>>();
        mockResponse.Setup(x => x.Items).Returns(bars);
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync(symbol, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(mockResponse.Object);

        // Mock existing stop order (lower than new calculated stop)
        var existingStopPrice = 145m; // Lower than new stop of 152
        var mockExistingOrder = new Mock<IOrder>();
        mockExistingOrder.Setup(x => x.Symbol).Returns(symbol);
        mockExistingOrder.Setup(x => x.OrderType).Returns(OrderType.Stop);
        mockExistingOrder.Setup(x => x.StopPrice).Returns(existingStopPrice);
        mockExistingOrder.Setup(x => x.OrderId).Returns(Guid.NewGuid());

        _mockTradingClient.Setup(x => x.ListOrdersAsync(It.IsAny<ListOrdersRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<IOrder> { mockExistingOrder.Object });

        // Mock new order creation
        var mockNewOrder = new Mock<IOrder>();
        mockNewOrder.Setup(x => x.OrderId).Returns(Guid.NewGuid());
        _mockTradingClient.Setup(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockNewOrder.Object);

        // Act
        await _stopManager.UpdateTrailingStopsAsync();

        // Assert
        // Should cancel existing stop
        _mockTradingClient.Verify(x => x.CancelOrderAsync(mockExistingOrder.Object.OrderId, It.IsAny<CancellationToken>()), Times.Once);

        // Should create new stop order
        _mockTradingClient.Verify(x => x.PostOrderAsync(
            It.Is<NewOrderRequest>(order =>
                order.Symbol == symbol &&
                order.Side == OrderSide.Sell &&
                order.Type == OrderType.Stop),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task UpdateTrailingStopsAsync_WithLongPositionAndHigherExistingStop_DoesNotUpdate()
    {
        // Arrange
        var symbol = "AAPL";
        var currentPrice = 160m;
        var currentAtr = 4m;
        var calculatedStopPrice = currentPrice - (2m * currentAtr); // 160 - 8 = 152

        // Mock position
        var mockPosition = new Mock<IPosition>();
        mockPosition.Setup(x => x.Symbol).Returns(symbol);
        mockPosition.Setup(x => x.Quantity).Returns(100m); // Long position

        _mockTradingClient.Setup(x => x.ListPositionsAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<IPosition> { mockPosition.Object });

        // Mock market data
        var bars = CreateMockBars(currentPrice, currentAtr);
        var mockResponse = new Mock<IPage<IBar>>();
        mockResponse.Setup(x => x.Items).Returns(bars);
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync(symbol, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(mockResponse.Object);

        // Mock existing stop order (higher than new calculated stop - should not update)
        var existingStopPrice = 155m; // Higher than calculated stop of 152
        var mockExistingOrder = new Mock<IOrder>();
        mockExistingOrder.Setup(x => x.Symbol).Returns(symbol);
        mockExistingOrder.Setup(x => x.OrderType).Returns(OrderType.Stop);
        mockExistingOrder.Setup(x => x.StopPrice).Returns(existingStopPrice);

        _mockTradingClient.Setup(x => x.ListOrdersAsync(It.IsAny<ListOrdersRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<IOrder> { mockExistingOrder.Object });

        // Act
        await _stopManager.UpdateTrailingStopsAsync();

        // Assert
        // Should NOT cancel existing stop
        _mockTradingClient.Verify(x => x.CancelOrderAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()), Times.Never);

        // Should NOT create new stop
        _mockTradingClient.Verify(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task RemoveStopsAsync_WithExistingStopOrders_CancelsAllStops()
    {
        // Arrange
        var symbol = "AAPL";
        var stopOrderId1 = Guid.NewGuid();
        var stopOrderId2 = Guid.NewGuid();

        var mockStopOrder1 = new Mock<IOrder>();
        mockStopOrder1.Setup(x => x.Symbol).Returns(symbol);
        mockStopOrder1.Setup(x => x.OrderType).Returns(OrderType.Stop);
        mockStopOrder1.Setup(x => x.OrderId).Returns(stopOrderId1);

        var mockStopOrder2 = new Mock<IOrder>();
        mockStopOrder2.Setup(x => x.Symbol).Returns(symbol);
        mockStopOrder2.Setup(x => x.OrderType).Returns(OrderType.Stop);
        mockStopOrder2.Setup(x => x.OrderId).Returns(stopOrderId2);

        // Mock other order types that should not be cancelled
        var mockLimitOrder = new Mock<IOrder>();
        mockLimitOrder.Setup(x => x.Symbol).Returns(symbol);
        mockLimitOrder.Setup(x => x.OrderType).Returns(OrderType.Limit);

        _mockTradingClient.Setup(x => x.ListOrdersAsync(It.IsAny<ListOrdersRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<IOrder> { mockStopOrder1.Object, mockStopOrder2.Object, mockLimitOrder.Object });

        // Act
        await _stopManager.RemoveStopsAsync(symbol);

        // Assert
        _mockTradingClient.Verify(x => x.CancelOrderAsync(stopOrderId1, It.IsAny<CancellationToken>()), Times.Once);
        _mockTradingClient.Verify(x => x.CancelOrderAsync(stopOrderId2, It.IsAny<CancellationToken>()), Times.Once);
        
        // Should not cancel the limit order
        _mockTradingClient.Verify(x => x.CancelOrderAsync(mockLimitOrder.Object.OrderId, It.IsAny<CancellationToken>()), Times.Never);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task UpdateTrailingStopsAsync_WithInsufficientData_SkipsUpdate()
    {
        // Arrange
        var symbol = "AAPL";
        var mockPosition = new Mock<IPosition>();
        mockPosition.Setup(x => x.Symbol).Returns(symbol);
        mockPosition.Setup(x => x.Quantity).Returns(100m);

        _mockTradingClient.Setup(x => x.ListPositionsAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<IPosition> { mockPosition.Object });

        // Mock insufficient data (less than 14 bars needed for ATR)
        var insufficientBars = CreateMockBars(150m, 3m, 10); // Only 10 bars
        var mockResponse = new Mock<IPage<IBar>>();
        mockResponse.Setup(x => x.Items).Returns(insufficientBars);
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync(symbol, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(mockResponse.Object);

        // Act
        await _stopManager.UpdateTrailingStopsAsync();

        // Assert
        // Should not attempt to update stops due to insufficient data
        _mockTradingClient.Verify(x => x.ListOrdersAsync(It.IsAny<ListOrdersRequest>(), It.IsAny<CancellationToken>()), Times.Never);
        _mockTradingClient.Verify(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    private List<IBar> CreateMockBars(decimal currentPrice, decimal atr, int count = 20)
    {
        var bars = new List<IBar>();
        var random = new Random(42); // Deterministic for testing

        for (int i = 0; i < count; i++)
        {
            var mockBar = new Mock<IBar>();
            var price = currentPrice + (decimal)(random.NextDouble() - 0.5) * 2m; // Small price variation
            var dailyRange = atr * 0.8m; // Approximate daily range for ATR calculation

            mockBar.Setup(x => x.Close).Returns(i == count - 1 ? currentPrice : price);
            mockBar.Setup(x => x.Open).Returns(price - dailyRange * 0.2m);
            mockBar.Setup(x => x.High).Returns(price + dailyRange * 0.6m);
            mockBar.Setup(x => x.Low).Returns(price - dailyRange * 0.6m);
            mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddDays(-count + i));

            bars.Add(mockBar.Object);
        }

        return bars;
    }
}
