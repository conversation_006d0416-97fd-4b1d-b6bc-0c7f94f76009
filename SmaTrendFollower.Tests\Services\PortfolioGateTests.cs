using SmaTrendFollower.Services;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

public class PortfolioGateTests
{
    private readonly Mock<IAlpacaClientFactory> _mockClientFactory;
    private readonly Mock<IAlpacaDataClient> _mockDataClient;
    private readonly Mock<ILogger<PortfolioGate>> _mockLogger;
    private readonly PortfolioGate _portfolioGate;

    public PortfolioGateTests()
    {
        _mockClientFactory = new Mock<IAlpacaClientFactory>();
        _mockDataClient = new Mock<IAlpacaDataClient>();
        _mockLogger = new Mock<ILogger<PortfolioGate>>();
        
        _mockClientFactory.Setup(x => x.CreateDataClient()).Returns(_mockDataClient.Object);
        
        _portfolioGate = new PortfolioGate(_mockClientFactory.Object, _mockLogger.Object);
    }

    [Fact]
    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    public async Task ShouldTradeAsync_WhenSpyAboveSma200_ReturnsTrue()
    {
        // Arrange - Need at least 200 bars for SMA200 calculation
        var bars = CreateMockBars(currentPrice: 450m, sma200Price: 400m, barCount: 250);
        var mockResponse = new Mock<IPage<IBar>>();
        mockResponse.Setup(x => x.Items).Returns(bars);

        _mockDataClient.Setup(x => x.ListHistoricalBarsAsync(It.IsAny<HistoricalBarsRequest>(), default))
            .ReturnsAsync(mockResponse.Object);

        // Act
        var result = await _portfolioGate.ShouldTradeAsync();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    public async Task ShouldTradeAsync_WhenSpyBelowSma200_ReturnsFalse()
    {
        // Arrange - Need at least 200 bars for SMA200 calculation
        var bars = CreateMockBars(currentPrice: 380m, sma200Price: 400m, barCount: 250);
        var mockResponse = new Mock<IPage<IBar>>();
        mockResponse.Setup(x => x.Items).Returns(bars);

        _mockDataClient.Setup(x => x.ListHistoricalBarsAsync(It.IsAny<HistoricalBarsRequest>(), default))
            .ReturnsAsync(mockResponse.Object);

        // Act
        var result = await _portfolioGate.ShouldTradeAsync();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    public async Task ShouldTradeAsync_WithInsufficientData_ReturnsFalse()
    {
        // Arrange - Use insufficient bar count to test validation
        var bars = TestDataFactory.CreateMinimalBars("SPY", 150); // Less than required 200

        // Verify we actually have insufficient bars
        bars.Should().HaveCount(150, "Test setup should create exactly 150 bars to test insufficient data condition");

        var mockResponse = new Mock<IPage<IBar>>();
        mockResponse.Setup(x => x.Items).Returns(bars);

        _mockDataClient.Setup(x => x.ListHistoricalBarsAsync(It.IsAny<HistoricalBarsRequest>(), default))
            .ReturnsAsync(mockResponse.Object);

        // Act
        var result = await _portfolioGate.ShouldTradeAsync();

        // Assert
        result.Should().BeFalse("PortfolioGate should return false when there are insufficient bars for SMA200 calculation");
    }

    private static List<IBar> CreateMockBars(decimal currentPrice, decimal sma200Price, int barCount = 250)
    {
        // Use optimized SMA test data factory for much faster execution
        var actualBarCount = Math.Max(barCount, TestConstants.LongSmaBarCount);
        return TestDataFactory.CreateSmaOptimizedBars("SPY", currentPrice, sma200Price, actualBarCount);
    }
}
