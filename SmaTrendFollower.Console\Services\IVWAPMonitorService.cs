using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time VWAP monitoring service for trend-following strategy enhancement
/// Calculates rolling real-time VWAP using tick/aggregate data with Redis caching
/// Enables entries only above VWAP in trending market regimes
/// </summary>
public interface IVWAPMonitorService : IDisposable
{
    // === Events ===
    
    /// <summary>
    /// Fired when VWAP is updated for a symbol
    /// </summary>
    event EventHandler<VWAPUpdateEventArgs>? VWAPUpdated;
    
    /// <summary>
    /// Fired when a symbol crosses above/below VWAP
    /// </summary>
    event EventHandler<VWAPCrossEventArgs>? VWAPCrossed;
    
    // === Core Methods ===
    
    /// <summary>
    /// Start monitoring VWAP for specified symbols
    /// </summary>
    Task StartMonitoringAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stop monitoring VWAP for all symbols
    /// </summary>
    Task StopMonitoringAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Add symbols to VWAP monitoring
    /// </summary>
    Task AddSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Remove symbols from VWAP monitoring
    /// </summary>
    Task RemoveSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    // === VWAP Data Access ===
    
    /// <summary>
    /// Get current VWAP for a symbol
    /// </summary>
    Task<VWAPData?> GetCurrentVWAPAsync(string symbol);
    
    /// <summary>
    /// Get VWAP history for a symbol
    /// </summary>
    Task<IEnumerable<VWAPData>> GetVWAPHistoryAsync(string symbol, int minutes = 60);
    
    /// <summary>
    /// Check if current price is above VWAP for trending regime entry
    /// </summary>
    Task<bool> IsPriceAboveVWAPAsync(string symbol, decimal currentPrice);
    
    /// <summary>
    /// Get VWAP deviation percentage for a symbol
    /// </summary>
    Task<decimal?> GetVWAPDeviationAsync(string symbol, decimal currentPrice);
    
    // === Status and Configuration ===
    
    /// <summary>
    /// Get list of currently monitored symbols
    /// </summary>
    IEnumerable<string> GetMonitoredSymbols();
    
    /// <summary>
    /// Get monitoring status
    /// </summary>
    VWAPMonitorStatus GetStatus();
    
    /// <summary>
    /// Update VWAP calculation parameters
    /// </summary>
    Task UpdateParametersAsync(VWAPMonitorConfig config);
}

/// <summary>
/// VWAP data structure with timestamp and metadata
/// </summary>
public record VWAPData(
    string Symbol,
    decimal VWAP,
    decimal CurrentPrice,
    decimal DeviationPercent,
    long CumulativeVolume,
    decimal CumulativeValue,
    DateTime Timestamp,
    int TradeCount,
    VWAPTrend Trend
);

/// <summary>
/// VWAP monitoring configuration
/// </summary>
public record VWAPMonitorConfig(
    int RollingMinutes = 30,
    int MinTradesRequired = 10,
    decimal MinVolumeThreshold = 1000,
    bool RequireTrendingRegime = true,
    decimal DeviationAlertThreshold = 2.0m,
    TimeSpan CacheExpiry = default
)
{
    public TimeSpan CacheExpiry { get; init; } = CacheExpiry == default ? TimeSpan.FromMinutes(5) : CacheExpiry;
}

/// <summary>
/// VWAP trend classification
/// </summary>
public enum VWAPTrend
{
    Unknown,
    AboveVWAP,
    BelowVWAP,
    AtVWAP
}

/// <summary>
/// VWAP monitoring status
/// </summary>
public enum VWAPMonitorStatus
{
    Stopped,
    Starting,
    Active,
    Error
}

/// <summary>
/// Event args for VWAP updates
/// </summary>
public class VWAPUpdateEventArgs : EventArgs
{
    public required VWAPData VWAPData { get; init; }
}

/// <summary>
/// Event args for VWAP crosses
/// </summary>
public class VWAPCrossEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required decimal Price { get; init; }
    public required decimal VWAP { get; init; }
    public required VWAPTrend NewTrend { get; init; }
    public required VWAPTrend PreviousTrend { get; init; }
    public required DateTime Timestamp { get; init; }
}
