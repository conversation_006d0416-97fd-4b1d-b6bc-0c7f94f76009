using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for Polygon WebSocket client for real-time streaming data
/// </summary>
public interface IPolygonWebSocketClient : IDisposable
{
    // === Events ===
    
    /// <summary>
    /// Fired when connection status changes
    /// </summary>
    event EventHandler<PolygonConnectionStatusEventArgs>? ConnectionStatusChanged;
    
    /// <summary>
    /// Fired when an index value update is received
    /// </summary>
    event EventHandler<PolygonIndexUpdateEventArgs>? IndexUpdated;

    /// <summary>
    /// Fired when a trade update is received
    /// </summary>
    event EventHandler<PolygonTradeUpdateEventArgs>? TradeUpdated;

    /// <summary>
    /// Fired when a quote update is received
    /// </summary>
    event EventHandler<PolygonQuoteUpdateEventArgs>? QuoteUpdated;

    /// <summary>
    /// Fired when an aggregate (minute bar) update is received
    /// </summary>
    event EventHandler<PolygonAggregateUpdateEventArgs>? AggregateUpdated;

    /// <summary>
    /// Fired when an error occurs
    /// </summary>
    event EventHandler<PolygonErrorEventArgs>? ErrorOccurred;
    
    // === Properties ===
    
    /// <summary>
    /// Current connection status
    /// </summary>
    PolygonConnectionStatus ConnectionStatus { get; }
    
    // === Connection Management ===
    
    /// <summary>
    /// Connects to Polygon WebSocket stream
    /// </summary>
    Task ConnectAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Disconnects from Polygon WebSocket stream
    /// </summary>
    Task DisconnectAsync(CancellationToken cancellationToken = default);
    
    // === Subscription Management ===
    
    /// <summary>
    /// Subscribe to index updates (e.g., I:VIX, I:SPX)
    /// </summary>
    Task SubscribeToIndexUpdatesAsync(IEnumerable<string> indexSymbols, CancellationToken cancellationToken = default);

    /// <summary>
    /// Subscribe to trade updates for specified stock symbols
    /// </summary>
    /// <param name="stockSymbols">Stock symbols to subscribe to (e.g., "AAPL", "MSFT")</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SubscribeToTradeUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default);

    /// <summary>
    /// Subscribe to quote updates for specified stock symbols
    /// </summary>
    /// <param name="stockSymbols">Stock symbols to subscribe to (e.g., "AAPL", "MSFT")</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SubscribeToQuoteUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default);

    /// <summary>
    /// Subscribe to minute aggregate updates for specified stock symbols
    /// </summary>
    /// <param name="stockSymbols">Stock symbols to subscribe to (e.g., "AAPL", "MSFT")</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SubscribeToAggregateUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default);

    /// <summary>
    /// Subscribe to equity updates (both trades and quotes) for specified stock symbols
    /// </summary>
    /// <param name="stockSymbols">Stock symbols to subscribe to (e.g., "AAPL", "MSFT")</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SubscribeToEquityUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default);

    /// <summary>
    /// Unsubscribe from index updates
    /// </summary>
    Task UnsubscribeFromIndexUpdatesAsync(IEnumerable<string> indexSymbols, CancellationToken cancellationToken = default);

    /// <summary>
    /// Unsubscribe from equity updates (both trades and quotes) for specified stock symbols
    /// </summary>
    /// <param name="stockSymbols">Stock symbols to unsubscribe from</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task UnsubscribeFromEquityUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default);

    /// <summary>
    /// Unsubscribe from all subscriptions
    /// </summary>
    Task UnsubscribeAllAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Connection status for Polygon WebSocket
/// </summary>
public enum PolygonConnectionStatus
{
    Disconnected,
    Connecting,
    Connected,
    Authenticating,
    Authenticated,
    Reconnecting,
    Error
}

/// <summary>
/// Event args for Polygon connection status changes
/// </summary>
public class PolygonConnectionStatusEventArgs : EventArgs
{
    public PolygonConnectionStatus Status { get; init; }
    public string? Message { get; init; }
    public Exception? Exception { get; init; }
}

/// <summary>
/// Event args for Polygon index updates
/// </summary>
public class PolygonIndexUpdateEventArgs : EventArgs
{
    public string IndexSymbol { get; init; } = "";
    public decimal Value { get; init; }
    public decimal Change { get; init; }
    public decimal ChangePercent { get; init; }
    public DateTime Timestamp { get; init; }
    public long Volume { get; init; }
}

/// <summary>
/// Event args for Polygon trade updates
/// </summary>
public class PolygonTradeUpdateEventArgs : EventArgs
{
    public string Symbol { get; init; } = "";
    public decimal Price { get; init; }
    public long Size { get; init; }
    public DateTime Timestamp { get; init; }
    public string Exchange { get; init; } = "";
    public string Conditions { get; init; } = "";
}

/// <summary>
/// Event args for Polygon quote updates
/// </summary>
public class PolygonQuoteUpdateEventArgs : EventArgs
{
    public string Symbol { get; init; } = "";
    public decimal BidPrice { get; init; }
    public decimal AskPrice { get; init; }
    public long BidSize { get; init; }
    public long AskSize { get; init; }
    public DateTime Timestamp { get; init; }
    public string Exchange { get; init; } = "";
}

/// <summary>
/// Event args for Polygon aggregate (minute bar) updates
/// </summary>
public class PolygonAggregateUpdateEventArgs : EventArgs
{
    public string Symbol { get; init; } = "";
    public decimal Open { get; init; }
    public decimal High { get; init; }
    public decimal Low { get; init; }
    public decimal Close { get; init; }
    public long Volume { get; init; }
    public decimal Vwap { get; init; }
    public DateTime Timestamp { get; init; }
    public long TradeCount { get; init; }
}

/// <summary>
/// Event args for Polygon errors
/// </summary>
public class PolygonErrorEventArgs : EventArgs
{
    public string Message { get; init; } = "";
    public int? ErrorCode { get; init; }
    public Exception? Exception { get; init; }
}

/// <summary>
/// Polygon WebSocket message types
/// </summary>
public static class PolygonMessageTypes
{
    public const string Status = "status";
    public const string IndexValue = "V"; // Index value update
    public const string Trade = "T"; // Trade update
    public const string Quote = "Q"; // Quote update
    public const string Aggregate = "A"; // Minute aggregate update
    public const string Error = "error";
    public const string Success = "success";
}

/// <summary>
/// Base class for Polygon WebSocket messages
/// </summary>
public abstract class PolygonWebSocketMessage
{
    public string EventType { get; init; } = "";
}

/// <summary>
/// Polygon status message
/// </summary>
public class PolygonStatusMessage : PolygonWebSocketMessage
{
    public string Status { get; init; } = "";
    public string Message { get; init; } = "";
}

/// <summary>
/// Polygon index value message
/// </summary>
public class PolygonIndexValueMessage : PolygonWebSocketMessage
{
    public string Symbol { get; init; } = "";
    public decimal Value { get; init; }
    public long Timestamp { get; init; }
    public decimal? Change { get; init; }
    public decimal? ChangePercent { get; init; }
    public long? Volume { get; init; }
}

/// <summary>
/// Polygon trade message
/// </summary>
public class PolygonTradeMessage : PolygonWebSocketMessage
{
    public string Symbol { get; init; } = "";
    public decimal Price { get; init; }
    public long Size { get; init; }
    public long Timestamp { get; init; }
    public string Exchange { get; init; } = "";
    public string Conditions { get; init; } = "";
}

/// <summary>
/// Polygon quote message
/// </summary>
public class PolygonQuoteMessage : PolygonWebSocketMessage
{
    public string Symbol { get; init; } = "";
    public decimal BidPrice { get; init; }
    public decimal AskPrice { get; init; }
    public long BidSize { get; init; }
    public long AskSize { get; init; }
    public long Timestamp { get; init; }
    public string Exchange { get; init; } = "";
}

/// <summary>
/// Polygon aggregate (minute bar) message
/// </summary>
public class PolygonAggregateMessage : PolygonWebSocketMessage
{
    public string Symbol { get; init; } = "";
    public decimal Open { get; init; }
    public decimal High { get; init; }
    public decimal Low { get; init; }
    public decimal Close { get; init; }
    public long Volume { get; init; }
    public decimal Vwap { get; init; }
    public long Timestamp { get; init; }
    public long TradeCount { get; init; }
}

/// <summary>
/// Polygon error message
/// </summary>
public class PolygonErrorMessage : PolygonWebSocketMessage
{
    public string Message { get; init; } = "";
    public int? Code { get; init; }
}
