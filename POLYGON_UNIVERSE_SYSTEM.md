# Polygon-Based Symbol Universe Management System

## Overview

The SmaTrendFollower trading system now includes a comprehensive Polygon-based symbol universe management system that automatically discovers, filters, and manages tradeable symbols for optimal trading performance. This system replaces static symbol lists with dynamic, data-driven universe selection.

## Architecture

### Core Components

1. **PolygonSymbolUniverseService** - Fetches and caches full symbol lists from Polygon API
2. **PolygonSymbolSnapshotService** - Provides real-time filtering and ranking capabilities
3. **DailyUniverseRefreshService** - Manages daily universe refresh and candidate selection
4. **WebSocketSymbolSubscriptionManager** - Handles real-time data subscriptions
5. **MarketScheduleCoordinatorService** - Coordinates timing of all universe operations
6. **Enhanced DynamicUniverseProvider** - Integrates Polygon data with existing universe logic

### Data Flow

```mermaid
graph TD
    A[Polygon API] --> B[PolygonSymbolUniverseService]
    B --> C[Weekly Symbol Cache]
    A --> D[PolygonSymbolSnapshotService]
    D --> E[Real-time Filtering]
    B --> F[DailyUniverseRefreshService]
    E --> F
    F --> G[Daily Candidates Cache]
    G --> H[WebSocketSubscriptionManager]
    H --> I[Real-time Trading Data]
    J[MarketScheduleCoordinator] --> F
    J --> H
    J --> K[Trading Engine]
```

## Daily Workflow

### 8:30 AM ET - Universe Refresh
- Fetch latest symbol list from Polygon (if cache is stale)
- Apply volume, volatility, and price filters
- Generate top ~200 candidates
- Cache results in Redis as `universe:candidates`

### 9:25 AM ET - WebSocket Subscription
- Subscribe to real-time data for filtered candidates
- Setup trade and quote streams
- Prepare for market open

### 9:30 AM ET - Trading Start
- Signal engine begins processing
- Execution service starts
- Real-time trading begins

## Configuration

### Environment Variables

```bash
# Polygon API Configuration
POLY_API_KEY=your_polygon_api_key

# Redis Configuration (optional but recommended)
REDIS_URL=localhost:6379
REDIS_DATABASE=0
REDIS_PASSWORD=your_redis_password

# Enable Polygon Universe Integration
UsePolygonUniverse=true
```

### Application Configuration

```json
{
  "PolygonUniverse": {
    "PageSize": 1000,
    "MaxSymbols": 0,
    "IncludedMarkets": ["stocks"],
    "IncludedTypes": ["CS", "ETF"],
    "ActiveOnly": true,
    "DelayBetweenCalls": 200,
    "CacheTtlHours": 168
  },
  "PolygonSnapshot": {
    "BatchSize": 50,
    "MaxConcurrency": 5,
    "DelayBetweenCalls": 200
  },
  "UniverseRefresh": {
    "RefreshTimeUtc": "12:30:00",
    "MinPrice": 10.0,
    "MinAverageVolume": 1000000,
    "MinVolatilityPercent": 2.0,
    "MaxCandidates": 200,
    "AnalysisPeriodDays": 20,
    "CacheTtlHours": 24
  },
  "SubscriptionManager": {
    "SubscriptionTimeUtc": "13:25:00",
    "MaxSubscriptions": 200,
    "BatchSize": 50,
    "DelayBetweenBatches": 1000
  },
  "MarketSchedule": {
    "UniverseRefreshTimeEt": "08:30:00",
    "SubscriptionSetupTimeEt": "09:25:00",
    "TradingStartTimeEt": "09:30:00",
    "TradingEndTimeEt": "16:00:00",
    "TriggerWindowMinutes": 30
  }
}
```

## API Reference

### IPolygonSymbolUniverseService

```csharp
public interface IPolygonSymbolUniverseService
{
    // Fetch full symbol list with pagination
    Task<IEnumerable<PolygonSymbolInfo>> FetchFullSymbolListAsync(CancellationToken cancellationToken = default);
    
    // Get cached symbols or fetch if stale
    Task<IEnumerable<PolygonSymbolInfo>> GetSymbolListAsync(CancellationToken cancellationToken = default);
    
    // Force refresh from API
    Task<IEnumerable<PolygonSymbolInfo>> RefreshSymbolListAsync(CancellationToken cancellationToken = default);
    
    // Check cache validity
    Task<bool> IsCacheValidAsync(CancellationToken cancellationToken = default);
    
    // Get cache details and metrics
    Task<RedisPolygonSymbolList?> GetCacheDetailsAsync(CancellationToken cancellationToken = default);
}
```

### IPolygonSymbolSnapshotService

```csharp
public interface IPolygonSymbolSnapshotService
{
    // Get all market snapshots
    Task<IEnumerable<PolygonSnapshotTicker>> GetAllSnapshotsAsync(CancellationToken cancellationToken = default);
    
    // Get snapshots for specific symbols
    Task<IEnumerable<PolygonSnapshotTicker>> GetSnapshotsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    // Filter and rank symbols
    Task<IEnumerable<UniverseCandidate>> FilterAndRankSymbolsAsync(
        IEnumerable<PolygonSymbolInfo> symbols,
        CandidateFilterCriteria criteria,
        CancellationToken cancellationToken = default);
    
    // Calculate historical volatility
    Task<Dictionary<string, decimal>> GetHistoricalVolatilityAsync(
        IEnumerable<string> symbols,
        int periodDays = 20,
        CancellationToken cancellationToken = default);
    
    // Calculate average volume
    Task<Dictionary<string, long>> GetAverageVolumeAsync(
        IEnumerable<string> symbols,
        int periodDays = 20,
        CancellationToken cancellationToken = default);
}
```

### IDailyUniverseRefreshService

```csharp
public interface IDailyUniverseRefreshService
{
    // Manually trigger refresh
    Task<RedisUniverseCandidates> RefreshUniverseAsync(CancellationToken cancellationToken = default);
    
    // Get current cached candidates
    Task<RedisUniverseCandidates?> GetCurrentCandidatesAsync(CancellationToken cancellationToken = default);
    
    // Check cache validity
    Task<bool> IsCacheValidAsync(CancellationToken cancellationToken = default);
    
    // Get service status
    Task<UniverseRefreshStatus> GetStatusAsync(CancellationToken cancellationToken = default);
}
```

## Data Models

### PolygonSymbolInfo

```csharp
public class PolygonSymbolInfo
{
    public string Ticker { get; set; }
    public string Name { get; set; }
    public string Market { get; set; }
    public string Locale { get; set; }
    public string PrimaryExchange { get; set; }
    public string Type { get; set; }
    public bool Active { get; set; }
    public string CurrencyName { get; set; }
    public DateTime? LastUpdatedUtc { get; set; }
}
```

### UniverseCandidate

```csharp
public class UniverseCandidate
{
    public string Symbol { get; set; }
    public decimal Price { get; set; }
    public long AverageVolume { get; set; }
    public decimal VolatilityPercent { get; set; }
    public decimal? MarketCap { get; set; }
    public decimal RankingScore { get; set; }
    public DateTime LastUpdated { get; set; }
    public Dictionary<string, object> Metadata { get; set; }
}
```

### CandidateFilterCriteria

```csharp
public class CandidateFilterCriteria
{
    public decimal MinPrice { get; set; } = 10.0m;
    public long MinAverageVolume { get; set; } = 1_000_000;
    public decimal MinVolatilityPercent { get; set; } = 2.0m;
    public int MaxCandidates { get; set; } = 200;
    public int AnalysisPeriodDays { get; set; } = 20;
    public decimal? MinMarketCap { get; set; }
    public List<string> ExcludedExchanges { get; set; }
    public List<string> ExcludedTypes { get; set; }
}
```

## Caching Strategy

### Redis Keys

- `polygon:symbols:full` - Full symbol list cache (1 week TTL)
- `universe:candidates` - Daily filtered candidates (24 hour TTL)
- `universe:candidates:YYYYMMDD` - Historical candidates (7 day TTL)

### Cache Hierarchy

1. **Daily Candidates** (Primary) - Pre-filtered, ready-to-use symbols
2. **Symbol Universe** (Secondary) - Full Polygon symbol list
3. **Traditional Cache** (Fallback) - Original DynamicUniverseProvider cache

## Performance Considerations

### API Rate Limits

- Polygon API: 5 requests/second (basic), 100 requests/second (advanced)
- Automatic rate limiting with exponential backoff
- Batch processing for large symbol sets

### Memory Usage

- Symbol cache: ~1-5MB for full US equity universe
- Candidate cache: ~50KB for 200 candidates
- Efficient JSON serialization for Redis storage

### Network Optimization

- Pagination for large datasets
- Compression for cached data
- Connection pooling for HTTP clients

## Monitoring and Observability

### Key Metrics

- Symbol fetch time and success rate
- Candidate generation time and count
- Cache hit/miss ratios
- WebSocket subscription success rate
- Market schedule adherence

### Logging

- Structured logging with correlation IDs
- Performance metrics for each operation
- Error tracking with context
- Debug information for troubleshooting

## Deployment

### Service Registration

```csharp
// Program.cs
services.AddSingleton<IPolygonSymbolUniverseService, PolygonSymbolUniverseService>();
services.AddSingleton<IPolygonSymbolSnapshotService, PolygonSymbolSnapshotService>();
services.AddSingleton<IDailyUniverseRefreshService, DailyUniverseRefreshService>();
services.AddSingleton<IWebSocketSymbolSubscriptionManager, WebSocketSymbolSubscriptionManager>();
services.AddSingleton<IMarketScheduleCoordinatorService, MarketScheduleCoordinatorService>();

// Background services
services.AddHostedService<DailyUniverseRefreshService>();
services.AddHostedService<WebSocketSymbolSubscriptionManager>();
services.AddHostedService<MarketScheduleCoordinatorService>();
```

### Health Checks

```csharp
services.AddHealthChecks()
    .AddCheck<PolygonApiHealthCheck>("polygon-api")
    .AddCheck<RedisHealthCheck>("redis")
    .AddCheck<UniverseServiceHealthCheck>("universe-service");
```

## Testing

### Unit Tests

- Service logic validation
- Filter criteria application
- Error handling scenarios
- Cache behavior verification

### Integration Tests

- Real Polygon API connectivity
- End-to-end workflow validation
- Performance benchmarking
- Cache integration testing

### Test Configuration

```bash
# Set for integration tests
POLY_API_KEY=your_test_api_key
REDIS_URL=localhost:6379
REDIS_DATABASE=1  # Use separate test database
```

## Troubleshooting

### Common Issues

1. **API Key Issues**
   - Verify POLY_API_KEY is set correctly
   - Check API key permissions and subscription level

2. **Cache Problems**
   - Verify Redis connectivity
   - Check Redis memory usage
   - Validate TTL settings

3. **Performance Issues**
   - Monitor API rate limits
   - Check network connectivity
   - Verify concurrent processing limits

4. **Schedule Timing**
   - Verify timezone configuration
   - Check market calendar integration
   - Monitor service startup timing

### Debug Commands

```bash
# Check service status
curl http://localhost:5000/health

# View current candidates
curl http://localhost:5000/api/universe/candidates

# Check market schedule
curl http://localhost:5000/api/schedule/status

# Force universe refresh
curl -X POST http://localhost:5000/api/universe/refresh
```

## Migration Guide

### From Static Universe

1. Enable Polygon integration: `UsePolygonUniverse=true`
2. Configure API credentials
3. Test with small symbol sets
4. Monitor performance and adjust settings
5. Gradually increase universe size

### Backward Compatibility

- Original DynamicUniverseProvider remains functional
- Automatic fallback to static symbols if Polygon unavailable
- Gradual migration path with feature flags
