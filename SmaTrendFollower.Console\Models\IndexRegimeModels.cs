using System.Text.Json;

namespace SmaTrendFollower.Models;

/// <summary>
/// Index-based market regime classification
/// </summary>
public enum IndexMarketRegime
{
    /// <summary>
    /// Strong bullish trend with low VIX and positive SPX momentum
    /// </summary>
    TrendingUp,

    /// <summary>
    /// Bearish trend with negative SPX momentum
    /// </summary>
    TrendingDown,

    /// <summary>
    /// Range-bound market with neutral momentum
    /// </summary>
    Sideways,

    /// <summary>
    /// High volatility with elevated VIX
    /// </summary>
    Volatile,

    /// <summary>
    /// Panic conditions with VIX spike and negative momentum
    /// </summary>
    Panic,

    /// <summary>
    /// Euphoric conditions with very low VIX and strong momentum
    /// </summary>
    Euphoric
}

/// <summary>
/// Divergence types
/// </summary>
public enum DivergenceType
{
    Bullish, // SPX up, VIX down
    Bearish, // SPX down, VIX up
    Neutral
}

/// <summary>
/// SPX momentum analysis data
/// </summary>
public class SpxMomentumAnalysis
{
    public decimal CurrentPrice { get; set; }
    public decimal PreviousPrice { get; set; }
    public decimal MomentumPercent { get; set; }
    public decimal IntraDayHigh { get; set; }
    public decimal IntraDayLow { get; set; }
    public decimal IntraDayRange { get; set; }
    public DateTime AnalyzedAt { get; set; }
    public MomentumDirection Direction { get; set; }
    public MomentumStrength Strength { get; set; }
}

/// <summary>
/// VIX analysis data
/// </summary>
public class VixAnalysis
{
    public decimal CurrentVix { get; set; }
    public decimal PreviousVix { get; set; }
    public decimal VixChange { get; set; }
    public decimal VixChangePercent { get; set; }
    public decimal DayHigh { get; set; }
    public decimal DayLow { get; set; }
    public decimal TwentyDayAverage { get; set; }
    public DateTime AnalyzedAt { get; set; }
    public VixLevel Level { get; set; }
    public VixTrend Trend { get; set; }
}

/// <summary>
/// SPX-VIX divergence analysis
/// </summary>
public class SpxVixDivergenceAnalysis
{
    public decimal SpxChange { get; set; }
    public decimal VixChange { get; set; }
    public decimal DivergenceScore { get; set; }
    public decimal CorrelationCoefficient { get; set; }
    public DateTime AnalyzedAt { get; set; }
    public DivergenceType DivergenceType { get; set; }
    public DivergenceSignificance Significance { get; set; }
    public bool IsAnomalous { get; set; }
}

/// <summary>
/// NDX momentum analysis for tech sector
/// </summary>
public class NdxMomentumAnalysis
{
    public decimal CurrentPrice { get; set; }
    public decimal PreviousPrice { get; set; }
    public decimal MomentumPercent { get; set; }
    public decimal RelativeToSpx { get; set; }
    public DateTime AnalyzedAt { get; set; }
    public MomentumDirection Direction { get; set; }
    public TechSectorStrength TechStrength { get; set; }
}

/// <summary>
/// Index regime history entry
/// </summary>
public class IndexRegimeHistoryEntry
{
    public IndexMarketRegime Regime { get; set; }
    public DateTime DetectedAt { get; set; }
    public decimal Confidence { get; set; }
    public SpxMomentumAnalysis SpxMomentum { get; set; } = new();
    public VixAnalysis VixAnalysis { get; set; } = new();
    public SpxVixDivergenceAnalysis Divergence { get; set; } = new();
    public string Metadata { get; set; } = string.Empty;
}

/// <summary>
/// Redis model for index regime data
/// </summary>
public class RedisIndexRegime
{
    public IndexMarketRegime Regime { get; set; }
    public DateTime DetectedAt { get; set; }
    public decimal Confidence { get; set; }
    public SpxMomentumAnalysis SpxMomentum { get; set; } = new();
    public VixAnalysis VixAnalysis { get; set; } = new();
    public SpxVixDivergenceAnalysis Divergence { get; set; } = new();
    public NdxMomentumAnalysis NdxMomentum { get; set; } = new();
    public string Metadata { get; set; } = string.Empty;

    /// <summary>
    /// Serialize to JSON for Redis storage
    /// </summary>
    public string ToJson()
    {
        return JsonSerializer.Serialize(this);
    }

    /// <summary>
    /// Deserialize from JSON stored in Redis
    /// </summary>
    public static RedisIndexRegime? FromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
            return null;

        try
        {
            return JsonSerializer.Deserialize<RedisIndexRegime>(json);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Generate Redis key for index regime data
    /// </summary>
    public static string GetRedisKey() => "index:regime:current";

    /// <summary>
    /// Generate Redis key for historical index regime data
    /// </summary>
    public static string GetRedisKey(DateTime timestamp) => $"index:regime:{timestamp:yyyyMMddHHmm}";
}

/// <summary>
/// Momentum direction
/// </summary>
public enum MomentumDirection
{
    Bullish,
    Bearish,
    Neutral
}

/// <summary>
/// Momentum strength
/// </summary>
public enum MomentumStrength
{
    Weak,
    Moderate,
    Strong,
    Extreme
}

/// <summary>
/// VIX level classification
/// </summary>
public enum VixLevel
{
    VeryLow,    // < 12
    Low,        // 12-16
    Normal,     // 16-20
    Elevated,   // 20-25
    High,       // 25-30
    VeryHigh,   // 30-35
    Extreme     // > 35
}

/// <summary>
/// VIX trend direction
/// </summary>
public enum VixTrend
{
    Declining,
    Stable,
    Rising,
    Spiking
}

/// <summary>
/// Divergence significance
/// </summary>
public enum DivergenceSignificance
{
    Insignificant,
    Mild,
    Moderate,
    Strong,
    Extreme
}

/// <summary>
/// Tech sector strength relative to broader market
/// </summary>
public enum TechSectorStrength
{
    Underperforming,
    InLine,
    Outperforming,
    Leading
}
