#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Adds missing using statements to test files
.DESCRIPTION
    This script adds the missing 'using SmaTrendFollower.Tests.TestHelpers;' statement
    to all test files that are missing it.
#>

param(
    [switch]$DryRun = $false
)

# Get all C# test files
$testFiles = Get-ChildItem -Path "SmaTrendFollower.Tests" -Filter "*.cs" -Recurse | Where-Object {
    $_.Name -notlike "TestHelpers*" -and 
    $_.Name -notlike "TestConfiguration*" -and
    $_.Name -notlike "TestTimeoutAttribute*" -and
    $_.Name -notlike "TestCollections*" -and
    $_.Name -notlike "TestDatabaseHelper*" -and
    $_.Name -notlike "TestDataFactory*"
}

$modifiedCount = 0
$totalCount = $testFiles.Count

Write-Host "Found $totalCount test files to process..." -ForegroundColor Green

foreach ($file in $testFiles) {
    $content = Get-Content $file.FullName -Raw
    
    # Check if the file already has the TestHelpers using statement
    if ($content -notmatch "using SmaTrendFollower\.Tests\.TestHelpers;") {
        Write-Host "Processing: $($file.Name)" -ForegroundColor Yellow
        
        # Find the last using statement
        $lines = $content -split "`r?`n"
        $lastUsingIndex = -1
        
        for ($i = 0; $i -lt $lines.Count; $i++) {
            if ($lines[$i] -match "^using\s+") {
                $lastUsingIndex = $i
            }
            elseif ($lines[$i] -match "^namespace\s+" -or $lines[$i] -match "^\s*$") {
                # Stop looking once we hit namespace or empty line after usings
                if ($lastUsingIndex -ge 0) {
                    break
                }
            }
        }
        
        if ($lastUsingIndex -ge 0) {
            # Insert the new using statement after the last using statement
            $newLines = @()
            $newLines += $lines[0..$lastUsingIndex]
            $newLines += "using SmaTrendFollower.Tests.TestHelpers;"
            $newLines += $lines[($lastUsingIndex + 1)..($lines.Count - 1)]
            
            $newContent = $newLines -join "`n"
            
            if (-not $DryRun) {
                Set-Content -Path $file.FullName -Value $newContent -NoNewline
                Write-Host "  ✅ Added TestHelpers using statement" -ForegroundColor Green
            } else {
                Write-Host "  [DRY RUN] Would add TestHelpers using statement" -ForegroundColor Cyan
            }
            
            $modifiedCount++
        } else {
            Write-Host "  ⚠️  Could not find using statements section" -ForegroundColor Red
        }
    } else {
        Write-Host "Skipping: $($file.Name) (already has TestHelpers using)" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "Summary:" -ForegroundColor Green
Write-Host "  Total files processed: $totalCount" -ForegroundColor White
Write-Host "  Files modified: $modifiedCount" -ForegroundColor White

if ($DryRun) {
    Write-Host ""
    Write-Host "This was a dry run. Use -DryRun:`$false to apply changes." -ForegroundColor Yellow
}
