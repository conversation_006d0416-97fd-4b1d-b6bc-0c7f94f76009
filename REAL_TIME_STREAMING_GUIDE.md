# Real-time Streaming Configuration Guide

## 🔄 Overview

SmaTrendFollower's real-time streaming implementation provides live market data processing using WebSocket connections to both Alpaca and Polygon APIs. This guide covers setup, configuration, and best practices for real-time data streaming.

## 🏗️ Architecture

### Dual-Source Streaming Strategy
- **Alpaca WebSocket**: Primary source for equity quotes, bars, and trade execution updates
- **Polygon WebSocket**: Secondary source for index data (VIX, SPX) and options quotes
- **Intelligent Failover**: Automatic switching between sources for reliability

### Connection Management
```csharp
public enum ConnectionStatus
{
    Disconnected,    // Not connected to any streams
    Connecting,      // Establishing connections
    Connected,       // Successfully connected and receiving data
    Reconnecting,    // Attempting to restore lost connections
    Error           // Connection error state
}
```

## ⚙️ Configuration

### Environment Variables
```bash
# Core streaming settings
ENABLE_REAL_TIME_STREAMING=true
STREAMING_SYMBOLS=SPY,QQQ,AAPL,MSFT,NVDA,TSLA,GOOGL,AMZN
STREAMING_MODE=production  # or development

# WebSocket configuration
ALPACA_WEBSOCKET_ENABLED=true
POLYGON_WEBSOCKET_ENABLED=true
WEBSOCKET_TIMEOUT=30000  # 30 seconds
WEBSOCKET_BUFFER_SIZE=8192

# Connection management
WEBSOCKET_RECONNECT_ATTEMPTS=5
WEBSOCKET_RECONNECT_DELAY=5000  # 5 seconds
WEBSOCKET_HEARTBEAT_INTERVAL=30000  # 30 seconds
CONNECTION_HEALTH_CHECK_INTERVAL=60000  # 1 minute

# Data processing
STREAMING_QUEUE_SIZE=1000
STREAMING_BATCH_SIZE=50
STREAMING_PROCESSING_DELAY=100  # milliseconds

# VIX monitoring
VIX_SPIKE_THRESHOLD=25.0
VIX_MONITORING_ENABLED=true
VIX_ALERT_COOLDOWN=300000  # 5 minutes
```

### Service Registration
```csharp
// In Program.cs - Streaming services are automatically registered
services.AddFullTradingSystem();

// Or register individually
services.AddSingleton<IStreamingDataService, StreamingDataService>();
services.AddSingleton<IRealTimeMarketMonitor, RealTimeMarketMonitor>();

// Configure streaming options
services.Configure<StreamingConfiguration>(configuration.GetSection("Streaming"));
```
```

## 📡 Data Sources and Events

### Alpaca WebSocket Events
```csharp
// Quote updates for equity symbols
streamingService.QuoteReceived += async (sender, args) =>
{
    await ProcessEquityQuoteAsync(args.Symbol, args.Quote);
};

// Real-time bar data (minute bars)
streamingService.BarReceived += async (sender, args) =>
{
    await ProcessEquityBarAsync(args.Symbol, args.Bar);
};

// Trade execution updates
streamingService.TradeUpdated += async (sender, args) =>
{
    await ProcessTradeUpdateAsync(args.Order);
};
```

### Polygon WebSocket Events
```csharp
// Index value updates (VIX, SPX, etc.)
streamingService.IndexUpdated += async (sender, args) =>
{
    await ProcessIndexUpdateAsync(args.Symbol, args.Value);
};

// VIX spike detection
streamingService.VixSpikeDetected += async (sender, args) =>
{
    await HandleVixSpikeAsync(args.VixValue, args.Timestamp);
};

// Options quotes (if enabled)
streamingService.OptionsQuoteReceived += async (sender, args) =>
{
    await ProcessOptionsQuoteAsync(args.OptionSymbol, args.Quote);
};
```

## 🔧 Implementation Examples

### Basic Streaming Setup
```csharp
public class TradingService
{
    private readonly IStreamingDataService _streamingService;
    private readonly ILogger<TradingService> _logger;
    
    public async Task StartTradingAsync()
    {
        // Subscribe to streaming events
        _streamingService.QuoteReceived += OnQuoteReceived;
        _streamingService.VixSpikeDetected += OnVixSpike;
        
        // Start streaming for active symbols
        var symbols = await GetActiveSymbolsAsync();
        await _streamingService.StartStreamingAsync(symbols);
        
        _logger.LogInformation("Real-time streaming started for {Count} symbols", symbols.Count());
    }
    
    private async Task OnQuoteReceived(object sender, StreamingQuoteEventArgs args)
    {
        // Process real-time quote
        await UpdatePositionMonitoringAsync(args.Symbol, args.Quote.BidPrice);
        
        // Check for immediate exit conditions
        var shouldExit = await EvaluateExitConditionsAsync(args.Symbol, args.Quote.BidPrice);
        if (shouldExit)
        {
            await ExecuteImmediateExitAsync(args.Symbol);
        }
    }
}
```

### VIX Spike Handling
```csharp
private async Task OnVixSpike(object sender, VixSpikeEventArgs args)
{
    _logger.LogWarning("VIX spike detected: {VixValue} at {Timestamp}", 
        args.VixValue, args.Timestamp);
    
    // Immediate risk management actions
    await ReducePositionSizesAsync(0.5m);  // Reduce to 50% size
    await TightenStopLossesAsync(0.75m);   // Tighten stops to 75%
    await PauseNewPositionsAsync(TimeSpan.FromMinutes(30));
    
    // Send alert notification
    await _discordService.SendVixSpikeAlertAsync(args.VixValue, args.Timestamp);
}
```

### Connection Health Monitoring
```csharp
public class StreamingHealthMonitor
{
    private readonly IStreamingDataService _streamingService;
    private readonly Timer _healthCheckTimer;
    
    public StreamingHealthMonitor(IStreamingDataService streamingService)
    {
        _streamingService = streamingService;
        _healthCheckTimer = new Timer(CheckConnectionHealth, null, 
            TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }
    
    private async void CheckConnectionHealth(object state)
    {
        var status = _streamingService.GetConnectionStatus();
        
        if (status != ConnectionStatus.Connected)
        {
            _logger.LogWarning("Streaming connection unhealthy: {Status}", status);
            
            // Attempt reconnection
            if (status == ConnectionStatus.Error)
            {
                await _streamingService.RestartConnectionAsync();
            }
        }
    }
}
```

## 📊 Performance Optimization

### Data Processing Pipeline
```csharp
// Efficient quote processing with batching
private readonly ConcurrentQueue<StreamingQuote> _quoteQueue = new();
private readonly Timer _processingTimer;

private async void ProcessQuoteBatch(object state)
{
    var quotes = new List<StreamingQuote>();
    
    // Dequeue batch of quotes
    while (quotes.Count < _config.BatchSize && _quoteQueue.TryDequeue(out var quote))
    {
        quotes.Add(quote);
    }
    
    if (quotes.Any())
    {
        await ProcessQuotesInBatchAsync(quotes);
    }
}
```

### Memory Management
```csharp
// Efficient memory usage for streaming data
public class StreamingDataBuffer
{
    private readonly ConcurrentDictionary<string, CircularBuffer<IQuote>> _quoteBuffers;
    private readonly int _bufferSize;
    
    public StreamingDataBuffer(int bufferSize = 1000)
    {
        _bufferSize = bufferSize;
        _quoteBuffers = new ConcurrentDictionary<string, CircularBuffer<IQuote>>();
    }
    
    public void AddQuote(string symbol, IQuote quote)
    {
        var buffer = _quoteBuffers.GetOrAdd(symbol, _ => new CircularBuffer<IQuote>(_bufferSize));
        buffer.Add(quote);
    }
}
```

## 🚨 Error Handling and Resilience

### Connection Recovery
```csharp
private async Task ConnectWithRetryAsync(CancellationToken cancellationToken)
{
    var attempts = 0;
    var maxAttempts = _config.MaxReconnectAttempts;
    
    while (attempts < maxAttempts && !cancellationToken.IsCancellationRequested)
    {
        try
        {
            await _alpacaStreamingClient.ConnectAsync(cancellationToken);
            _connectionStatus = ConnectionStatus.Connected;
            _logger.LogInformation("Streaming connection established");
            return;
        }
        catch (Exception ex)
        {
            attempts++;
            _logger.LogWarning(ex, "Connection attempt {Attempt}/{MaxAttempts} failed", 
                attempts, maxAttempts);
            
            if (attempts < maxAttempts)
            {
                var delay = TimeSpan.FromMilliseconds(_config.ReconnectDelay * attempts);
                await Task.Delay(delay, cancellationToken);
            }
        }
    }
    
    _connectionStatus = ConnectionStatus.Error;
    _logger.LogError("Failed to establish streaming connection after {Attempts} attempts", attempts);
}
```

### Data Validation
```csharp
private bool ValidateStreamingData(StreamingQuoteEventArgs args)
{
    // Basic data validation
    if (string.IsNullOrEmpty(args.Symbol) || 
        args.Quote.BidPrice <= 0 || 
        args.Quote.AskPrice <= 0 ||
        args.Quote.BidPrice > args.Quote.AskPrice * 1.1m)  // Reasonable spread check
    {
        _logger.LogWarning("Invalid streaming data received for {Symbol}", args.Symbol);
        return false;
    }
    
    // Timestamp validation
    var age = DateTime.UtcNow - args.Timestamp;
    if (age > TimeSpan.FromMinutes(5))
    {
        _logger.LogWarning("Stale streaming data received for {Symbol}: {Age}", 
            args.Symbol, age);
        return false;
    }
    
    return true;
}
```

## 📈 Monitoring and Metrics

### Streaming Metrics
```csharp
public class StreamingMetrics
{
    public long QuotesReceived { get; set; }
    public long BarsReceived { get; set; }
    public long IndexUpdatesReceived { get; set; }
    public TimeSpan AverageLatency { get; set; }
    public int ConnectionDrops { get; set; }
    public DateTime LastDataReceived { get; set; }
}

// Metrics collection
private void UpdateMetrics(StreamingQuoteEventArgs args)
{
    Interlocked.Increment(ref _metrics.QuotesReceived);
    _metrics.LastDataReceived = DateTime.UtcNow;
    
    var latency = DateTime.UtcNow - args.Timestamp;
    UpdateAverageLatency(latency);
}
```

### Health Dashboard Integration
```csharp
// Export metrics for monitoring dashboard
public async Task<StreamingHealthReport> GetHealthReportAsync()
{
    return new StreamingHealthReport
    {
        ConnectionStatus = _connectionStatus,
        Metrics = _metrics,
        ActiveSymbols = _subscribedSymbols.Count,
        LastHealthCheck = DateTime.UtcNow,
        Alerts = await GetActiveAlertsAsync()
    };
}
```

## 🔒 Security Considerations

### API Key Management
```bash
# Secure API key storage
APCA_API_KEY_ID=your_alpaca_key_id
APCA_API_SECRET_KEY=your_alpaca_secret_key
POLY_API_KEY=your_polygon_api_key

# Use environment-specific keys
APCA_API_ENV=paper  # or live
```

### Network Security
- **TLS/SSL**: All WebSocket connections use secure protocols
- **Authentication**: Proper API key authentication for all connections
- **Rate Limiting**: Built-in rate limiting to prevent API abuse

## 🎯 Best Practices

### Symbol Management
- **Active Symbols Only**: Stream only symbols with active positions or signals
- **Dynamic Subscription**: Add/remove symbols based on trading activity
- **Resource Management**: Limit total symbols to prevent resource exhaustion

### Data Processing
- **Asynchronous Processing**: Use async/await for all data processing
- **Batch Processing**: Process data in batches for efficiency
- **Error Isolation**: Isolate errors to prevent cascade failures

### Performance Monitoring
- **Latency Tracking**: Monitor data latency and connection health
- **Resource Usage**: Track memory and CPU usage during streaming
- **Alert Thresholds**: Set appropriate thresholds for automated alerts

This comprehensive guide provides all necessary information for implementing and managing real-time streaming in the SmaTrendFollower system.
