using FluentAssertions;
using SmaTrendFollower.Services;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Comprehensive tests for SystemTimeProvider
/// Tests time provider functionality, timezone handling, and time calculations
/// </summary>
public class SystemTimeProviderTests
{
    private readonly SystemTimeProvider _timeProvider;

    public SystemTimeProviderTests()
    {
        _timeProvider = new SystemTimeProvider();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void UtcNow_ShouldReturnCurrentUtcTime()
    {
        // Act
        var utcNow = _timeProvider.UtcNow;
        var systemUtcNow = DateTime.UtcNow;

        // Assert
        utcNow.Should().BeCloseTo(systemUtcNow, TimeSpan.FromSeconds(1));
        utcNow.Kind.Should().Be(DateTimeKind.Utc);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void Now_ShouldReturnCurrentLocalTime()
    {
        // Act
        var now = _timeProvider.UtcNow;
        var systemNow = DateTime.UtcNow;

        // Assert
        now.Should().BeCloseTo(systemNow, TimeSpan.FromSeconds(1));
        now.Kind.Should().Be(DateTimeKind.Utc);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void Today_ShouldReturnCurrentDate()
    {
        // Act
        var today = _timeProvider.UtcNow.Date;
        var systemToday = DateTime.UtcNow.Date;

        // Assert
        today.Should().Be(systemToday);
        today.TimeOfDay.Should().Be(TimeSpan.Zero);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void UtcNow_CalledMultipleTimes_ShouldReturnIncreasingValues()
    {
        // Act
        var time1 = _timeProvider.UtcNow;
        // Use a tight loop instead of Thread.Sleep for faster execution
        var time2 = _timeProvider.UtcNow;
        while (time2 <= time1)
        {
            time2 = _timeProvider.UtcNow;
        }

        // Assert
        time2.Should().BeAfter(time1);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void Now_CalledMultipleTimes_ShouldReturnIncreasingValues()
    {
        // Act
        var time1 = _timeProvider.UtcNow;
        // Use a tight loop instead of Thread.Sleep for faster execution
        var time2 = _timeProvider.UtcNow;
        while (time2 <= time1)
        {
            time2 = _timeProvider.UtcNow;
        }

        // Assert
        time2.Should().BeAfter(time1);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void UtcNow_ShouldBeConsistentWithSystemTime()
    {
        // Arrange
        var tolerance = TimeSpan.FromMilliseconds(100);

        // Act
        var providerTime = _timeProvider.UtcNow;
        var systemTime = DateTime.UtcNow;

        // Assert
        var difference = Math.Abs((providerTime - systemTime).TotalMilliseconds);
        difference.Should().BeLessThan(tolerance.TotalMilliseconds);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void Now_ShouldBeConsistentWithSystemTime()
    {
        // Arrange
        var tolerance = TimeSpan.FromMilliseconds(100);

        // Act
        var providerTime = _timeProvider.UtcNow;
        var systemTime = DateTime.UtcNow;

        // Assert
        var difference = Math.Abs((providerTime - systemTime).TotalMilliseconds);
        difference.Should().BeLessThan(tolerance.TotalMilliseconds);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void Today_ShouldBeConsistentWithSystemToday()
    {
        // Act
        var providerToday = _timeProvider.UtcNow.Date;
        var systemToday = DateTime.UtcNow.Date;

        // Assert
        providerToday.Should().Be(systemToday);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void UtcNow_ShouldHaveUtcKind()
    {
        // Act
        var utcTime = _timeProvider.UtcNow;

        // Assert
        utcTime.Kind.Should().Be(DateTimeKind.Utc);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void Now_ShouldHaveLocalKind()
    {
        // Act
        var localTime = _timeProvider.UtcNow;

        // Assert
        localTime.Kind.Should().Be(DateTimeKind.Utc);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void Today_ShouldHaveUtcKind()
    {
        // Act
        var today = _timeProvider.UtcNow.Date;

        // Assert
        // DateTime.Date preserves the Kind from the source DateTime
        today.Kind.Should().Be(DateTimeKind.Utc);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void UtcNow_And_Now_ShouldHaveCorrectTimezoneRelationship()
    {
        // Act
        var utcTime = _timeProvider.UtcNow;
        var localTime = _timeProvider.UtcNow.ToLocalTime();

        // Assert
        var utcAsLocal = utcTime.ToLocalTime();
        utcAsLocal.Should().BeCloseTo(localTime, TimeSpan.FromSeconds(1));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ConcurrentAccess_ShouldHandleSafely()
    {
        // Arrange
        var results = new List<DateTime>();
        var tasks = new List<Task>();

        // Act - Reduced from 20 to 5 concurrent tasks for faster execution
        for (int i = 0; i < 5; i++)
        {
            tasks.Add(Task.Run(() =>
            {
                lock (results)
                {
                    results.Add(_timeProvider.UtcNow);
                }
            }));
        }

        await Task.WhenAll(tasks); // Use async version instead of blocking WaitAll

        // Assert
        results.Should().HaveCount(5); // Updated to match the reduced task count
        results.Should().OnlyContain(dt => dt.Kind == DateTimeKind.Utc);

        // Times should be in ascending order (with some tolerance for concurrent execution)
        var sortedResults = results.OrderBy(dt => dt).ToList();
        var timeDifferences = sortedResults.Zip(sortedResults.Skip(1), (a, b) => b - a);
        timeDifferences.Should().OnlyContain(diff => diff >= TimeSpan.Zero);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void PerformanceTest_ShouldBeReasonablyFast()
    {
        // Arrange - Further reduced iterations for faster test execution
        const int iterations = 100; // Reduced from 1000 to 100
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        for (int i = 0; i < iterations; i++)
        {
            var _ = _timeProvider.UtcNow;
        }

        stopwatch.Stop();

        // Assert - More relaxed threshold for faster execution
        var averageTimePerCall = stopwatch.Elapsed.TotalMilliseconds / iterations;
        averageTimePerCall.Should().BeLessThan(5.0); // Less than 5ms per call (relaxed from 1ms)
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void Today_ShouldHaveZeroTimeComponent()
    {
        // Act
        var today = _timeProvider.UtcNow.Date;

        // Assert
        today.Hour.Should().Be(0);
        today.Minute.Should().Be(0);
        today.Second.Should().Be(0);
        today.Millisecond.Should().Be(0);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void UtcNow_ShouldBeWithinReasonableRange()
    {
        // Arrange
        var minExpectedTime = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var maxExpectedTime = new DateTime(2030, 12, 31, 23, 59, 59, DateTimeKind.Utc);

        // Act
        var currentTime = _timeProvider.UtcNow;

        // Assert
        currentTime.Should().BeAfter(minExpectedTime);
        currentTime.Should().BeBefore(maxExpectedTime);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void Now_ShouldBeWithinReasonableRange()
    {
        // Arrange
        var minExpectedTime = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Local);
        var maxExpectedTime = new DateTime(2030, 12, 31, 23, 59, 59, DateTimeKind.Local);

        // Act
        var currentTime = _timeProvider.UtcNow;

        // Assert
        currentTime.Should().BeAfter(minExpectedTime);
        currentTime.Should().BeBefore(maxExpectedTime);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void Today_ShouldBeWithinReasonableRange()
    {
        // Arrange
        var minExpectedDate = new DateTime(2024, 1, 1);
        var maxExpectedDate = new DateTime(2030, 12, 31);

        // Act
        var today = _timeProvider.UtcNow.Date;

        // Assert
        today.Should().BeOnOrAfter(minExpectedDate);
        today.Should().BeOnOrBefore(maxExpectedDate);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void MultipleInstances_ShouldReturnSimilarTimes()
    {
        // Arrange
        var provider1 = new SystemTimeProvider();
        var provider2 = new SystemTimeProvider();

        // Act
        var time1 = provider1.UtcNow;
        var time2 = provider2.UtcNow;

        // Assert
        time2.Should().BeCloseTo(time1, TimeSpan.FromMilliseconds(10));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void TimeProvider_ShouldImplementITimeProvider()
    {
        // Assert
        _timeProvider.Should().BeAssignableTo<ITimeProvider>();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void Precision_ShouldBeAtLeastMillisecondLevel()
    {
        // Act
        var time1 = _timeProvider.UtcNow;
        var time2 = _timeProvider.UtcNow;

        // Assert
        // Even if called immediately, the precision should be at least millisecond level
        var difference = time2 - time1;
        difference.Should().BeGreaterOrEqualTo(TimeSpan.Zero);
        
        // The difference should be measurable at millisecond precision
        var millisecondDifference = difference.TotalMilliseconds;
        millisecondDifference.Should().BeGreaterOrEqualTo(0);
    }
}
