using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using StackExchange.Redis;
using System.Collections.Concurrent;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time breakout signal service implementation
/// Combines quote + trade data for precise breakout detection
/// Entry only if last trade > prior high and bid >= signal price
/// </summary>
public sealed class RealTimeBreakoutSignal : IRealTimeBreakoutSignal, IDisposable
{
    private readonly ITickStreamService _tickStreamService;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly IMarketDataService _marketDataService;
    private readonly ILogger<RealTimeBreakoutSignal> _logger;
    
    private readonly ConcurrentDictionary<string, BreakoutTracker> _breakoutTrackers = new();
    private readonly HashSet<string> _monitoredSymbols = new();
    private readonly object _symbolsLock = new();
    
    private BreakoutSignalConfig _config;
    private BreakoutMonitorStatus _status = BreakoutMonitorStatus.Stopped;
    private bool _disposed;
    
    // Redis key patterns
    private const string BreakoutStatusKeyPattern = "breakout:status:{0}";
    private const string BreakoutHistoryKeyPattern = "breakout:history:{0}";
    private const string PriceHistoryKeyPattern = "breakout:prices:{0}";
    
    public event EventHandler<BreakoutSignalEventArgs>? BreakoutDetected;
    public event EventHandler<BreakdownSignalEventArgs>? BreakdownDetected;
    public event EventHandler<BreakoutInvalidatedEventArgs>? BreakoutInvalidated;
    
    public RealTimeBreakoutSignal(
        ITickStreamService tickStreamService,
        IOptimizedRedisConnectionService redisService,
        IMarketDataService marketDataService,
        IConfiguration configuration,
        ILogger<RealTimeBreakoutSignal> logger)
    {
        _tickStreamService = tickStreamService ?? throw new ArgumentNullException(nameof(tickStreamService));
        _redisService = redisService ?? throw new ArgumentNullException(nameof(redisService));
        _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // Load configuration
        _config = LoadConfiguration(configuration);
        
        // Subscribe to tick stream events
        _tickStreamService.TradeReceived += OnTradeReceived;
        _tickStreamService.QuoteReceived += OnQuoteReceived;
        
        _logger.LogInformation("RealTimeBreakoutSignal initialized with {LookbackMinutes}min lookback, {MinBreakout}% min breakout",
            _config.LookbackPeriodMinutes, _config.MinBreakoutPercent);
    }
    
    public async Task StartMonitoringAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(RealTimeBreakoutSignal));
            
        var symbolList = symbols.ToList();
        if (!symbolList.Any())
            return;
            
        try
        {
            _status = BreakoutMonitorStatus.Starting;
            _logger.LogInformation("Starting breakout monitoring for {Count} symbols", symbolList.Count);
            
            // Initialize breakout trackers
            foreach (var symbol in symbolList)
            {
                var tracker = new BreakoutTracker(symbol, _config, _logger);
                _breakoutTrackers.TryAdd(symbol, tracker);
                
                // Load historical price data for prior high/low calculation
                await InitializePriceHistoryAsync(symbol, tracker);
            }
            
            // Update monitored symbols
            lock (_symbolsLock)
            {
                foreach (var symbol in symbolList)
                {
                    _monitoredSymbols.Add(symbol);
                }
            }
            
            // Subscribe to tick stream
            await _tickStreamService.SubscribeAsync(symbolList, TickDataTypes.Trades | TickDataTypes.Quotes, cancellationToken);
            
            _status = BreakoutMonitorStatus.Active;
            _logger.LogInformation("Breakout monitoring started successfully for {Count} symbols", symbolList.Count);
        }
        catch (Exception ex)
        {
            _status = BreakoutMonitorStatus.Error;
            _logger.LogError(ex, "Error starting breakout monitoring");
            throw;
        }
    }
    
    public async Task StopMonitoringAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;
            
        try
        {
            _logger.LogInformation("Stopping breakout monitoring");
            
            // Clear monitored symbols
            lock (_symbolsLock)
            {
                _monitoredSymbols.Clear();
            }
            
            // Clear trackers
            _breakoutTrackers.Clear();
            
            _status = BreakoutMonitorStatus.Stopped;
            _logger.LogInformation("Breakout monitoring stopped");
        }
        catch (Exception ex)
        {
            _status = BreakoutMonitorStatus.Error;
            _logger.LogError(ex, "Error stopping breakout monitoring");
            throw;
        }
    }
    
    public async Task AddSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(RealTimeBreakoutSignal));
            
        var symbolList = symbols.ToList();
        if (!symbolList.Any())
            return;
            
        var newSymbols = new List<string>();
        
        lock (_symbolsLock)
        {
            foreach (var symbol in symbolList)
            {
                if (_monitoredSymbols.Add(symbol))
                {
                    newSymbols.Add(symbol);
                }
            }
        }
        
        if (newSymbols.Any())
        {
            foreach (var symbol in newSymbols)
            {
                var tracker = new BreakoutTracker(symbol, _config, _logger);
                _breakoutTrackers.TryAdd(symbol, tracker);
                
                await InitializePriceHistoryAsync(symbol, tracker);
            }
            
            if (_status == BreakoutMonitorStatus.Active)
            {
                await _tickStreamService.SubscribeAsync(newSymbols, TickDataTypes.Trades | TickDataTypes.Quotes, cancellationToken);
            }
            
            _logger.LogInformation("Added {Count} new symbols to breakout monitoring", newSymbols.Count);
        }
    }
    
    public async Task RemoveSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;
            
        var symbolList = symbols.ToList();
        if (!symbolList.Any())
            return;
            
        var removedSymbols = new List<string>();
        
        lock (_symbolsLock)
        {
            foreach (var symbol in symbolList)
            {
                if (_monitoredSymbols.Remove(symbol))
                {
                    removedSymbols.Add(symbol);
                }
            }
        }
        
        foreach (var symbol in removedSymbols)
        {
            _breakoutTrackers.TryRemove(symbol, out _);
        }
        
        if (removedSymbols.Any())
        {
            _logger.LogInformation("Removed {Count} symbols from breakout monitoring", removedSymbols.Count);
        }
    }
    
    public async Task<BreakoutStatus?> GetBreakoutStatusAsync(string symbol)
    {
        if (!_breakoutTrackers.TryGetValue(symbol, out var tracker))
            return null;
            
        try
        {
            // Try Redis cache first
            var database = await _redisService.GetDatabaseAsync();
            var cacheKey = string.Format(BreakoutStatusKeyPattern, symbol);
            var cachedData = await database.StringGetAsync(cacheKey);
            
            if (cachedData.HasValue)
            {
                var status = JsonSerializer.Deserialize<BreakoutStatus>(cachedData!);
                if (DateTime.UtcNow - status.LastUpdate < _config.CacheExpiry)
                {
                    return status;
                }
            }
            
            // Calculate current status
            return tracker.GetCurrentStatus();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting breakout status for {Symbol}", symbol);
            return null;
        }
    }
    
    public async Task<bool> IsInBreakoutAsync(string symbol)
    {
        var status = await GetBreakoutStatusAsync(symbol);
        return status?.Condition == BreakoutCondition.Triggered || status?.Condition == BreakoutCondition.Confirmed;
    }
    
    public async Task<decimal> GetSignalStrengthAsync(string symbol)
    {
        var status = await GetBreakoutStatusAsync(symbol);
        return status?.SignalStrength ?? 0;
    }
    
    public async Task<IEnumerable<BreakoutEvent>> GetBreakoutHistoryAsync(string symbol, int hours = 24)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var historyKey = string.Format(BreakoutHistoryKeyPattern, symbol);
            var historyData = await database.ListRangeAsync(historyKey, 0, hours * 12 - 1); // 12 entries per hour (5min intervals)
            
            var breakoutHistory = new List<BreakoutEvent>();
            foreach (var item in historyData)
            {
                if (item.HasValue)
                {
                    var breakoutEvent = JsonSerializer.Deserialize<BreakoutEvent>(item!);
                    breakoutHistory.Add(breakoutEvent);
                }
            }
            
            return breakoutHistory.OrderByDescending(e => e.Timestamp);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting breakout history for {Symbol}", symbol);
            return Enumerable.Empty<BreakoutEvent>();
        }
    }
    
    public async Task UpdateConfigurationAsync(BreakoutSignalConfig config)
    {
        _config = config;
        
        // Update all trackers with new config
        foreach (var tracker in _breakoutTrackers.Values)
        {
            tracker.UpdateConfig(config);
        }
        
        _logger.LogInformation("Breakout signal configuration updated");
    }
    
    public BreakoutMonitorStatus GetStatus() => _status;
    
    public IEnumerable<string> GetMonitoredSymbols()
    {
        lock (_symbolsLock)
        {
            return _monitoredSymbols.ToList();
        }
    }
    
    private BreakoutSignalConfig LoadConfiguration(IConfiguration configuration)
    {
        var section = configuration.GetSection("BreakoutSignal");
        
        return new BreakoutSignalConfig(
            LookbackPeriodMinutes: section.GetValue("LookbackPeriodMinutes", 60),
            MinBreakoutPercent: section.GetValue("MinBreakoutPercent", 0.5m),
            BidSupportThreshold: section.GetValue("BidSupportThreshold", 0.8m),
            MinVolumeMultiplier: section.GetValue("MinVolumeMultiplier", 2),
            RequireVolumeConfirmation: section.GetValue("RequireVolumeConfirmation", true),
            RequireBidSupport: section.GetValue("RequireBidSupport", true),
            SignalValidityPeriod: TimeSpan.FromMinutes(section.GetValue("SignalValidityMinutes", 5)),
            MinPriceThreshold: section.GetValue("MinPriceThreshold", 5.0m),
            CacheExpiry: TimeSpan.FromMinutes(section.GetValue("CacheExpiryMinutes", 2))
        );
    }
    
    private async Task InitializePriceHistoryAsync(string symbol, BreakoutTracker tracker)
    {
        try
        {
            // Get recent price history for prior high/low calculation
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddMinutes(-_config.LookbackPeriodMinutes);
            
            var bars = await _marketDataService.GetStockMinuteBarsAsync(symbol, startDate, endDate);
            if (bars.Items.Any())
            {
                tracker.InitializePriceHistory(bars.Items);
                _logger.LogDebug("Initialized price history for {Symbol} with {Count} bars", symbol, bars.Items.Count());
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error initializing price history for {Symbol}", symbol);
        }
    }
    
    private async void OnTradeReceived(object? sender, TradeTickEventArgs e)
    {
        if (_disposed || !_breakoutTrackers.TryGetValue(e.TradeTick.Symbol, out var tracker))
            return;

        try
        {
            var status = tracker.UpdateWithTrade(e.TradeTick.Price, e.TradeTick.Size, e.TradeTick.Timestamp);
            if (status != null)
            {
                await ProcessBreakoutUpdate(status);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing trade tick for breakout: {Symbol}", e.TradeTick.Symbol);
        }
    }

    private async void OnQuoteReceived(object? sender, QuoteTickEventArgs e)
    {
        if (_disposed || !_breakoutTrackers.TryGetValue(e.QuoteTick.Symbol, out var tracker))
            return;

        try
        {
            var status = tracker.UpdateWithQuote(e.QuoteTick.BidPrice, e.QuoteTick.AskPrice, e.QuoteTick.Timestamp);
            if (status != null)
            {
                await ProcessBreakoutUpdate(status);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing quote tick for breakout: {Symbol}", e.QuoteTick.Symbol);
        }
    }

    private async Task ProcessBreakoutUpdate(BreakoutStatus status)
    {
        try
        {
            // Cache status in Redis
            await CacheBreakoutStatusAsync(status);

            // Check for breakout signals
            if (status.Condition == BreakoutCondition.Triggered || status.Condition == BreakoutCondition.Confirmed)
            {
                if (status.CurrentPrice > status.BreakoutLevel)
                {
                    // Upward breakout
                    BreakoutDetected?.Invoke(this, new BreakoutSignalEventArgs
                    {
                        Symbol = status.Symbol,
                        TriggerPrice = status.CurrentPrice,
                        BreakoutLevel = status.BreakoutLevel,
                        BidPrice = status.BidPrice,
                        SignalStrength = status.SignalStrength,
                        Volume = status.CurrentVolume,
                        Timestamp = status.LastUpdate,
                        Status = status
                    });

                    await RecordBreakoutEventAsync(new BreakoutEvent(
                        Symbol: status.Symbol,
                        Type: BreakoutType.Upward,
                        TriggerPrice: status.CurrentPrice,
                        BreakoutLevel: status.BreakoutLevel,
                        SignalStrength: status.SignalStrength,
                        Volume: status.CurrentVolume,
                        Timestamp: status.LastUpdate,
                        Duration: TimeSpan.Zero
                    ));

                    _logger.LogInformation("Upward breakout detected for {Symbol}: {Price:F2} > {Level:F2} (strength: {Strength:F1}%)",
                        status.Symbol, status.CurrentPrice, status.BreakoutLevel, status.SignalStrength);
                }
                else if (status.CurrentPrice < status.BreakdownLevel)
                {
                    // Downward breakdown
                    BreakdownDetected?.Invoke(this, new BreakdownSignalEventArgs
                    {
                        Symbol = status.Symbol,
                        TriggerPrice = status.CurrentPrice,
                        BreakdownLevel = status.BreakdownLevel,
                        AskPrice = status.AskPrice,
                        SignalStrength = status.SignalStrength,
                        Volume = status.CurrentVolume,
                        Timestamp = status.LastUpdate,
                        Status = status
                    });

                    await RecordBreakoutEventAsync(new BreakoutEvent(
                        Symbol: status.Symbol,
                        Type: BreakoutType.Downward,
                        TriggerPrice: status.CurrentPrice,
                        BreakoutLevel: status.BreakdownLevel,
                        SignalStrength: status.SignalStrength,
                        Volume: status.CurrentVolume,
                        Timestamp: status.LastUpdate,
                        Duration: TimeSpan.Zero
                    ));

                    _logger.LogInformation("Downward breakdown detected for {Symbol}: {Price:F2} < {Level:F2} (strength: {Strength:F1}%)",
                        status.Symbol, status.CurrentPrice, status.BreakdownLevel, status.SignalStrength);
                }
            }
            else if (status.Condition == BreakoutCondition.Failed)
            {
                // Breakout invalidated
                BreakoutInvalidated?.Invoke(this, new BreakoutInvalidatedEventArgs
                {
                    Symbol = status.Symbol,
                    Reason = "Breakout conditions no longer met",
                    CurrentPrice = status.CurrentPrice,
                    Timestamp = status.LastUpdate
                });

                _logger.LogDebug("Breakout invalidated for {Symbol}: {Price:F2}", status.Symbol, status.CurrentPrice);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing breakout update for {Symbol}", status.Symbol);
        }
    }

    private async Task CacheBreakoutStatusAsync(BreakoutStatus status)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(BreakoutStatusKeyPattern, status.Symbol);
            var json = JsonSerializer.Serialize(status);
            await database.StringSetAsync(key, json, _config.CacheExpiry);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error caching breakout status for {Symbol}", status.Symbol);
        }
    }

    private async Task RecordBreakoutEventAsync(BreakoutEvent breakoutEvent)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var historyKey = string.Format(BreakoutHistoryKeyPattern, breakoutEvent.Symbol);
            var json = JsonSerializer.Serialize(breakoutEvent);

            await database.ListLeftPushAsync(historyKey, json);
            await database.ListTrimAsync(historyKey, 0, 288 - 1); // Keep 24 hours of 5-minute intervals
            await database.KeyExpireAsync(historyKey, TimeSpan.FromDays(1));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error recording breakout event for {Symbol}", breakoutEvent.Symbol);
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            _tickStreamService.TradeReceived -= OnTradeReceived;
            _tickStreamService.QuoteReceived -= OnQuoteReceived;

            _breakoutTrackers.Clear();

            lock (_symbolsLock)
            {
                _monitoredSymbols.Clear();
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error disposing RealTimeBreakoutSignal");
        }
    }
}
