using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time tick streaming service for market data processing
/// Provides live trade, quote, and aggregate data with Redis caching
/// </summary>
public interface ITickStreamService : IDisposable
{
    // === Events ===
    
    /// <summary>
    /// Fired when a trade tick is received
    /// </summary>
    event EventHandler<TradeTickEventArgs>? TradeReceived;
    
    /// <summary>
    /// Fired when a quote tick is received
    /// </summary>
    event EventHandler<QuoteTickEventArgs>? QuoteReceived;
    
    /// <summary>
    /// Fired when an aggregate (minute bar) is received
    /// </summary>
    event EventHandler<AggregateTickEventArgs>? AggregateReceived;
    
    /// <summary>
    /// Fired when connection status changes
    /// </summary>
    event EventHandler<TickStreamStatusEventArgs>? StatusChanged;
    
    // === Properties ===
    
    /// <summary>
    /// Current connection status
    /// </summary>
    TickStreamStatus Status { get; }
    
    /// <summary>
    /// List of currently subscribed symbols
    /// </summary>
    IReadOnlyList<string> SubscribedSymbols { get; }
    
    // === Connection Management ===
    
    /// <summary>
    /// Start the tick streaming service
    /// </summary>
    Task StartAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stop the tick streaming service
    /// </summary>
    Task StopAsync(CancellationToken cancellationToken = default);
    
    // === Subscription Management ===
    
    /// <summary>
    /// Subscribe to real-time data for specified symbols
    /// </summary>
    /// <param name="symbols">Stock symbols to subscribe to</param>
    /// <param name="dataTypes">Types of data to subscribe to (trades, quotes, aggregates)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SubscribeAsync(IEnumerable<string> symbols, TickDataTypes dataTypes, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Unsubscribe from real-time data for specified symbols
    /// </summary>
    /// <param name="symbols">Stock symbols to unsubscribe from</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task UnsubscribeAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Unsubscribe from all symbols
    /// </summary>
    Task UnsubscribeAllAsync(CancellationToken cancellationToken = default);
    
    // === Data Retrieval ===
    
    /// <summary>
    /// Get the latest trade tick for a symbol from cache
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <returns>Latest trade tick or null if not available</returns>
    Task<TradeTick?> GetLatestTradeAsync(string symbol);
    
    /// <summary>
    /// Get the latest quote tick for a symbol from cache
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <returns>Latest quote tick or null if not available</returns>
    Task<QuoteTick?> GetLatestQuoteAsync(string symbol);
    
    /// <summary>
    /// Get recent trade ticks for a symbol from cache
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <param name="count">Number of recent ticks to retrieve</param>
    /// <returns>Recent trade ticks ordered by timestamp descending</returns>
    Task<IReadOnlyList<TradeTick>> GetRecentTradesAsync(string symbol, int count = 100);
    
    /// <summary>
    /// Get recent quote ticks for a symbol from cache
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <param name="count">Number of recent ticks to retrieve</param>
    /// <returns>Recent quote ticks ordered by timestamp descending</returns>
    Task<IReadOnlyList<QuoteTick>> GetRecentQuotesAsync(string symbol, int count = 100);
    
    // === Real-time Analysis ===
    
    /// <summary>
    /// Check if current price is above 5-minute high for real-time signal confirmation
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <returns>True if current price > 5-min high</returns>
    Task<bool> IsAboveFiveMinuteHighAsync(string symbol);
    
    /// <summary>
    /// Calculate volume-weighted average price (VWAP) from recent trades
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <param name="minutes">Number of minutes to look back</param>
    /// <returns>VWAP or null if insufficient data</returns>
    Task<decimal?> CalculateVwapAsync(string symbol, int minutes = 5);
    
    /// <summary>
    /// Get current bid-ask spread for a symbol
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <returns>Bid-ask spread or null if no quote available</returns>
    Task<decimal?> GetBidAskSpreadAsync(string symbol);
}

/// <summary>
/// Tick stream connection status
/// </summary>
public enum TickStreamStatus
{
    Disconnected,
    Connecting,
    Connected,
    Subscribing,
    Active,
    Reconnecting,
    Error,
    Stopped
}

/// <summary>
/// Types of tick data to subscribe to
/// </summary>
[Flags]
public enum TickDataTypes
{
    None = 0,
    Trades = 1,
    Quotes = 2,
    Aggregates = 4,
    All = Trades | Quotes | Aggregates
}

/// <summary>
/// Event args for tick stream status changes
/// </summary>
public class TickStreamStatusEventArgs : EventArgs
{
    public TickStreamStatus Status { get; init; }
    public string? Message { get; init; }
    public Exception? Exception { get; init; }
}

/// <summary>
/// Event args for trade tick updates
/// </summary>
public class TradeTickEventArgs : EventArgs
{
    public required TradeTick TradeTick { get; init; }
}

/// <summary>
/// Event args for quote tick updates
/// </summary>
public class QuoteTickEventArgs : EventArgs
{
    public required QuoteTick QuoteTick { get; init; }
}

/// <summary>
/// Event args for aggregate tick updates
/// </summary>
public class AggregateTickEventArgs : EventArgs
{
    public required AggregateTick AggregateTick { get; init; }
}
