using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for WebSocket symbol subscription management
/// </summary>
public interface IWebSocketSymbolSubscriptionManager
{
    /// <summary>
    /// Subscribe to symbols via WebSocket
    /// </summary>
    Task SubscribeToSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);

    /// <summary>
    /// Unsubscribe from symbols
    /// </summary>
    Task UnsubscribeFromSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get currently subscribed symbols
    /// </summary>
    IEnumerable<string> GetSubscribedSymbols();

    /// <summary>
    /// Get subscription status
    /// </summary>
    Task<SubscriptionStatus> GetStatusAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Refresh subscriptions based on current universe candidates
    /// </summary>
    Task RefreshSubscriptionsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Background service that manages WebSocket subscriptions for universe candidates
/// Subscribes to filtered symbols by 9:25 AM ET for real-time trading
/// </summary>
public sealed class WebSocketSymbolSubscriptionManager : BackgroundService, IWebSocketSymbolSubscriptionManager, IDisposable
{
    private readonly IDailyUniverseRefreshService _universeRefreshService;
    private readonly IPolygonWebSocketClient _polygonWebSocket;
    private readonly ILogger<WebSocketSymbolSubscriptionManager> _logger;
    private readonly SubscriptionManagerConfig _config;
    private readonly Timer _subscriptionTimer;

    private readonly HashSet<string> _subscribedSymbols = new(StringComparer.OrdinalIgnoreCase);
    private readonly object _subscriptionLock = new();
    private DateTime _lastSubscriptionTime = DateTime.MinValue;
    private DateTime _nextSubscriptionTime = DateTime.MinValue;
    private bool _isSubscribing = false;

    public WebSocketSymbolSubscriptionManager(
        IDailyUniverseRefreshService universeRefreshService,
        IPolygonWebSocketClient polygonWebSocket,
        IConfiguration configuration,
        ILogger<WebSocketSymbolSubscriptionManager> logger)
    {
        _universeRefreshService = universeRefreshService ?? throw new ArgumentNullException(nameof(universeRefreshService));
        _polygonWebSocket = polygonWebSocket ?? throw new ArgumentNullException(nameof(polygonWebSocket));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // Load configuration
        _config = SubscriptionManagerConfig.Default;
        configuration.GetSection("SubscriptionManager").Bind(_config);

        // Calculate next subscription time
        _nextSubscriptionTime = CalculateNextSubscriptionTime();

        // Create timer for daily subscription
        var timeUntilNextSubscription = _nextSubscriptionTime - DateTime.UtcNow;
        if (timeUntilNextSubscription < TimeSpan.Zero)
        {
            timeUntilNextSubscription = TimeSpan.FromMinutes(1); // Start soon if we're past today's subscription time
        }

        _subscriptionTimer = new Timer(OnTimerElapsed, null, timeUntilNextSubscription, TimeSpan.FromDays(1));

        _logger.LogInformation("WebSocketSymbolSubscriptionManager initialized. Next subscription: {NextSubscription} UTC ({TimeUntil} from now)",
            _nextSubscriptionTime, timeUntilNextSubscription);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("WebSocketSymbolSubscriptionManager started");

        // Check if we need to subscribe on startup
        var shouldSubscribeOnStartup = await ShouldSubscribeOnStartupAsync(stoppingToken);
        if (shouldSubscribeOnStartup)
        {
            _logger.LogInformation("Performing startup symbol subscription");
            try
            {
                await RefreshSubscriptionsAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during startup symbol subscription");
            }
        }

        // Keep the service running
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken); // Check every minute
            }
            catch (OperationCanceledException)
            {
                break;
            }
        }

        _logger.LogInformation("WebSocketSymbolSubscriptionManager stopped");
    }

    public async Task SubscribeToSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        var symbolList = symbols.ToList();
        if (!symbolList.Any())
        {
            _logger.LogWarning("No symbols provided for subscription");
            return;
        }

        var stopwatch = Stopwatch.StartNew();
        _logger.LogInformation("Subscribing to {SymbolCount} symbols via WebSocket", symbolList.Count);

        try
        {
            // Ensure WebSocket is connected
            if (_polygonWebSocket.ConnectionStatus != PolygonConnectionStatus.Authenticated)
            {
                _logger.LogInformation("WebSocket not connected, establishing connection");
                await _polygonWebSocket.ConnectAsync(cancellationToken);
            }

            // Subscribe to equity updates (trades and quotes)
            await _polygonWebSocket.SubscribeToEquityUpdatesAsync(symbolList, cancellationToken);

            // Update subscribed symbols list
            lock (_subscriptionLock)
            {
                foreach (var symbol in symbolList)
                {
                    _subscribedSymbols.Add(symbol);
                }
            }

            stopwatch.Stop();
            _lastSubscriptionTime = DateTime.UtcNow;

            _logger.LogInformation("Successfully subscribed to {SymbolCount} symbols in {ElapsedMs}ms. Total subscribed: {TotalSubscribed}",
                symbolList.Count, stopwatch.ElapsedMilliseconds, _subscribedSymbols.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error subscribing to symbols via WebSocket");
            throw;
        }
    }

    public async Task UnsubscribeFromSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        var symbolList = symbols.ToList();
        if (!symbolList.Any())
        {
            return;
        }

        var stopwatch = Stopwatch.StartNew();
        _logger.LogInformation("Unsubscribing from {SymbolCount} symbols via WebSocket", symbolList.Count);

        try
        {
            // Unsubscribe from equity updates
            await _polygonWebSocket.UnsubscribeFromEquityUpdatesAsync(symbolList, cancellationToken);

            // Update subscribed symbols list
            lock (_subscriptionLock)
            {
                foreach (var symbol in symbolList)
                {
                    _subscribedSymbols.Remove(symbol);
                }
            }

            stopwatch.Stop();

            _logger.LogInformation("Successfully unsubscribed from {SymbolCount} symbols in {ElapsedMs}ms. Total subscribed: {TotalSubscribed}",
                symbolList.Count, stopwatch.ElapsedMilliseconds, _subscribedSymbols.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unsubscribing from symbols via WebSocket");
            throw;
        }
    }

    public IEnumerable<string> GetSubscribedSymbols()
    {
        lock (_subscriptionLock)
        {
            return _subscribedSymbols.ToList();
        }
    }

    public async Task<SubscriptionStatus> GetStatusAsync(CancellationToken cancellationToken = default)
    {
        var subscribedSymbols = GetSubscribedSymbols().ToList();
        var universeStatus = await _universeRefreshService.GetStatusAsync(cancellationToken);

        return new SubscriptionStatus
        {
            IsSubscribing = _isSubscribing,
            SubscribedSymbolCount = subscribedSymbols.Count,
            SubscribedSymbols = subscribedSymbols,
            LastSubscriptionTime = _lastSubscriptionTime,
            NextSubscriptionTime = _nextSubscriptionTime,
            WebSocketStatus = _polygonWebSocket.ConnectionStatus.ToString(),
            UniverseCandidateCount = universeStatus.CandidateCount,
            ServiceStatus = _isSubscribing ? "Subscribing" : "Idle"
        };
    }

    public async Task RefreshSubscriptionsAsync(CancellationToken cancellationToken = default)
    {
        if (_isSubscribing)
        {
            _logger.LogWarning("Subscription refresh already in progress, skipping");
            return;
        }

        _isSubscribing = true;
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting subscription refresh");

            // Get current universe candidates
            var candidates = await _universeRefreshService.GetCurrentCandidatesAsync(cancellationToken);
            if (candidates == null || !candidates.Candidates.Any())
            {
                _logger.LogWarning("No universe candidates available for subscription");
                return;
            }

            var candidateSymbols = candidates.Candidates.Select(c => c.Symbol).ToList();
            _logger.LogInformation("Retrieved {CandidateCount} universe candidates for subscription", candidateSymbols.Count);

            // Get currently subscribed symbols
            var currentlySubscribed = GetSubscribedSymbols().ToList();

            // Calculate symbols to add and remove
            var symbolsToAdd = candidateSymbols.Except(currentlySubscribed, StringComparer.OrdinalIgnoreCase).ToList();
            var symbolsToRemove = currentlySubscribed.Except(candidateSymbols, StringComparer.OrdinalIgnoreCase).ToList();

            _logger.LogInformation("Subscription changes: +{AddCount} symbols, -{RemoveCount} symbols",
                symbolsToAdd.Count, symbolsToRemove.Count);

            // Remove old subscriptions
            if (symbolsToRemove.Any())
            {
                await UnsubscribeFromSymbolsAsync(symbolsToRemove, cancellationToken);
            }

            // Add new subscriptions
            if (symbolsToAdd.Any())
            {
                await SubscribeToSymbolsAsync(symbolsToAdd, cancellationToken);
            }

            stopwatch.Stop();
            _nextSubscriptionTime = CalculateNextSubscriptionTime();

            _logger.LogInformation("Subscription refresh completed in {ElapsedMs}ms. Total subscribed: {TotalSubscribed}. Next refresh: {NextRefresh}",
                stopwatch.ElapsedMilliseconds, _subscribedSymbols.Count, _nextSubscriptionTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during subscription refresh");
            throw;
        }
        finally
        {
            _isSubscribing = false;
        }
    }

    private async Task<bool> ShouldSubscribeOnStartupAsync(CancellationToken cancellationToken)
    {
        // Check if we have valid universe candidates
        var candidates = await _universeRefreshService.GetCurrentCandidatesAsync(cancellationToken);
        if (candidates == null || !candidates.Candidates.Any())
        {
            _logger.LogInformation("No universe candidates available, skipping startup subscription");
            return false;
        }

        // Check if it's past the subscription time today
        var now = DateTime.UtcNow;
        var today = now.Date;
        var subscriptionTimeToday = today.Add(_config.SubscriptionTimeUtc);

        if (now >= subscriptionTimeToday)
        {
            _logger.LogInformation("Past today's subscription time and have candidates, performing startup subscription");
            return true;
        }

        _logger.LogInformation("Before today's subscription time, will wait for scheduled subscription");
        return false;
    }

    private DateTime CalculateNextSubscriptionTime()
    {
        var now = DateTime.UtcNow;
        var today = now.Date;
        var subscriptionTimeToday = today.Add(_config.SubscriptionTimeUtc);

        if (now < subscriptionTimeToday)
        {
            return subscriptionTimeToday; // Today's subscription time
        }
        else
        {
            return today.AddDays(1).Add(_config.SubscriptionTimeUtc); // Tomorrow's subscription time
        }
    }

    private async void OnTimerElapsed(object? state)
    {
        try
        {
            _logger.LogInformation("Daily subscription timer elapsed, starting subscription refresh");
            await RefreshSubscriptionsAsync(CancellationToken.None);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during scheduled subscription refresh");
        }
    }

    public new void Dispose()
    {
        _subscriptionTimer?.Dispose();
        base.Dispose();
    }
}

/// <summary>
/// Configuration for subscription manager
/// </summary>
public class SubscriptionManagerConfig
{
    /// <summary>
    /// Time of day to refresh subscriptions (UTC)
    /// </summary>
    public TimeSpan SubscriptionTimeUtc { get; set; } = new TimeSpan(13, 25, 0); // 9:25 AM ET = 13:25 UTC

    /// <summary>
    /// Maximum number of symbols to subscribe to
    /// </summary>
    public int MaxSubscriptions { get; set; } = 200;

    /// <summary>
    /// Batch size for subscription operations
    /// </summary>
    public int BatchSize { get; set; } = 50;

    /// <summary>
    /// Delay between subscription batches (milliseconds)
    /// </summary>
    public int DelayBetweenBatches { get; set; } = 1000;

    /// <summary>
    /// Default configuration
    /// </summary>
    public static SubscriptionManagerConfig Default => new()
    {
        SubscriptionTimeUtc = new TimeSpan(13, 25, 0), // 9:25 AM ET
        MaxSubscriptions = 200,
        BatchSize = 50,
        DelayBetweenBatches = 1000
    };
}

/// <summary>
/// Status information for subscription manager
/// </summary>
public class SubscriptionStatus
{
    public bool IsSubscribing { get; set; }
    public int SubscribedSymbolCount { get; set; }
    public List<string> SubscribedSymbols { get; set; } = new();
    public DateTime LastSubscriptionTime { get; set; }
    public DateTime NextSubscriptionTime { get; set; }
    public string WebSocketStatus { get; set; } = string.Empty;
    public int UniverseCandidateCount { get; set; }
    public string ServiceStatus { get; set; } = string.Empty;
}
