namespace SmaTrendFollower.Services;

/// <summary>
/// Service for initializing all database contexts and ensuring proper schema creation
/// </summary>
public interface IDatabaseInitializationService
{
    /// <summary>
    /// Initializes all database contexts and ensures schemas are created
    /// </summary>
    Task InitializeAllDatabasesAsync();

    /// <summary>
    /// Initializes the index cache database
    /// </summary>
    Task InitializeIndexCacheAsync();

    /// <summary>
    /// Initializes the stock bar cache database
    /// </summary>
    Task InitializeStockCacheAsync();

    /// <summary>
    /// Checks if all databases are properly initialized
    /// </summary>
    Task<bool> AreAllDatabasesInitializedAsync();
}
