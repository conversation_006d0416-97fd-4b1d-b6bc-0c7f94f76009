using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Tick bar builder service for reconstructing bars from tick data for custom intervals
/// Supports Renko, Range, Volume, and Time-based bars for alternate signal engines
/// Useful for high-frequency backtests and custom technical analysis
/// </summary>
public interface ITickBarBuilder : IDisposable
{
    // === Events ===
    
    /// <summary>
    /// Fired when a new custom bar is completed
    /// </summary>
    event EventHandler<CustomBarCompletedEventArgs>? BarCompleted;
    
    /// <summary>
    /// Fired when bar building parameters are updated
    /// </summary>
    event EventHandler<BarBuilderConfigUpdatedEventArgs>? ConfigurationUpdated;
    
    /// <summary>
    /// Fired when bar building encounters an error
    /// </summary>
    event EventHandler<BarBuilderErrorEventArgs>? ErrorOccurred;
    
    // === Core Methods ===
    
    /// <summary>
    /// Start building bars for specified symbols and bar types
    /// </summary>
    Task StartBuildingAsync(IEnumerable<string> symbols, IEnumerable<BarBuildingConfig> configs, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stop building bars
    /// </summary>
    Task StopBuildingAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Process a trade tick for bar building
    /// </summary>
    Task ProcessTradeTickAsync(TradeTick tick);
    
    /// <summary>
    /// Process a quote tick for bar building (for bid/ask analysis)
    /// </summary>
    Task ProcessQuoteTickAsync(QuoteTick tick);
    
    /// <summary>
    /// Get current incomplete bar for a symbol and bar type
    /// </summary>
    Task<CustomBar?> GetCurrentBarAsync(string symbol, CustomBarType barType);
    
    /// <summary>
    /// Get completed bars for a symbol and bar type
    /// </summary>
    Task<IEnumerable<CustomBar>> GetCompletedBarsAsync(string symbol, CustomBarType barType, int count = 100);
    
    /// <summary>
    /// Get bar building statistics for a symbol
    /// </summary>
    Task<BarBuildingStatistics?> GetBuildingStatisticsAsync(string symbol);
    
    /// <summary>
    /// Force completion of current bar (useful for end-of-session)
    /// </summary>
    Task ForceBarCompletionAsync(string symbol, CustomBarType barType, string reason = "Manual");
    
    /// <summary>
    /// Reset bar building for a symbol (clear current bars)
    /// </summary>
    Task ResetBarsAsync(string symbol, CustomBarType? barType = null);
    
    // === Configuration ===
    
    /// <summary>
    /// Update bar building configuration for a symbol
    /// </summary>
    Task UpdateConfigurationAsync(string symbol, BarBuildingConfig config);
    
    /// <summary>
    /// Get current configuration for a symbol
    /// </summary>
    Task<BarBuildingConfig?> GetConfigurationAsync(string symbol, CustomBarType barType);
    
    /// <summary>
    /// Get building status
    /// </summary>
    BarBuildingStatus GetStatus();
    
    /// <summary>
    /// Get list of symbols being processed
    /// </summary>
    IEnumerable<string> GetProcessedSymbols();
    
    /// <summary>
    /// Get supported bar types
    /// </summary>
    IEnumerable<CustomBarType> GetSupportedBarTypes();
}

/// <summary>
/// Bar building configuration for a specific bar type
/// </summary>
public record BarBuildingConfig(
    CustomBarType BarType,
    decimal Parameter, // Renko: brick size, Range: range size, Volume: volume threshold, Time: minutes
    bool EnableBidAskAnalysis = false,
    bool EnableVolumeWeighting = true,
    bool EnableGapHandling = true,
    decimal MinTickSize = 0.01m,
    TimeSpan MaxBarDuration = default, // Maximum time before forcing bar completion
    bool EnableRealTimeUpdates = true,
    string? CustomParameters = null
)
{
    public TimeSpan MaxBarDuration { get; init; } = MaxBarDuration == default ? TimeSpan.FromHours(4) : MaxBarDuration;
}

/// <summary>
/// Custom bar types supported by the builder
/// </summary>
public enum CustomBarType
{
    Renko,      // Fixed price movement bars
    Range,      // Fixed high-low range bars
    Volume,     // Fixed volume bars
    Tick,       // Fixed number of ticks
    Dollar,     // Fixed dollar volume bars
    Time        // Custom time intervals
}

/// <summary>
/// Custom bar data structure
/// </summary>
public record CustomBar(
    string Symbol,
    CustomBarType BarType,
    decimal Open,
    decimal High,
    decimal Low,
    decimal Close,
    long Volume,
    decimal DollarVolume,
    int TickCount,
    DateTime StartTime,
    DateTime EndTime,
    bool IsComplete,
    decimal Parameter, // The parameter used to build this bar
    BarMetadata Metadata
);

/// <summary>
/// Additional metadata for custom bars
/// </summary>
public record BarMetadata(
    decimal BidVolumeRatio, // Percentage of volume on bid
    decimal AskVolumeRatio, // Percentage of volume on ask
    decimal AverageSpread,
    decimal VWAP,
    int UptickCount,
    int DowntickCount,
    decimal MaxSpread,
    decimal MinSpread,
    string CompletionReason
);

/// <summary>
/// Bar building statistics for a symbol
/// </summary>
public record BarBuildingStatistics(
    string Symbol,
    int TotalBarsBuilt,
    int ActiveBarTypes,
    DateTime LastBarTime,
    DateTime LastTickProcessed,
    long TotalTicksProcessed,
    long TotalVolumeProcessed,
    decimal AverageBarDuration,
    Dictionary<CustomBarType, int> BarCountsByType,
    DateTime StatisticsTime
);

/// <summary>
/// Bar building status
/// </summary>
public enum BarBuildingStatus
{
    Stopped,
    Starting,
    Active,
    Error
}

// === Event Args ===

public class CustomBarCompletedEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required CustomBar CompletedBar { get; init; }
    public required DateTime CompletionTime { get; init; }
    public string? CompletionReason { get; init; }
}

public class BarBuilderConfigUpdatedEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required BarBuildingConfig OldConfig { get; init; }
    public required BarBuildingConfig NewConfig { get; init; }
    public required DateTime UpdateTime { get; init; }
}

public class BarBuilderErrorEventArgs : EventArgs
{
    public required string Symbol { get; init; }
    public required CustomBarType BarType { get; init; }
    public required Exception Error { get; init; }
    public required DateTime ErrorTime { get; init; }
    public string? Context { get; init; }
}

/// <summary>
/// Renko bar specific data
/// </summary>
public record RenkoBar(
    CustomBar BaseBar,
    decimal BrickSize,
    RenkoDirection Direction,
    int ConsecutiveBricks,
    decimal TrendStartPrice
) : CustomBar(
    BaseBar.Symbol,
    BaseBar.BarType,
    BaseBar.Open,
    BaseBar.High,
    BaseBar.Low,
    BaseBar.Close,
    BaseBar.Volume,
    BaseBar.DollarVolume,
    BaseBar.TickCount,
    BaseBar.StartTime,
    BaseBar.EndTime,
    BaseBar.IsComplete,
    BaseBar.Parameter,
    BaseBar.Metadata
);

/// <summary>
/// Range bar specific data
/// </summary>
public record RangeBar(
    CustomBar BaseBar,
    decimal RangeSize,
    decimal TrueRange,
    bool IsGapBar
) : CustomBar(
    BaseBar.Symbol,
    BaseBar.BarType,
    BaseBar.Open,
    BaseBar.High,
    BaseBar.Low,
    BaseBar.Close,
    BaseBar.Volume,
    BaseBar.DollarVolume,
    BaseBar.TickCount,
    BaseBar.StartTime,
    BaseBar.EndTime,
    BaseBar.IsComplete,
    BaseBar.Parameter,
    BaseBar.Metadata
);

/// <summary>
/// Volume bar specific data
/// </summary>
public record VolumeBar(
    CustomBar BaseBar,
    long VolumeThreshold,
    decimal AveragePrice,
    decimal VolumeWeightedPrice
) : CustomBar(
    BaseBar.Symbol,
    BaseBar.BarType,
    BaseBar.Open,
    BaseBar.High,
    BaseBar.Low,
    BaseBar.Close,
    BaseBar.Volume,
    BaseBar.DollarVolume,
    BaseBar.TickCount,
    BaseBar.StartTime,
    BaseBar.EndTime,
    BaseBar.IsComplete,
    BaseBar.Parameter,
    BaseBar.Metadata
);

/// <summary>
/// Renko direction enumeration
/// </summary>
public enum RenkoDirection
{
    Up,
    Down,
    Neutral
}

/// <summary>
/// Tick direction for bar building analysis
/// </summary>
public enum BarTickDirection
{
    Uptick,
    Downtick,
    Zero
}

/// <summary>
/// Bar completion trigger types
/// </summary>
public enum BarCompletionTrigger
{
    Parameter,      // Normal completion based on bar parameter
    Time,          // Time-based completion
    Manual,        // Manual force completion
    EndOfSession,  // End of trading session
    Gap,           // Price gap detected
    Error          // Error condition
}
